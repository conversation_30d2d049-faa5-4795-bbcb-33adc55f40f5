(()=>{var e={};e.id=6359,e.ids=[6359],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41510:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\employees\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\[id]\\edit\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62557:(e,t,r)=>{Promise.resolve().then(r.bind(r,72332))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72332:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(60687),i=r(75699),n=r(58261);let o=(0,r(82614).A)("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]]);var a=r(16189),l=r(43210),d=r(96342),c=r(48041),u=r(85726),p=r(49278),m=r(19599);let h=e=>{switch(e){case"Active":return"Active";case"Inactive":default:return"Inactive";case"On_Leave":return"On_Leave";case"Terminated":return"Terminated"}},f=e=>{switch(e){case"Active":return{status:"Active"};case"Inactive":default:return{status:"Inactive"};case"On_Leave":return{status:"On_Leave"};case"Terminated":return{status:"Terminated"}}};function y(){let e=(0,a.useRouter)(),t=(0,a.useParams)(),r=t?.id,{data:y,error:x,isLoading:v}=(0,m.uC)(r),b=(0,m.Db)(),g=async t=>{if(!y)return void p.Ok.error("Error","Employee data not available for update.");let r=f(t.status),s=Object.fromEntries(Object.entries({availability:t.availability??null,contactInfo:t.contactInfo,contactMobile:t.contactMobile??null,contactPhone:t.contactPhone??null,currentLocation:t.currentLocation??null,department:t.department??null,employeeId:t.employeeId,fullName:t.fullName??null,generalAssignments:t.generalAssignments??[],hireDate:t.hireDate??null,name:t.name,notes:t.notes??null,position:t.position??null,profileImageUrl:t.profileImageUrl??null,role:t.role,shiftSchedule:t.shiftSchedule??null,skills:t.skills??[],status:r.status}).filter(([,e])=>void 0!==e));try{let t={name:(await b.mutateAsync({data:s,id:y.id.toString()})).name};p.Ok.entityUpdated(t),e.push(`/employees/${y.employeeId}`)}catch(t){console.error("Failed to update employee:",t);let e=t.response?.data?.error||t.message||"An unexpected error occurred.";p.Ok.entityUpdateError(e)}},k=(0,l.useMemo)(()=>{if(y){let e=h(y.status);return y.contactPhone,y.contactMobile,{availability:y.availability||null,contactEmail:y.contactEmail||"",contactInfo:y.contactInfo||"",contactMobile:y.contactMobile||"",contactPhone:y.contactPhone||"",currentLocation:y.currentLocation||"",department:y.department||"",employeeId:y.employeeId,fullName:y.fullName||y.name||"",generalAssignments:y.generalAssignments||[],hireDate:y.hireDate?(0,i.GP)((0,n.H)(y.hireDate),"yyyy-MM-dd"):"",name:y.name||"",notes:y.notes||"",position:y.position||"",profileImageUrl:y.profileImageUrl||"",role:y.role||"other",shiftSchedule:y.shiftSchedule||"",skills:y.skills||[],status:e}}},[y]);return v||r&&!y&&!x?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{icon:o,title:"Loading Employee Data..."}),(0,s.jsx)(u.E,{className:"h-[700px] w-full rounded-lg bg-card"})]}):y&&k?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{description:"Modify the details for this employee.",icon:o,title:`Edit Employee: ${y.name}`}),(0,s.jsx)(d.N,{initialData:k,isEditing:!0,isLoading:b.isPending,onSubmit:g})]}):(0,s.jsx)("p",{children:"Employee not found or data could not be prepared."})}},74075:e=>{"use strict";e.exports=require("zlib")},75166:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["employees",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41510)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\[id]\\edit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/employees/[id]/edit/page",pathname:"/employees/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var s=r(60687),i=r(22482);function n({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},92413:(e,t,r)=>{Promise.resolve().then(r.bind(r,41510))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,211,1658,8390,2670,9275,6013,101,5825,9599,5785,6342],()=>r(75166));module.exports=s})();