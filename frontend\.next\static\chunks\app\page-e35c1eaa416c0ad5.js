(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{21968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>T});var i=s(95155),a=s(80659),r=s(77070),n=s(18046),c=s(51920),l=s(5263),d=s(8376),o=s(28328),u=s(40207),h=s(37648),m=s(31949),x=s(17607),v=s(3235),p=s(50286),f=s(6874),y=s.n(f),j=s(12115),g=s(26126),N=s(30285),b=s(66695),w=s(24944),A=s(40283),k=s(17841),R=s(83761),E=s(98691),P=s(61051),I=s(80937),K=s(54036);let V=e=>{let{title:t,value:s,description:n,icon:c,trend:l,href:d}=e,o=d?y():"div";return(0,i.jsx)(o,{href:d||"#",className:d?"block":"",children:(0,i.jsx)(b.Zp,{className:(0,K.cn)("transition-all duration-200",d&&"hover:shadow-md cursor-pointer"),children:(0,i.jsx)(b.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:t}),(0,i.jsx)("p",{className:"text-3xl font-bold",children:s}),n&&(0,i.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:n})]}),(0,i.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,i.jsx)("div",{className:"rounded-md bg-primary/10 p-2",children:(0,i.jsx)(c,{className:"size-4 text-primary"})}),l&&(0,i.jsxs)("div",{className:(0,K.cn)("flex items-center text-xs",l.isPositive?"text-green-600":"text-red-600"),children:[l.isPositive?(0,i.jsx)(a.A,{className:"size-3 mr-1"}):(0,i.jsx)(r.A,{className:"size-3 mr-1"}),Math.abs(l.value),"%"]})]})]})})})})},S=e=>{let{title:t,description:s,icon:a,href:r,variant:n="outline"}=e;return(0,i.jsx)(b.Zp,{className:"hover:shadow-md transition-all duration-200",children:(0,i.jsx)(b.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[(0,i.jsx)("div",{className:"rounded-md bg-primary/10 p-2",children:(0,i.jsx)(a,{className:"size-4 text-primary"})}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsx)("h4",{className:"text-sm font-medium",children:t}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:s}),(0,i.jsx)(N.$,{asChild:!0,variant:n,size:"sm",className:"mt-3 w-full",children:(0,i.jsx)(y(),{href:r,children:"Get Started"})})]})]})})})},q=()=>{var e;let{user:t,isInitialized:s,loading:a}=(0,A.useAuthContext)(),r=(0,j.useMemo)(()=>s&&!a&&!!t,[s,a,null==t?void 0:t.id]),{data:f=[]}=(0,I.T$)({enabled:r}),{data:K=[]}=(0,k.BD)({enabled:r}),{data:q=[]}=(0,P.si)({enabled:r}),{data:T=[]}=(0,R.nR)({enabled:r}),{data:Q=[]}=(0,E.fs)({enabled:r});if(!s||a)return(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,i.jsx)("p",{className:"mt-4 text-sm text-muted-foreground",children:"Initializing..."})]})});if(!t)return(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsxs)(b.Zp,{className:"w-full max-w-md",children:[(0,i.jsxs)(b.aR,{className:"text-center",children:[(0,i.jsx)(b.ZB,{children:"Welcome to WorkHub"}),(0,i.jsx)(b.BT,{children:"Please sign in to access your dashboard"})]}),(0,i.jsx)(b.Wu,{children:(0,i.jsx)(N.$,{asChild:!0,className:"w-full",children:(0,i.jsx)(y(),{href:"/login",children:"Sign In"})})})]})});let W={totalVehicles:f.length,activeDelegations:K.filter(e=>"In_Progress"===e.status).length,pendingTasks:q.filter(e=>"Assigned"===e.status||"In_Progress"===e.status).length,maintenancesDue:Q.filter(e=>Math.random()>.8).length,teamMembers:T.length},C=[{title:"Add New Vehicle",description:"Register a new asset to your fleet",icon:n.A,href:"/add-vehicle",variant:"default"},{title:"Schedule Maintenance",description:"Plan upcoming service appointments",icon:c.A,href:"/service-records"},{title:"View Analytics",description:"See detailed reports and insights",icon:l.A,href:"/reports"},{title:"Assign Task",description:"Create and delegate new tasks",icon:d.A,href:"/tasks"}];return(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Welcome back",(null==t||null==(e=t.user_metadata)?void 0:e.full_name)?", ".concat(t.user_metadata.full_name):"","!"]}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Here's what's happening with your operations today."})]}),(0,i.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,i.jsx)(V,{title:"Total Vehicles",value:W.totalVehicles,description:"Active fleet assets",icon:o.A,href:"/vehicles",trend:{value:5.2,isPositive:!0}}),(0,i.jsx)(V,{title:"Active Projects",value:W.activeDelegations,description:"In progress delegations",icon:u.A,href:"/delegations"}),(0,i.jsx)(V,{title:"Pending Tasks",value:W.pendingTasks,description:"Awaiting completion",icon:h.A,href:"/tasks",trend:{value:2.1,isPositive:!1}}),(0,i.jsx)(V,{title:"Maintenance Due",value:W.maintenancesDue,description:"Requires attention",icon:m.A,href:"/service-history"})]}),(0,i.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,i.jsx)("div",{className:"lg:col-span-2",children:(0,i.jsxs)(b.Zp,{children:[(0,i.jsxs)(b.aR,{className:"flex flex-row items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(b.ZB,{children:"Recent Activity"}),(0,i.jsx)(b.BT,{children:"Latest updates from your operations"})]}),(0,i.jsx)(N.$,{variant:"outline",size:"sm",asChild:!0,children:(0,i.jsxs)(y(),{href:"/activity",children:[(0,i.jsx)(x.A,{className:"size-4 mr-2"}),"View All"]})})]}),(0,i.jsx)(b.Wu,{children:(0,i.jsx)("div",{className:"space-y-4",children:[{id:"1",title:"Vehicle VIN-123 maintenance completed",description:"Oil change and tire rotation finished",timestamp:"2 hours ago",type:"maintenance"},{id:"2",title:"New task assigned: Fleet inspection",description:"Quarterly safety inspection due next week",timestamp:"4 hours ago",type:"task",priority:"high"},{id:"3",title:"Project Alpha milestone completed",description:"Phase 2 deliverables submitted",timestamp:"1 day ago",type:"delegation"}].map(e=>(0,i.jsxs)("div",{className:"flex items-start gap-3 p-3 rounded-lg bg-muted/30",children:[(0,i.jsxs)("div",{className:"rounded-full bg-primary/10 p-1",children:["maintenance"===e.type&&(0,i.jsx)(v.A,{className:"size-3 text-primary"}),"task"===e.type&&(0,i.jsx)(d.A,{className:"size-3 text-primary"}),"delegation"===e.type&&(0,i.jsx)(p.A,{className:"size-3 text-primary"}),"vehicle"===e.type&&(0,i.jsx)(o.A,{className:"size-3 text-primary"})]}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("p",{className:"text-sm font-medium",children:e.title}),e.priority&&(0,i.jsx)(g.E,{variant:"high"===e.priority?"destructive":"secondary",className:"text-xs",children:e.priority})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.timestamp})]})]},e.id))})})]})}),(0,i.jsx)("div",{children:(0,i.jsxs)(b.Zp,{children:[(0,i.jsxs)(b.aR,{children:[(0,i.jsx)(b.ZB,{children:"Quick Actions"}),(0,i.jsx)(b.BT,{children:"Common tasks and shortcuts"})]}),(0,i.jsx)(b.Wu,{children:(0,i.jsx)("div",{className:"space-y-3",children:C.map((e,t)=>(0,i.jsx)(S,{...e},t))})})]})})]}),(0,i.jsxs)(b.Zp,{children:[(0,i.jsxs)(b.aR,{children:[(0,i.jsx)(b.ZB,{children:"Fleet Performance"}),(0,i.jsx)(b.BT,{children:"Overview of fleet efficiency and maintenance status"})]}),(0,i.jsx)(b.Wu,{children:(0,i.jsxs)("div",{className:"grid gap-6 md:grid-cols-3",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Fleet Utilization"}),(0,i.jsx)("span",{className:"font-medium",children:"87%"})]}),(0,i.jsx)(w.k,{value:87,className:"h-2"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Maintenance Up-to-date"}),(0,i.jsx)("span",{className:"font-medium",children:"92%"})]}),(0,i.jsx)(w.k,{value:92,className:"h-2"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Task Completion Rate"}),(0,i.jsx)("span",{className:"font-medium",children:"78%"})]}),(0,i.jsx)(w.k,{value:78,className:"h-2"})]})]})})]})]})};function T(){return(0,i.jsx)(q,{})}"undefined"!=typeof document&&(document.title="Dashboard - WorkHub")},24944:(e,t,s)=>{"use strict";s.d(t,{k:()=>c});var i=s(95155),a=s(55863),r=s(12115),n=s(54036);let c=r.forwardRef((e,t)=>{let{className:s,value:r,...c}=e;return(0,i.jsx)(a.bL,{className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),ref:t,...c,children:(0,i.jsx)(a.C1,{className:"size-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});c.displayName=a.bL.displayName},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var i=s(95155),a=s(74466);s(12115);var r=s(54036);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function c(e){let{className:t,variant:s,...a}=e;return(0,i.jsx)("div",{className:(0,r.cn)(n({variant:s}),t),...a})}},80937:(e,t,s)=>{"use strict";s.d(t,{NS:()=>x,T$:()=>o,W_:()=>u,Y1:()=>h,lR:()=>m});var i=s(26715),a=s(5041),r=s(90111),n=s(42366),c=s(99605),l=s(75908);let d={all:["vehicles"],detail:e=>["vehicles",e]},o=e=>(0,r.GK)([...d.all],async()=>(await l.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>{var s;return(0,r.GK)([...d.detail(e)],()=>l.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(s=null==t?void 0:t.enabled)||s),staleTime:3e5,...t})},h=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,n.useNotifications)();return(0,a.n)({mutationFn:e=>{let t=c.M.toCreateRequest(e);return l.vehicleApiService.create(t)},onError:e=>{t("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),s('Vehicle "'.concat(t.licensePlate,'" has been created successfully!'))}})},m=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,n.useNotifications)();return(0,a.n)({mutationFn:e=>{let{data:t,id:s}=e,i=c.M.toUpdateRequest(t);return l.vehicleApiService.update(s,i)},onError:e=>{t("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),e.invalidateQueries({queryKey:d.detail(t.id)}),s('Vehicle "'.concat(t.licensePlate,'" has been updated successfully!'))}})},x=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,n.useNotifications)();return(0,a.n)({mutationFn:e=>l.vehicleApiService.delete(e),onError:e=>{t("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(t,i)=>{e.invalidateQueries({queryKey:d.all}),e.removeQueries({queryKey:d.detail(i)}),s("Vehicle has been deleted successfully!")}})}},92401:(e,t,s)=>{Promise.resolve().then(s.bind(s,21968))},98691:(e,t,s)=>{"use strict";s.d(t,{Ln:()=>p,WV:()=>h,fs:()=>m,kI:()=>v,xH:()=>x,xT:()=>f});var i=s(26715),a=s(5041),r=s(25982),n=s(90111),c=s(72248);let l={fromApi(e){let t={cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId};return e.vehicleMake||e.vehicleModel||e.vehicleYear?{...t,vehicleMake:e.vehicleMake||"Unknown",vehicleModel:e.vehicleModel||"Unknown",vehicleYear:e.vehicleYear||new Date().getFullYear(),licensePlate:e.licensePlate||null,employeeName:e.employeeName||null}:t},toApi:e=>e};class d extends r.v{async getById(e){return this.executeWithInfrastructure("".concat(this.endpoint,":").concat(e),async()=>{let t=await this.apiClient.get("".concat(this.endpoint,"/").concat(e));return this.transformer.fromApi?this.transformer.fromApi(t):t})}async updateRecord(e,t,s){return this.executeWithInfrastructure(null,async()=>{let{vehicleId:i,...a}=s,r=await this.apiClient.put("/vehicles/".concat(t,"/servicerecords/").concat(e),a);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":").concat(e))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(t,":"))),this.transformer.fromApi?this.transformer.fromApi(r):r})}async deleteRecord(e,t){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("/vehicles/".concat(t,"/servicerecords/").concat(e)),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":").concat(e))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(t,":")))})}async getVehicleServiceRecords(e){return this.executeWithInfrastructure("vehicles:".concat(e,":servicerecords"),async()=>await this.apiClient.get("/vehicles/".concat(e,"/servicerecords")))}async createVehicleServiceRecord(e,t){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/vehicles/".concat(e,"/servicerecords"),t);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(e,":"))),this.transformer.fromApi?this.transformer.fromApi(s):s})}async getAllEnriched(){return this.executeWithInfrastructure("".concat(this.endpoint,":enriched"),async()=>(await this.apiClient.get("".concat(this.endpoint,"/enriched"))).map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e))}constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/servicerecords",this.transformer=l}}let o=new d(c.uE),u="serviceRecords",h=(e,t)=>{var s;return(0,n.GK)([u,e],()=>o.getById(e),"serviceRecord",{enabled:null!=(s=null==t?void 0:t.enabled)?s:!!e,staleTime:3e5})},m=e=>{var t;return(0,n.GK)([u,"allEnriched"],()=>o.getAllEnriched(),"serviceRecord",{enabled:null==(t=null==e?void 0:e.enabled)||t,staleTime:3e5})},x=(e,t)=>{var s;return(0,n.GK)([u,"forVehicle",e],()=>o.getVehicleServiceRecords(e),"serviceRecord",{enabled:null==(s=null==t?void 0:t.enabled)||s,staleTime:3e5})},v=()=>{let e=(0,i.jE)();return(0,a.n)({mutationFn:async e=>{let{vehicleId:t}=e;return o.createVehicleServiceRecord(t,e)},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,"forVehicle",s.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",s.vehicleId]})}})},p=()=>{let e=(0,i.jE)();return(0,a.n)({mutationFn:async e=>{let{id:t,vehicleId:s,data:i}=e;return o.updateRecord(t,s,i)},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,s.id]}),e.invalidateQueries({queryKey:[u,"forVehicle",s.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",s.vehicleId]})}})},f=()=>{let e=(0,i.jE)();return(0,a.n)({mutationFn:async e=>{let{id:t,vehicleId:s}=e;return o.deleteRecord(t,s)},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,s.id]}),e.invalidateQueries({queryKey:[u,"forVehicle"]}),e.invalidateQueries({queryKey:["vehicle"]})}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,7047,3860,9664,6874,4977,4036,4767,8950,7515,7841,1051,8441,1684,7358],()=>t(92401)),_N_E=e.O()}]);