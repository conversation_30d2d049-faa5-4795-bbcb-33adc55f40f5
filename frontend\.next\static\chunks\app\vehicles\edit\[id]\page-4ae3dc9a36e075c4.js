(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6683],{18763:(e,s,i)=>{"use strict";i.d(s,{A:()=>a});let a=(0,i(40157).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},27767:(e,s,i)=>{Promise.resolve().then(i.bind(i,55201))},55201:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>u});var a=i(95155),t=i(28328),l=i(18763),c=i(35695);i(12115);var d=i(30285),n=i(95647),r=i(68856),o=i(30836),h=i(53712),m=i(80937);let u=()=>{let e=(0,c.useRouter)(),s=(0,c.useParams)(),{showEntityUpdated:i,showEntityUpdateError:u}=(0,h.O_)("vehicle"),x=null==s?void 0:s.id,v=x?Number(x):null,{data:p,error:j,isLoading:N}=(0,m.W_)(v),{error:g,isPending:f,mutateAsync:y}=(0,m.lR)(),b=async s=>{if(!v)return void u("Vehicle ID is missing. Cannot update.");try{let a={...s,initialOdometer:void 0===s.initialOdometer?(null==p?void 0:p.initialOdometer)||0:s.initialOdometer};await y({data:a,id:v});let t={make:s.make,model:s.model};i(t),e.push("/vehicles")}catch(e){console.error("Failed to update vehicle:",e),u(e.message||(null==g?void 0:g.message)||"Could not update the vehicle. Please check the details and try again.")}};return v?N?(0,a.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,a.jsx)(n.z,{description:"Loading vehicle details...",icon:l.A,title:"Edit Vehicle"}),(0,a.jsxs)("div",{className:"mx-auto max-w-2xl space-y-6",children:[(0,a.jsx)(r.E,{className:"h-10 w-1/3"}),(0,a.jsx)(r.E,{className:"h-12 w-full"}),(0,a.jsx)(r.E,{className:"h-12 w-full"}),(0,a.jsx)(r.E,{className:"h-12 w-full"}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,a.jsx)(r.E,{className:"h-10 w-24"}),(0,a.jsx)(r.E,{className:"h-10 w-24"})]})]})]}):j?(0,a.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,a.jsx)(n.z,{description:j.message||"Could not load vehicle data.",icon:t.A,title:"Error Loading Vehicle"}),(0,a.jsx)(d.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):p?(0,a.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,a.jsx)(n.z,{description:"Update details for ".concat((null==p?void 0:p.make)||"vehicle"," ").concat((null==p?void 0:p.model)||""),icon:l.A,title:"Edit Vehicle"}),g&&(0,a.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error updating: ",g.message]}),p&&(0,a.jsx)(o.x,{initialData:p?{...p,id:String(p.id)}:void 0,isEditing:!0,isLoading:f,onSubmit:b})]}):(0,a.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,a.jsx)(n.z,{description:"The requested vehicle could not be found.",icon:t.A,title:"Vehicle Not Found"}),(0,a.jsx)(d.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):(0,a.jsxs)("div",{className:"container mx-auto space-y-8 py-8 text-center",children:[(0,a.jsx)(n.z,{description:"Invalid Vehicle ID.",icon:t.A,title:"Error"}),(0,a.jsx)(d.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]})}},68856:(e,s,i)=>{"use strict";i.d(s,{E:()=>l});var a=i(95155),t=i(54036);function l(e){let{className:s,...i}=e;return(0,a.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",s),...i})}},95647:(e,s,i)=>{"use strict";i.d(s,{z:()=>t});var a=i(95155);function t(e){let{children:s,description:i,icon:t,title:l}=e;return(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"size-8 text-primary"}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:l})]}),i&&(0,a.jsx)("p",{className:"mt-1 text-muted-foreground",children:i})]}),s&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:s})]})}i(12115)}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,3860,9664,1263,5495,1859,5669,4629,4036,4767,8950,3712,8122,8441,1684,7358],()=>s(27767)),_N_E=e.O()}]);