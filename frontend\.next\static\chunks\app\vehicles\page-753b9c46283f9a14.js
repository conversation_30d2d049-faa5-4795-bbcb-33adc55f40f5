(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1602],{19018:(e,r,t)=>{"use strict";t.d(r,{Breadcrumb:()=>o,BreadcrumbItem:()=>d,BreadcrumbLink:()=>u,BreadcrumbList:()=>c,BreadcrumbPage:()=>m,BreadcrumbSeparator:()=>h});var a=t(95155),s=t(99708),i=t(73158),l=(t(3561),t(12115)),n=t(54036);let o=l.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("nav",{"aria-label":"breadcrumb",className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",t),ref:r,...s})});o.displayName="Breadcrumb";let c=l.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("ol",{className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",t),ref:r,...s})});c.displayName="BreadcrumbList";let d=l.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("li",{className:(0,n.cn)("inline-flex items-center gap-1.5",t),ref:r,...s})});d.displayName="BreadcrumbItem";let u=l.forwardRef((e,r)=>{let{asChild:t,className:i,...l}=e,o=t?s.DX:"a";return(0,a.jsx)(o,{className:(0,n.cn)("transition-colors hover:text-foreground",i),ref:r,...l})});u.displayName="BreadcrumbLink";let m=l.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,n.cn)("font-normal text-foreground",t),ref:r,role:"link",...s})});m.displayName="BreadcrumbPage";let h=e=>{let{children:r,className:t,...s}=e;return(0,a.jsx)("span",{"aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",t),role:"presentation",...s,children:null!=r?r:(0,a.jsx)(i.A,{className:"size-4"})})};h.displayName="BreadcrumbSeparator"},22346:(e,r,t)=>{"use strict";t.d(r,{w:()=>n});var a=t(95155),s=t(87489),i=t(12115),l=t(54036);let n=i.forwardRef((e,r)=>{let{className:t,decorative:i=!0,orientation:n="horizontal",...o}=e;return(0,a.jsx)(s.b,{className:(0,l.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",t),decorative:i,orientation:n,ref:r,...o})});n.displayName=s.b.displayName},42366:(e,r,t)=>{"use strict";t.r(r),t.d(r,{useNotifications:()=>i,useWorkHubNotifications:()=>l});var a=t(12115),s=t(96016);let i=()=>{let e=(0,s.C)(e=>e.addNotification),r=(0,s.C)(e=>e.removeNotification),t=(0,s.C)(e=>e.clearAllNotifications),i=(0,s.C)(e=>e.unreadNotificationCount),l=(0,a.useCallback)(r=>{e({message:r,type:"success"})},[e]),n=(0,a.useCallback)(r=>{e({message:r,type:"error"})},[e]),o=(0,a.useCallback)(r=>{e({message:r,type:"warning"})},[e]),c=(0,a.useCallback)(r=>{e({message:r,type:"info"})},[e]),d=(0,a.useCallback)((e,r,t)=>{e?l(r):n(t)},[l,n]),u=(0,a.useCallback)(function(t,a){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:a,type:t}),setTimeout(()=>{let e=s.C.getState().notifications.at(-1);e&&e.message===a&&r(e.id)},i)},[e,r]),m=(0,a.useCallback)(function(){var r;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:t,type:"info"}),null==(r=s.C.getState().notifications.at(-1))?void 0:r.id},[e]),h=(0,a.useCallback)((e,t,a)=>{r(e),t?l(a):n(a)},[r,l,n]);return{clearAllNotifications:t,removeNotification:r,showApiResult:d,showError:n,showInfo:c,showLoading:m,showSuccess:l,showTemporary:u,showWarning:o,unreadCount:i,updateLoadingNotification:h}},l=()=>{let{clearAllNotifications:e,removeNotification:r,showError:t,showInfo:l,showSuccess:n,showWarning:o,unreadCount:c}=i(),d=(0,a.useCallback)((e,r)=>{(0,s.C.getState().addNotification)({...r&&{actionUrl:r},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,a.useCallback)((e,r)=>{(0,s.C.getState().addNotification)({...r&&{actionUrl:r},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),m=(0,a.useCallback)((e,r)=>{(0,s.C.getState().addNotification)({...r&&{actionUrl:r},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:r,showDelegationUpdate:d,showEmployeeUpdate:(0,a.useCallback)((e,r)=>{(0,s.C.getState().addNotification)({...r&&{actionUrl:r},category:"employee",message:e,type:"employee-update"})},[]),showError:t,showInfo:l,showSuccess:n,showTaskAssigned:m,showVehicleMaintenance:u,showWarning:o,unreadCount:c}}},79463:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>L});var a=t(95155),s=t(28328),i=t(34301),l=t(67554),n=t(75074),o=t(6874),c=t.n(o),d=t(12115),u=t(88240),m=t(69321),h=t(3235),f=t(19968),x=t(66766),p=t(6560),g=t(66695),v=t(22346),b=t(44689);function N(e){var r,t;let{vehicle:s}=e,i=((null==(r=s.serviceHistory)?void 0:r.length)||0)>0,l=null!==s.initialOdometer,n=i?Math.max(s.initialOdometer||0,...(s.serviceHistory||[]).map(e=>e.odometer)):s.initialOdometer||0;return(0,a.jsxs)(g.Zp,{className:"flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",children:[(0,a.jsx)(g.aR,{className:"relative p-0",children:(0,a.jsx)("div",{className:"relative aspect-[16/10] w-full",children:(0,a.jsx)(x.default,{alt:"".concat(s.make," ").concat(s.model),className:"bg-muted object-cover","data-ai-hint":"luxury car",fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",src:(0,b.aI)(s.imageUrl,s.id)})})}),(0,a.jsxs)(g.Wu,{className:"flex grow flex-col p-5",children:[(0,a.jsxs)(g.ZB,{className:"mb-1 text-xl font-semibold text-primary",children:[s.make," ",s.model]}),(0,a.jsxs)(g.BT,{className:"mb-3 text-sm text-muted-foreground",children:[s.year," ",s.color&&"• ".concat(s.color)," ",s.licensePlate&&"• Plate: ".concat(s.licensePlate)]}),(0,a.jsx)(v.w,{className:"my-3 bg-border/50"}),(0,a.jsxs)("div",{className:"grow space-y-2.5 text-sm text-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Latest Odometer: "}),(0,a.jsx)("strong",{className:"font-semibold",children:i||l?"".concat(n.toLocaleString()," miles"):"Not recorded"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Services Logged: "}),(0,a.jsx)("strong",{className:"font-semibold",children:(null==(t=s.serviceHistory)?void 0:t.length)||0})]})]})]})]}),(0,a.jsx)(g.wL,{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,a.jsx)(p.r,{actionType:"tertiary",asChild:!0,className:"w-full",icon:(0,a.jsx)(f.A,{className:"size-4"}),children:(0,a.jsx)(c(),{href:"/vehicles/".concat(s.id),children:"Manage Vehicle"})})})]})}var y=t(53712),j=t(80937);let w=e=>{let{children:r}=e,{showEntityDeleted:t,showEntityDeletionError:a}=(0,y.O_)("vehicle"),{data:s,error:i,isFetching:l,isLoading:n,refetch:o}=(0,j.T$)(),{isPending:c,mutateAsync:d}=(0,j.NS)(),u=async e=>{if(globalThis.confirm("Are you sure you want to delete this vehicle?"))try{await d(e),t({make:"Vehicle",model:""})}catch(e){console.error("Error deleting vehicle:",e),a(e.message||"Could not delete the vehicle.")}};return r({error:(null==i?void 0:i.message)||null,fetchVehicles:o,handleDelete:u,isRefreshing:l,loading:n,vehicles:s||[]})};var k=t(89440),C=t(62523),S=t(77023),A=t(95647);let B=()=>{let[e,r]=(0,d.useState)("");return(0,a.jsx)(w,{children:t=>{let{error:o,fetchVehicles:u,handleDelete:m,isRefreshing:h,loading:f,vehicles:x}=t,g=(0,d.useMemo)(()=>{if(f||o)return[];let r=e.toLowerCase();return x.filter(e=>{var t,a,s;return e.make.toLowerCase().includes(r)||e.model.toLowerCase().includes(r)||e.year.toString().includes(r)||(null==(t=e.licensePlate)?void 0:t.toLowerCase().includes(r))||(null==(a=e.vin)?void 0:a.toLowerCase().includes(r))||(null==(s=e.ownerName)?void 0:s.toLowerCase().includes(r))})},[x,e,f,o]);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(k.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,a.jsx)(A.z,{description:"Manage, track, and gain insights into your vehicles.",icon:s.A,title:"My Vehicle Fleet",children:(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsx)(p.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(i.A,{className:"size-4"}),children:(0,a.jsx)(c(),{href:"/vehicles/new",children:"Add New Vehicle"})})})}),(0,a.jsxs)("div",{className:"relative mb-6 rounded-lg bg-card p-4 shadow",children:[h&&!f&&(0,a.jsxs)("div",{className:"absolute right-4 top-4 flex items-center text-xs text-muted-foreground",children:[(0,a.jsx)(l.A,{className:"mr-1 size-3 animate-spin"}),"Updating list..."]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.A,{className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(C.p,{className:"w-full pl-10",onChange:e=>r(e.target.value),placeholder:"Search vehicles (Make, Model, Year, VIN, Plate, Owner...)",type:"text",value:e})]})]}),(0,a.jsx)(S.gO,{data:g,emptyComponent:(0,a.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,a.jsx)(s.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:e?"No Vehicles Match Your Search":"Your Garage is Empty!"}),(0,a.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:e?"Try adjusting your search terms or add a new vehicle to your fleet.":"It looks like you haven't added any vehicles yet. Let's get your first one set up."}),!e&&(0,a.jsx)(p.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(i.A,{className:"size-4"}),size:"lg",children:(0,a.jsx)(c(),{href:"/vehicles/new",children:"Add Your First Vehicle"})})]}),error:o,isLoading:f,loadingComponent:(0,a.jsx)(S.jt,{count:3,variant:"card"}),onRetry:u,children:e=>(0,a.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:e.map(e=>(0,a.jsx)(N,{vehicle:e},e.id))})})]})}})};function L(){return(0,a.jsx)(u.A,{children:(0,a.jsx)(B,{})})}},80937:(e,r,t)=>{"use strict";t.d(r,{NS:()=>f,T$:()=>d,W_:()=>u,Y1:()=>m,lR:()=>h});var a=t(26715),s=t(5041),i=t(90111),l=t(42366),n=t(99605),o=t(75908);let c={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,i.GK)([...c.all],async()=>(await o.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,r)=>{var t;return(0,i.GK)([...c.detail(e)],()=>o.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(t=null==r?void 0:r.enabled)||t),staleTime:3e5,...r})},m=()=>{let e=(0,a.jE)(),{showError:r,showSuccess:t}=(0,l.useNotifications)();return(0,s.n)({mutationFn:e=>{let r=n.M.toCreateRequest(e);return o.vehicleApiService.create(r)},onError:e=>{r("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:r=>{e.invalidateQueries({queryKey:c.all}),t('Vehicle "'.concat(r.licensePlate,'" has been created successfully!'))}})},h=()=>{let e=(0,a.jE)(),{showError:r,showSuccess:t}=(0,l.useNotifications)();return(0,s.n)({mutationFn:e=>{let{data:r,id:t}=e,a=n.M.toUpdateRequest(r);return o.vehicleApiService.update(t,a)},onError:e=>{r("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:r=>{e.invalidateQueries({queryKey:c.all}),e.invalidateQueries({queryKey:c.detail(r.id)}),t('Vehicle "'.concat(r.licensePlate,'" has been updated successfully!'))}})},f=()=>{let e=(0,a.jE)(),{showError:r,showSuccess:t}=(0,l.useNotifications)();return(0,s.n)({mutationFn:e=>o.vehicleApiService.delete(e),onError:e=>{r("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(r,a)=>{e.invalidateQueries({queryKey:c.all}),e.removeQueries({queryKey:c.detail(a)}),t("Vehicle has been deleted successfully!")}})}},88240:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var a=t(95155),s=t(31949),i=t(67554),l=t(12115),n=t(55365),o=t(30285);class c extends l.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,r){this.setState({errorInfo:r}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",r.componentStack),this.props.onError&&this.props.onError(e,r)}render(){let{description:e="An unexpected error occurred.",resetLabel:r="Try Again",title:t="Something went wrong"}=this.props;if(this.state.hasError){var l;return this.props.fallback?this.props.fallback:(0,a.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,a.jsx)(s.A,{className:"mr-2 size-4"}),(0,a.jsx)(n.XL,{className:"text-lg font-semibold",children:t}),(0,a.jsxs)(n.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:(null==(l=this.state.error)?void 0:l.message)||e}),!1,(0,a.jsxs)(o.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),r]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let d=c},89440:(e,r,t)=>{"use strict";t.d(r,{AppBreadcrumb:()=>d});var a=t(95155),s=t(6874),i=t.n(s),l=t(35695),n=t(12115),o=t(19018),c=t(54036);function d(e){let{className:r,homeHref:t="/",homeLabel:s="Dashboard",showContainer:d=!0}=e,u=(0,l.usePathname)(),m=u?u.split("/").filter(Boolean):[],h=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},f=m.map((e,r)=>{let t="/"+m.slice(0,r+1).join("/"),s=r===m.length-1,l=h(e);return(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)(o.BreadcrumbItem,{children:s?(0,a.jsx)(o.BreadcrumbPage,{className:"font-medium text-foreground",children:l}):(0,a.jsx)(o.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:l})})}),!s&&(0,a.jsx)(o.BreadcrumbSeparator,{})]},t)}),x=(0,a.jsx)(o.Breadcrumb,{className:(0,c.cn)("text-sm",r),children:(0,a.jsxs)(o.BreadcrumbList,{className:"flex-wrap",children:[(0,a.jsx)(o.BreadcrumbItem,{children:(0,a.jsx)(o.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:s})})}),m.length>0&&(0,a.jsx)(o.BreadcrumbSeparator,{}),f]})});return d?(0,a.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"flex items-center",children:x})}):x}},94262:(e,r,t)=>{Promise.resolve().then(t.bind(t,79463))},96016:(e,r,t)=>{"use strict";t.d(r,{C:()=>i});var a=t(65453),s=t(46786);let i=(0,a.v)()((0,s.lt)((0,s.Zr)((e,r)=>({addNotification:r=>e(e=>({notifications:[...e.notifications,{...r,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:r=>e(e=>({notifications:e.notifications.map(e=>e.id===r?{...e,read:!0}:e)})),notifications:[],removeNotification:r=>e(e=>({notifications:e.notifications.filter(e=>e.id!==r)})),setTheme:r=>{e({currentTheme:r})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=r();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))}},e=>{var r=r=>e(e.s=r);e.O(0,[6476,7047,3860,9664,6874,6766,9106,4036,4767,8950,3712,2181,8441,1684,7358],()=>r(94262)),_N_E=e.O()}]);