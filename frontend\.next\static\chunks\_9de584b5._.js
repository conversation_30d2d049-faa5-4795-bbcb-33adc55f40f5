(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/security/SessionManager.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/lib/security/SessionManager.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/lib/api/index.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_api_e6c827dc._.js",
  "static/chunks/src_lib_api_index_ts_3b970cc6._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/api/index.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_api_services_1256fcf1._.js",
  "static/chunks/src_lib_api_services_apiServiceFactory_ts_3b970cc6._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript)");
    });
});
}}),
}]);