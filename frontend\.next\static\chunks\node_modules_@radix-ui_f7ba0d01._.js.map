{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-compose-refs/src/compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAQvB,SAAS,OAAU,GAAA,EAAqB,KAAA,EAAU;IAChD,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,IAAI,KAAK;IAClB,OAAA,IAAW,QAAQ,QAAQ,QAAQ,KAAA,GAAW;QAC5C,IAAI,OAAA,GAAU;IAChB;AACF;AAMA,SAAS,YAAA,GAAkB,IAAA,EAA8C;IACvE,OAAO,CAAC,SAAS;QACf,IAAI,aAAa;QACjB,MAAM,WAAW,KAAK,GAAA,CAAI,CAAC,QAAQ;YACjC,MAAM,UAAU,OAAO,KAAK,IAAI;YAChC,IAAI,CAAC,cAAc,OAAO,WAAW,YAAY;gBAC/C,aAAa;YACf;YACA,OAAO;QACT,CAAC;QAMD,IAAI,YAAY;YACd,OAAO,MAAM;gBACX,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;oBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;oBAC1B,IAAI,OAAO,WAAW,YAAY;wBAChC,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAA,CAAK,CAAC,CAAA,EAAG,IAAI;oBACtB;gBACF;YACF;QACF;IACF;AACF;AAMA,SAAS,gBAAA,GAAsB,IAAA,EAA8C;IAE3E,yKAAa,cAAA,EAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,mBAAmB;AAmCpB,SAkEG,YAAAA,WAlEH;;;;AAAA,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,0KAAa,aAAA,EAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,8KAAsB,WAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,IAAU,yKAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,qKAAa,WAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,WAAa,+KAAA,EAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,4KAAM,iBAAA,EAAe,UAAU,QACtB,6KAAA,EAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAG,SAAS,CAAA,KAAA,CAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,6KAAkB,cAAA,EAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QAEnC,QAAU,+KAAA,EAAe,QAAQ,GAAG;YAClC,MAAM,cAAc,cAAc,QAAQ;YAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,mKAAe,WAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM,gBAAe,gMAAA,EAAY,cAAc,WAAW,IAAI;YACtE;YACA,yKAAa,eAAA,EAAa,UAAUA,MAAK;QAC3C;QAEA,qKAAa,WAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,kKAAU,WAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC,CAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,yKAAAH,WAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,QACQ,kLAAA,EAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,MAAM,SAAS,eAAe,GAAG,IAAI;oBACrC,cAAc,GAAG,IAAI;oBACrB,OAAO;gBACT;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-context/src/create-context.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  const Provider: React.FC<ContextValueType & { children: React.ReactNode }> = (props) => {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  };\n\n  Provider.displayName = rootComponentName + 'Provider';\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    const Provider: React.FC<\n      ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    > = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    };\n\n    Provider.displayName = rootComponentName + 'Provider';\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "names": ["createContext", "useContext", "createScope", "nextScopes"], "mappings": ";;;;;AAAA,YAAY,WAAW;AAaZ;;;AAXX,SAASA,eACP,iBAAA,EACA,cAAA,EACA;IACA,MAAM,4KAAgB,gBAAA,EAA4C,cAAc;IAEhF,MAAM,WAAuE,CAAC,UAAU;QACtF,MAAM,EAAE,QAAA,EAAU,GAAG,QAAQ,CAAA,GAAI;QAGjC,MAAM,0KAAc,UAAA;sDAAQ,IAAM;qDAAS,OAAO,MAAA,CAAO,OAAO,CAAC;QACjE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,QAAQ,QAAA,EAAR;YAAiB;YAAe;QAAA,CAAS;IACnD;IAEA,SAAS,WAAA,GAAc,oBAAoB;IAE3C,SAASC,YAAW,YAAA,EAAsB;QACxC,MAAM,4KAAgB,aAAA,EAAW,OAAO;QACxC,IAAI,QAAS,CAAA,OAAO;QACpB,IAAI,mBAAmB,KAAA,EAAW,CAAA,OAAO;QAEzC,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,YAAY,CAAA,yBAAA,EAA4B,iBAAiB,CAAA,EAAA,CAAI;IACpF;IAEA,OAAO;QAAC;QAAUA,WAAU;KAAA;AAC9B;AAaA,SAAS,mBAAmB,SAAA,EAAmB,yBAAwC,CAAC,CAAA,EAAG;IACzF,IAAI,kBAAyB,CAAC,CAAA;IAM9B,SAASD,eACP,iBAAA,EACA,cAAA,EACA;QACA,MAAM,gLAAoB,gBAAA,EAA4C,cAAc;QACpF,MAAM,QAAQ,gBAAgB,MAAA;QAC9B,kBAAkB,CAAC;eAAG;YAAiB,cAAc;SAAA;QAErD,MAAM,WAEF,CAAC,UAAU;YACb,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,QAAQ,CAAA,GAAI;YACxC,MAAM,UAAU,OAAA,CAAQ,SAAS,CAAA,EAAA,CAAI,KAAK,CAAA,IAAK;YAG/C,MAAM,SAAc,2KAAA;6EAAQ,IAAM;4EAAS,OAAO,MAAA,CAAO,OAAO,CAAC;YACjE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,QAAQ,QAAA,EAAR;gBAAiB;gBAAe;YAAA,CAAS;QACnD;QAEA,SAAS,WAAA,GAAc,oBAAoB;QAE3C,SAASC,YAAW,YAAA,EAAsB,KAAA,EAA4C;YACpF,MAAM,UAAU,OAAA,CAAQ,SAAS,CAAA,EAAA,CAAI,KAAK,CAAA,IAAK;YAC/C,MAAM,WAAgB,8KAAA,EAAW,OAAO;YACxC,IAAI,QAAS,CAAA,OAAO;YACpB,IAAI,mBAAmB,KAAA,EAAW,CAAA,OAAO;YAEzC,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,YAAY,CAAA,yBAAA,EAA4B,iBAAiB,CAAA,EAAA,CAAI;QACpF;QAEA,OAAO;YAAC;YAAUA,WAAU;SAAA;IAC9B;IAMA,MAAM,cAA2B,MAAM;QACrC,MAAM,gBAAgB,gBAAgB,GAAA,CAAI,CAAC,mBAAmB;YAC5D,yKAAa,gBAAA,EAAc,cAAc;QAC3C,CAAC;QACD,OAAO,SAAS,SAAS,KAAA,EAAc;YACrC,MAAM,WAAW,OAAA,CAAQ,SAAS,CAAA,IAAK;YACvC,yKAAa,UAAA;mEACX,IAAA,CAAO;wBAAE,CAAC,CAAA,OAAA,EAAU,SAAS,EAAE,CAAA,EAAG;4BAAE,GAAG,KAAA;4BAAO,CAAC,SAAS,CAAA,EAAG;wBAAS;oBAAE,CAAA;kEACtE;gBAAC;gBAAO,QAAQ;aAAA;QAEpB;IACF;IAEA,YAAY,SAAA,GAAY;IACxB,OAAO;QAACD;QAAe,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;KAAA;AACrF;AAMA,SAAS,qBAAA,GAAwB,MAAA,EAAuB;IACtD,MAAM,YAAY,MAAA,CAAO,CAAC,CAAA;IAC1B,IAAI,OAAO,MAAA,KAAW,EAAG,CAAA,OAAO;IAEhC,MAAM,cAA2B,MAAM;QACrC,MAAM,aAAa,OAAO,GAAA,CAAI,CAACE,eAAAA,CAAiB;gBAC9C,UAAUA,aAAY;gBACtB,WAAWA,aAAY,SAAA;YACzB,CAAA,CAAE;QAEF,OAAO,SAAS,kBAAkB,cAAA,EAAgB;YAChD,MAAM,aAAa,WAAW,MAAA,CAAO,CAACC,aAAY,EAAE,QAAA,EAAU,SAAA,CAAU,CAAA,KAAM;gBAI5E,MAAM,aAAa,SAAS,cAAc;gBAC1C,MAAM,eAAe,UAAA,CAAW,CAAA,OAAA,EAAU,SAAS,EAAE,CAAA;gBACrD,OAAO;oBAAE,GAAGA,WAAAA;oBAAY,GAAG,YAAA;gBAAa;YAC1C,GAAG,CAAC,CAAC;YAEL,yKAAa,UAAA;8EAAQ,IAAA,CAAO;wBAAE,CAAC,CAAA,OAAA,EAAU,UAAU,SAAS,EAAE,CAAA,EAAG;oBAAW,CAAA;6EAAI;gBAAC,UAAU;aAAC;QAC9F;IACF;IAEA,YAAY,SAAA,GAAY,UAAU,SAAA;IAClC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/primitive/src/primitive.tsx"], "sourcesContent": ["function composeEventHandlers<E extends { defaultPrevented: boolean }>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,qBACP,oBAAA,EACA,eAAA,EACA,EAAE,2BAA2B,IAAA,CAAK,CAAA,GAAI,CAAC,CAAA,EACvC;IACA,OAAO,SAAS,YAAY,KAAA,EAAU;QACpC,uBAAuB,KAAK;QAE5B,IAAI,6BAA6B,SAAS,CAAC,MAAM,gBAAA,EAAkB;YACjE,OAAO,kBAAkB,KAAK;QAChC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-layout-effect/src/use-layout-effect.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "names": ["useLayoutEffect"], "mappings": ";;;;AAAA,YAAY,WAAW;;AASvB,IAAMA,mBAAkB,YAAY,yKAAiB,kBAAA,GAAkB,KAAO,CAAD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-effect-event/src/use-effect-event.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport * as React from 'react';\n\ntype AnyFunction = (...args: any[]) => any;\n\n// See https://github.com/webpack/webpack/issues/14814\nconst useReactEffectEvent = (React as any)[' useEffectEvent '.trim().toString()];\nconst useReactInsertionEffect = (React as any)[' useInsertionEffect '.trim().toString()];\n\n/**\n * Designed to approximate the behavior on `experimental_useEffectEvent` as best\n * as possible until its stable release, and back-fill it as a shim as needed.\n */\nexport function useEffectEvent<T extends AnyFunction>(callback?: T): T {\n  if (typeof useReactEffectEvent === 'function') {\n    return useReactEffectEvent(callback);\n  }\n\n  const ref = React.useRef<AnyFunction | undefined>(() => {\n    throw new Error('Cannot call an event handler while rendering.');\n  });\n  // See https://github.com/webpack/webpack/issues/14814\n  if (typeof useReactInsertionEffect === 'function') {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    useLayoutEffect(() => {\n      ref.current = callback;\n    });\n  }\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => ref.current?.(...args)) as T, []);\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,uBAAuB;AAChC,YAAY,WAAW;;;AAKvB,IAAM,sBAAuB,6JAAA,CAAc,mBAAmB,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA;AAC/E,IAAM,0BAA2B,6JAAA,CAAc,uBAAuB,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA;AAMhF,SAAS,eAAsC,QAAA,EAAiB;IACrE,IAAI,OAAO,wBAAwB,YAAY;QAC7C,OAAO,oBAAoB,QAAQ;IACrC;IAEA,MAAM,MAAY,8JAAA,MAAA;sCAAgC,MAAM;YACtD,MAAM,IAAI,MAAM,+CAA+C;QACjE,CAAC;;IAED,IAAI,OAAO,4BAA4B,YAAY;QACjD;sDAAwB,MAAM;gBAC5B,IAAI,OAAA,GAAU;YAChB,CAAC;;IACH,OAAO;QACL,CAAA,GAAA,sLAAA,CAAA,kBAAA;8CAAgB,MAAM;gBACpB,IAAI,OAAA,GAAU;YAChB,CAAC;;IACH;IAGA,OAAa,8JAAA,OAAA;kCAAQ;0CAAO,CAAA,GAAI,OAAS,IAAI,OAAA,GAAU,GAAG,IAAI;;iCAAS,CAAC,CAAC;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-controllable-state/src/use-controllable-state.tsx", "file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-controllable-state/src/use-controllable-state-reducer.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// Prevent bundlers from trying to optimize the import\nconst useInsertionEffect: typeof useLayoutEffect =\n  (React as any)[' useInsertionEffect '.trim().toString()] || useLayoutEffect;\n\ntype ChangeHandler<T> = (state: T) => void;\ntype SetStateFn<T> = React.Dispatch<React.SetStateAction<T>>;\n\ninterface UseControllableStateParams<T> {\n  prop?: T | undefined;\n  defaultProp: T;\n  onChange?: ChangeHandler<T>;\n  caller?: string;\n}\n\nexport function useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n  caller,\n}: UseControllableStateParams<T>): [T, SetStateFn<T>] {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange,\n  });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n\n  // OK to disable conditionally calling hooks here because they will always run\n  // consistently in the same environment. Bundlers should be able to remove the\n  // code block entirely in production.\n  /* eslint-disable react-hooks/rules-of-hooks */\n  if (process.env.NODE_ENV !== 'production') {\n    const isControlledRef = React.useRef(prop !== undefined);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? 'controlled' : 'uncontrolled';\n        const to = isControlled ? 'controlled' : 'uncontrolled';\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks */\n\n  const setValue = React.useCallback<SetStateFn<T>>(\n    (nextValue) => {\n      if (isControlled) {\n        const value = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value !== prop) {\n          onChangeRef.current?.(value);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n\n  return [value, setValue];\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>): [\n  Value: T,\n  setValue: React.Dispatch<React.SetStateAction<T>>,\n  OnChangeRef: React.RefObject<ChangeHandler<T> | undefined>,\n] {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n\n  return [value, setValue, onChangeRef];\n}\n\nfunction isFunction(value: unknown): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n", "import * as React from 'react';\nimport { useEffectEvent } from '@radix-ui/react-use-effect-event';\n\ntype ChangeHandler<T> = (state: T) => void;\n\ninterface UseControllableStateParams<T> {\n  prop: T | undefined;\n  defaultProp: T;\n  onChange: ChangeHandler<T> | undefined;\n  caller: string;\n}\n\ninterface AnyAction {\n  type: string;\n}\n\nconst SYNC_STATE = Symbol('RADIX:SYNC_STATE');\n\ninterface SyncStateAction<T> {\n  type: typeof SYNC_STATE;\n  state: T;\n}\n\nexport function useControllableStateReducer<T, S extends {}, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialState: S\n): [S & { state: T }, React.Dispatch<A>];\n\nexport function useControllableStateReducer<T, S extends {}, I, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialArg: I,\n  init: (i: I & { state: T }) => S\n): [S & { state: T }, React.Dispatch<A>];\n\nexport function useControllableStateReducer<T, S extends {}, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialArg: any,\n  init?: (i: any) => Omit<S, 'state'>\n): [S & { state: T }, React.Dispatch<A>] {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== undefined;\n\n  const onChange = useEffectEvent(onChangeProp);\n\n  // OK to disable conditionally calling hooks here because they will always run\n  // consistently in the same environment. Bundlers should be able to remove the\n  // code block entirely in production.\n  /* eslint-disable react-hooks/rules-of-hooks */\n  if (process.env.NODE_ENV !== 'production') {\n    const isControlledRef = React.useRef(controlledState !== undefined);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? 'controlled' : 'uncontrolled';\n        const to = isControlled ? 'controlled' : 'uncontrolled';\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks */\n\n  type InternalState = S & { state: T };\n  const args: [InternalState] = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    // @ts-expect-error\n    args.push(init);\n  }\n\n  const [internalState, dispatch] = React.useReducer(\n    (state: InternalState, action: A | SyncStateAction<T>): InternalState => {\n      if (action.type === SYNC_STATE) {\n        return { ...state, state: action.state };\n      }\n\n      const next = reducer(state, action);\n      if (isControlled && !Object.is(next.state, state.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React.useRef(uncontrolledState);\n  React.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n\n  const state = React.useMemo(() => {\n    const isControlled = controlledState !== undefined;\n    if (isControlled) {\n      return { ...internalState, state: controlledState };\n    }\n\n    return internalState;\n  }, [internalState, controlledState]);\n\n  React.useEffect(() => {\n    // Sync internal state for controlled components so that reducer is called\n    // with the correct state values\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n\n  return [state, dispatch as React.Dispatch<A>];\n}\n"], "names": ["value", "React", "state", "isControlled"], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;ACAhC,SAAS,sBAAsB;;;ADG/B,IAAM,qBACH,6JAAA,CAAc,uBAAuB,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA,2LAAK,kBAAA;AAYvD,SAAS,qBAAwB,EACtC,IAAA,EACA,WAAA,EACA,WAAW,KAAO,CAAA,AAAD,EACjB,MAAA,EACF,EAAsD;IACpD,MAAM,CAAC,kBAAkB,qBAAqB,WAAW,CAAA,GAAI,qBAAqB;QAChF;QACA;IACF,CAAC;IACD,MAAM,eAAe,SAAS,KAAA;IAC9B,MAAM,QAAQ,eAAe,OAAO;IAMpC,IAAI,oCAAuC;QACzC,MAAM,kBAAwB,8JAAA,MAAA,CAAO,SAAS,KAAA,CAAS;QACjD,8JAAA,SAAA;8CAAU,MAAM;gBACpB,MAAM,gBAAgB,gBAAgB,OAAA;gBACtC,IAAI,kBAAkB,cAAc;oBAClC,MAAM,OAAO,gBAAgB,eAAe;oBAC5C,MAAM,KAAK,eAAe,eAAe;oBACzC,QAAQ,IAAA,CACN,GAAG,MAAM,CAAA,kBAAA,EAAqB,IAAI,CAAA,IAAA,EAAO,EAAE,CAAA,0KAAA,CAAA;gBAE/C;gBACA,gBAAgB,OAAA,GAAU;YAC5B;6CAAG;YAAC;YAAc,MAAM;SAAC;IAC3B;IAGA,MAAM,WAAiB,8JAAA,WAAA;sDACrB,CAAC,cAAc;YACb,IAAI,cAAc;gBAChB,MAAMA,SAAQ,WAAW,SAAS,IAAI,UAAU,IAAI,IAAI;gBACxD,IAAIA,WAAU,MAAM;oBAClB,YAAY,OAAA,GAAUA,MAAK;gBAC7B;YACF,OAAO;gBACL,oBAAoB,SAAS;YAC/B;QACF;qDACA;QAAC;QAAc;QAAM;QAAqB,WAAW;KAAA;IAGvD,OAAO;QAAC;QAAO,QAAQ;KAAA;AACzB;AAEA,SAAS,qBAAwB,EAC/B,WAAA,EACA,QAAA,EACF,EAIE;IACA,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAU,8JAAA,QAAA,CAAS,WAAW;IACpD,MAAM,eAAqB,8JAAA,MAAA,CAAO,KAAK;IAEvC,MAAM,cAAoB,8JAAA,MAAA,CAAO,QAAQ;IACzC;mDAAmB,MAAM;YACvB,YAAY,OAAA,GAAU;QACxB;kDAAG;QAAC,QAAQ;KAAC;IAEP,8JAAA,SAAA;0CAAU,MAAM;YACpB,IAAI,aAAa,OAAA,KAAY,OAAO;gBAClC,YAAY,OAAA,GAAU,KAAK;gBAC3B,aAAa,OAAA,GAAU;YACzB;QACF;yCAAG;QAAC;QAAO,YAAY;KAAC;IAExB,OAAO;QAAC;QAAO;QAAU,WAAW;KAAA;AACtC;AAEA,SAAS,WAAW,KAAA,EAAkD;IACpE,OAAO,OAAO,UAAU;AAC1B;;;AC/EA,IAAM,aAAa,OAAO,kBAAkB;AAoBrC,SAAS,4BACd,OAAA,EACA,QAAA,EACA,UAAA,EACA,IAAA,EACuC;IACvC,MAAM,EAAE,MAAM,eAAA,EAAiB,WAAA,EAAa,UAAU,YAAA,EAAc,MAAA,CAAO,CAAA,GAAI;IAC/E,MAAM,eAAe,oBAAoB,KAAA;IAEzC,MAAM,qMAAW,iBAAA,EAAe,YAAY;IAM5C,IAAI,oCAAuC;QACzC,MAAM,kBAAwB,8JAAA,MAAA,CAAO,oBAAoB,KAAA,CAAS;QAC5D,8JAAA,SAAA;qDAAU,MAAM;gBACpB,MAAM,gBAAgB,gBAAgB,OAAA;gBACtC,IAAI,kBAAkB,cAAc;oBAClC,MAAM,OAAO,gBAAgB,eAAe;oBAC5C,MAAM,KAAK,eAAe,eAAe;oBACzC,QAAQ,IAAA,CACN,GAAG,MAAM,CAAA,kBAAA,EAAqB,IAAI,CAAA,IAAA,EAAO,EAAE,CAAA,0KAAA,CAAA;gBAE/C;gBACA,gBAAgB,OAAA,GAAU;YAC5B;oDAAG;YAAC;YAAc,MAAM;SAAC;IAC3B;IAIA,MAAM,OAAwB;QAAC;YAAE,GAAG,UAAA;YAAY,OAAO;QAAY,CAAC;KAAA;IACpE,IAAI,MAAM;QAER,KAAK,IAAA,CAAK,IAAI;IAChB;IAEA,MAAM,CAAC,eAAe,QAAQ,CAAA,GAAU,8JAAA,UAAA;kDACtC,CAACE,QAAsB,WAAkD;YACvE,IAAI,OAAO,IAAA,KAAS,YAAY;gBAC9B,OAAO;oBAAE,GAAGA,MAAAA;oBAAO,OAAO,OAAO,KAAA;gBAAM;YACzC;YAEA,MAAM,OAAO,QAAQA,QAAO,MAAM;YAClC,IAAI,gBAAgB,CAAC,OAAO,EAAA,CAAG,KAAK,KAAA,EAAOA,OAAM,KAAK,GAAG;gBACvD,SAAS,KAAK,KAAK;YACrB;YACA,OAAO;QACT;oDACG;IAGL,MAAM,oBAAoB,cAAc,KAAA;IACxC,MAAM,eAAqB,8JAAA,MAAA,CAAO,iBAAiB;IAC7C,8JAAA,SAAA;iDAAU,MAAM;YACpB,IAAI,aAAa,OAAA,KAAY,mBAAmB;gBAC9C,aAAa,OAAA,GAAU;gBACvB,IAAI,CAAC,cAAc;oBACjB,SAAS,iBAAiB;gBAC5B;YACF;QACF;gDAAG;QAAC;QAAU;QAAmB;QAAc,YAAY;KAAC;IAE5D,MAAM,QAAc,8JAAA,OAAA;sDAAQ,MAAM;YAChC,MAAMC,gBAAe,oBAAoB,KAAA;YACzC,IAAIA,eAAc;gBAChB,OAAO;oBAAE,GAAG,aAAA;oBAAe,OAAO;gBAAgB;YACpD;YAEA,OAAO;QACT;qDAAG;QAAC;QAAe,eAAe;KAAC;IAE7B,8JAAA,SAAA;iDAAU,MAAM;YAGpB,IAAI,gBAAgB,CAAC,OAAO,EAAA,CAAG,iBAAiB,cAAc,KAAK,GAAG;gBACpE,SAAS;oBAAE,MAAM;oBAAY,OAAO;gBAAgB,CAAC;YACvD;QACF;gDAAG;QAAC;QAAiB,cAAc,KAAA;QAAO,YAAY;KAAC;IAEvD,OAAO;QAAC;QAAO,QAA6B;KAAA;AAC9C", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-previous/src/use-previous.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;;AAEvB,SAAS,YAAe,KAAA,EAAU;IAChC,MAAM,wKAAY,SAAA,EAAO;QAAE;QAAO,UAAU;IAAM,CAAC;IAKnD,yKAAa,UAAA;+BAAQ,MAAM;YACzB,IAAI,IAAI,OAAA,CAAQ,KAAA,KAAU,OAAO;gBAC/B,IAAI,OAAA,CAAQ,QAAA,GAAW,IAAI,OAAA,CAAQ,KAAA;gBACnC,IAAI,OAAA,CAAQ,KAAA,GAAQ;YACtB;YACA,OAAO,IAAI,OAAA,CAAQ,QAAA;QACrB;8BAAG;QAAC,KAAK;KAAC;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-size/src/use-size.tsx"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n"], "names": [], "mappings": ";;;;AAEA,YAAY,WAAW;AACvB,SAAS,uBAAuB;;;AAEhC,SAAS,QAAQ,OAAA,EAA6B;IAC5C,MAAM,CAAC,MAAM,OAAO,CAAA,qKAAU,WAAA,EAAwD,KAAA,CAAS;IAE/F,CAAA,GAAA,sLAAA,CAAA,kBAAA;mCAAgB,MAAM;YACpB,IAAI,SAAS;gBAEX,QAAQ;oBAAE,OAAO,QAAQ,WAAA;oBAAa,QAAQ,QAAQ,YAAA;gBAAa,CAAC;gBAEpE,MAAM,iBAAiB,IAAI;+CAAe,CAAC,YAAY;wBACrD,IAAI,CAAC,MAAM,OAAA,CAAQ,OAAO,GAAG;4BAC3B;wBACF;wBAIA,IAAI,CAAC,QAAQ,MAAA,EAAQ;4BACnB;wBACF;wBAEA,MAAM,QAAQ,OAAA,CAAQ,CAAC,CAAA;wBACvB,IAAI;wBACJ,IAAI;wBAEJ,IAAI,mBAAmB,OAAO;4BAC5B,MAAM,kBAAkB,KAAA,CAAM,eAAe,CAAA;4BAE7C,MAAM,aAAa,MAAM,OAAA,CAAQ,eAAe,IAAI,eAAA,CAAgB,CAAC,CAAA,GAAI;4BACzE,QAAQ,UAAA,CAAW,YAAY,CAAA;4BAC/B,SAAS,UAAA,CAAW,WAAW,CAAA;wBACjC,OAAO;4BAGL,QAAQ,QAAQ,WAAA;4BAChB,SAAS,QAAQ,YAAA;wBACnB;wBAEA,QAAQ;4BAAE;4BAAO;wBAAO,CAAC;oBAC3B,CAAC;;gBAED,eAAe,OAAA,CAAQ,SAAS;oBAAE,KAAK;gBAAa,CAAC;gBAErD;+CAAO,IAAM,eAAe,SAAA,CAAU,OAAO;;YAC/C,OAAO;gBAGL,QAAQ,KAAA,CAAS;YACnB;QACF;kCAAG;QAAC,OAAO;KAAC;IAEZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-presence/src/presence.tsx", "file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-presence/src/use-state-machine.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './use-state-machine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;\n\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration | null>(null);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId: number;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied a frame after the\n          // animation ends, creating a flash of visible content. By setting the\n          // animation fill mode to \"forwards\", we force the node to keep the\n          // styles of the last keyframe, removing the flash.\n          //\n          // Previously we flushed the update via ReactDom.flushSync, but with\n          // exit animations this resulted in the node being removed from the\n          // DOM before the synthetic animationEnd event was dispatched, meaning\n          // user-provided event handlers would not be called.\n          // https://github.com/radix-ui/primitives/pull/1849\n          send('ANIMATION_END');\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = 'forwards';\n            // Reset the style after the node had time to unmount (for cases\n            // where the component chooses not to unmount). Doing this any\n            // sooner than `setTimeout` (e.g. with `requestAnimationFrame`)\n            // still causes a flash.\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === 'forwards') {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      stylesRef.current = node ? getComputedStyle(node) : null;\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles: CSSStyleDeclaration | null) {\n  return styles?.animationName || 'none';\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement<{ ref?: React.Ref<unknown> }>) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Presence;\n\nexport {\n  Presence,\n  //\n  Root,\n};\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "names": ["React", "node"], "mappings": ";;;;;AAAA,YAAYA,YAAW;AACvB,SAAS,uBAAuB;AAChC,SAAS,uBAAuB;;;;;;ACSzB,SAAS,gBACd,YAAA,EACA,OAAA,EACA;IACA,yKAAa,aAAA;sCAAW,CAAC,OAAwB,UAA4C;YAC3F,MAAM,YAAa,OAAA,CAAQ,KAAK,CAAA,CAAU,KAAK,CAAA;YAC/C,OAAO,aAAa;QACtB;qCAAG,YAAY;AACjB;;ADTA,IAAM,WAAoC,CAAC,UAAU;IACnD,MAAM,EAAE,OAAA,EAAS,QAAA,CAAS,CAAA,GAAI;IAC9B,MAAM,WAAW,YAAY,OAAO;IAEpC,MAAM,QACJ,OAAO,aAAa,aAChB,SAAS;QAAE,SAAS,SAAS,SAAA;IAAU,CAAC,kKAClC,WAAA,CAAS,IAAA,CAAK,QAAQ;IAGlC,MAAM,UAAM,iMAAA,EAAgB,SAAS,GAAA,EAAK,cAAc,KAAK,CAAC;IAC9D,MAAM,aAAa,OAAO,aAAa;IACvC,OAAO,cAAc,SAAS,SAAA,GAAkB,iLAAA,EAAa,OAAO;QAAE;IAAI,CAAC,IAAI;AACjF;AAEA,SAAS,WAAA,GAAc;AAMvB,SAAS,YAAY,OAAA,EAAkB;IACrC,MAAM,CAAC,MAAM,OAAO,CAAA,qKAAU,WAAA,CAAsB;IACpD,MAAM,gBAAkB,uKAAA,EAAmC,IAAI;IAC/D,MAAM,mLAAuB,SAAA,EAAO,OAAO;IAC3C,MAAM,wBAA6B,0KAAA,EAAe,MAAM;IACxD,MAAM,eAAe,UAAU,YAAY;IAC3C,MAAM,CAAC,OAAO,IAAI,CAAA,GAAI,gBAAgB,cAAc;QAClD,SAAS;YACP,SAAS;YACT,eAAe;QACjB;QACA,kBAAkB;YAChB,OAAO;YACP,eAAe;QACjB;QACA,WAAW;YACT,OAAO;QACT;IACF,CAAC;IAEK,8KAAA;iCAAU,MAAM;YACpB,MAAM,uBAAuB,iBAAiB,UAAU,OAAO;YAC/D,qBAAqB,OAAA,GAAU,UAAU,YAAY,uBAAuB;QAC9E;gCAAG;QAAC,KAAK;KAAC;IAEV,CAAA,GAAA,sLAAA,CAAA,kBAAA;uCAAgB,MAAM;YACpB,MAAM,SAAS,UAAU,OAAA;YACzB,MAAM,aAAa,eAAe,OAAA;YAClC,MAAM,oBAAoB,eAAe;YAEzC,IAAI,mBAAmB;gBACrB,MAAM,oBAAoB,qBAAqB,OAAA;gBAC/C,MAAM,uBAAuB,iBAAiB,MAAM;gBAEpD,IAAI,SAAS;oBACX,KAAK,OAAO;gBACd,OAAA,IAAW,yBAAyB,UAAU,QAAQ,YAAY,QAAQ;oBAGxE,KAAK,SAAS;gBAChB,OAAO;oBAOL,MAAM,cAAc,sBAAsB;oBAE1C,IAAI,cAAc,aAAa;wBAC7B,KAAK,eAAe;oBACtB,OAAO;wBACL,KAAK,SAAS;oBAChB;gBACF;gBAEA,eAAe,OAAA,GAAU;YAC3B;QACF;sCAAG;QAAC;QAAS,IAAI;KAAC;IAElB,CAAA,GAAA,sLAAA,CAAA,kBAAA;uCAAgB,MAAM;YACpB,IAAI,MAAM;gBACR,IAAI;gBACJ,MAAM,cAAc,KAAK,aAAA,CAAc,WAAA,IAAe;gBAMtD,MAAM;sEAAqB,CAAC,UAA0B;wBACpD,MAAM,uBAAuB,iBAAiB,UAAU,OAAO;wBAC/D,MAAM,qBAAqB,qBAAqB,QAAA,CAAS,MAAM,aAAa;wBAC5E,IAAI,MAAM,MAAA,KAAW,QAAQ,oBAAoB;4BAW/C,KAAK,eAAe;4BACpB,IAAI,CAAC,eAAe,OAAA,EAAS;gCAC3B,MAAM,kBAAkB,KAAK,KAAA,CAAM,iBAAA;gCACnC,KAAK,KAAA,CAAM,iBAAA,GAAoB;gCAK/B,YAAY,YAAY,UAAA;sFAAW,MAAM;wCACvC,IAAI,KAAK,KAAA,CAAM,iBAAA,KAAsB,YAAY;4CAC/C,KAAK,KAAA,CAAM,iBAAA,GAAoB;wCACjC;oCACF,CAAC;;4BACH;wBACF;oBACF;;gBACA,MAAM;wEAAuB,CAAC,UAA0B;wBACtD,IAAI,MAAM,MAAA,KAAW,MAAM;4BAEzB,qBAAqB,OAAA,GAAU,iBAAiB,UAAU,OAAO;wBACnE;oBACF;;gBACA,KAAK,gBAAA,CAAiB,kBAAkB,oBAAoB;gBAC5D,KAAK,gBAAA,CAAiB,mBAAmB,kBAAkB;gBAC3D,KAAK,gBAAA,CAAiB,gBAAgB,kBAAkB;gBACxD;mDAAO,MAAM;wBACX,YAAY,YAAA,CAAa,SAAS;wBAClC,KAAK,mBAAA,CAAoB,kBAAkB,oBAAoB;wBAC/D,KAAK,mBAAA,CAAoB,mBAAmB,kBAAkB;wBAC9D,KAAK,mBAAA,CAAoB,gBAAgB,kBAAkB;oBAC7D;;YACF,OAAO;gBAGL,KAAK,eAAe;YACtB;QACF;sCAAG;QAAC;QAAM,IAAI;KAAC;IAEf,OAAO;QACL,WAAW;YAAC;YAAW,kBAAkB;SAAA,CAAE,QAAA,CAAS,KAAK;QACzD,uKAAW,cAAA;uCAAY,CAACC,UAAsB;gBAC5C,UAAU,OAAA,GAAUA,QAAO,iBAAiBA,KAAI,IAAI;gBACpD,QAAQA,KAAI;YACd;sCAAG,CAAC,CAAC;IACP;AACF;AAIA,SAAS,iBAAiB,MAAA,EAAoC;IAC5D,OAAO,QAAQ,iBAAiB;AAClC;AAOA,SAAS,cAAc,OAAA,EAA2D;IAEhF,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAO,QAAQ,KAAA,CAAM,GAAA;IACvB;IAGA,OAAO,QAAQ,KAAA,CAAM,GAAA,IAAQ,QAAgB,GAAA;AAC/C;AAEA,IAAM,OAAO", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA4ChB;;;;;AA1CX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,+KAAO,aAAA,EAAW,CAAA,UAAA,EAAa,IAAI,EAAE;IAC3C,MAAM,yKAAa,aAAA,EAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,CAAA,UAAA,EAAa,IAAI,EAAA;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,0KAAS,YAAA,EAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-checkbox/src/checkbox.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst CHECKBOX_NAME = 'Checkbox';\n\ntype ScopedProps<P> = P & { __scopeCheckbox?: Scope };\nconst [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\n\ntype CheckedState = boolean | 'indeterminate';\n\ntype CheckboxContextValue<State extends CheckedState | boolean = CheckedState> = {\n  checked: State | boolean;\n  setChecked: React.Dispatch<React.SetStateAction<State | boolean>>;\n  disabled: boolean | undefined;\n  control: HTMLButtonElement | null;\n  setControl: React.Dispatch<React.SetStateAction<HTMLButtonElement | null>>;\n  name: string | undefined;\n  form: string | undefined;\n  value: string | number | readonly string[];\n  hasConsumerStoppedPropagationRef: React.RefObject<boolean>;\n  required: boolean | undefined;\n  defaultChecked: boolean | undefined;\n  isFormControl: boolean;\n  bubbleInput: HTMLInputElement | null;\n  setBubbleInput: React.Dispatch<React.SetStateAction<HTMLInputElement | null>>;\n};\n\nconst [CheckboxProviderImpl, useCheckboxContext] =\n  createCheckboxContext<CheckboxContextValue>(CHECKBOX_NAME);\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CheckboxProviderProps<State extends CheckedState = CheckedState> {\n  checked?: State | boolean;\n  defaultChecked?: State | boolean;\n  required?: boolean;\n  onCheckedChange?(checked: State | boolean): void;\n  name?: string;\n  form?: string;\n  disabled?: boolean;\n  value?: string | number | readonly string[];\n  children?: React.ReactNode;\n}\n\nfunction CheckboxProvider<State extends CheckedState = CheckedState>(\n  props: ScopedProps<CheckboxProviderProps<State>>\n) {\n  const {\n    __scopeCheckbox,\n    checked: checkedProp,\n    children,\n    defaultChecked,\n    disabled,\n    form,\n    name,\n    onCheckedChange,\n    required,\n    value = 'on',\n    // @ts-expect-error\n    internal_do_not_use_render,\n  } = props;\n\n  const [checked, setChecked] = useControllableState({\n    prop: checkedProp,\n    defaultProp: defaultChecked ?? false,\n    onChange: onCheckedChange,\n    caller: CHECKBOX_NAME,\n  });\n  const [control, setControl] = React.useState<HTMLButtonElement | null>(null);\n  const [bubbleInput, setBubbleInput] = React.useState<HTMLInputElement | null>(null);\n  const hasConsumerStoppedPropagationRef = React.useRef(false);\n  const isFormControl = control\n    ? !!form || !!control.closest('form')\n    : // We set this to true by default so that events bubble to forms without JS (SSR)\n      true;\n\n  const context: CheckboxContextValue<State> = {\n    checked: checked,\n    disabled: disabled,\n    setChecked: setChecked,\n    control: control,\n    setControl: setControl,\n    name: name,\n    form: form,\n    value: value,\n    hasConsumerStoppedPropagationRef: hasConsumerStoppedPropagationRef,\n    required: required,\n    defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n    isFormControl: isFormControl,\n    bubbleInput,\n    setBubbleInput,\n  };\n\n  return (\n    <CheckboxProviderImpl\n      scope={__scopeCheckbox}\n      {...(context as unknown as CheckboxContextValue<CheckedState>)}\n    >\n      {isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children}\n    </CheckboxProviderImpl>\n  );\n}\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CheckboxTrigger';\n\ninterface CheckboxTriggerProps\n  extends Omit<\n    React.ComponentPropsWithoutRef<typeof Primitive.button>,\n    keyof CheckboxProviderProps\n  > {\n  children?: React.ReactNode;\n}\n\nconst CheckboxTrigger = React.forwardRef<HTMLButtonElement, CheckboxTriggerProps>(\n  (\n    { __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }: ScopedProps<CheckboxTriggerProps>,\n    forwardedRef\n  ) => {\n    const {\n      control,\n      value,\n      disabled,\n      checked,\n      required,\n      setControl,\n      setChecked,\n      hasConsumerStoppedPropagationRef,\n      isFormControl,\n      bubbleInput,\n    } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = useComposedRefs(forwardedRef, setControl);\n\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = control?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [control, setChecked]);\n\n    return (\n      <Primitive.button\n        type=\"button\"\n        role=\"checkbox\"\n        aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n        aria-required={required}\n        data-state={getState(checked)}\n        data-disabled={disabled ? '' : undefined}\n        disabled={disabled}\n        value={value}\n        {...checkboxProps}\n        ref={composedRefs}\n        onKeyDown={composeEventHandlers(onKeyDown, (event) => {\n          // According to WAI ARIA, Checkboxes don't activate on enter keypress\n          if (event.key === 'Enter') event.preventDefault();\n        })}\n        onClick={composeEventHandlers(onClick, (event) => {\n          setChecked((prevChecked) => (isIndeterminate(prevChecked) ? true : !prevChecked));\n          if (bubbleInput && isFormControl) {\n            hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n            // if checkbox has a bubble input and is a form control, stop\n            // propagation from the button so that we only propagate one click\n            // event (from the input). We propagate changes from an input so\n            // that native form validation works and form events reflect\n            // checkbox updates.\n            if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n          }\n        })}\n      />\n    );\n  }\n);\n\nCheckboxTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Checkbox\n * -----------------------------------------------------------------------------------------------*/\n\ntype CheckboxElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CheckboxProps extends Omit<PrimitiveButtonProps, 'checked' | 'defaultChecked'> {\n  checked?: CheckedState;\n  defaultChecked?: CheckedState;\n  required?: boolean;\n  onCheckedChange?(checked: CheckedState): void;\n}\n\nconst Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>(\n  (props: ScopedProps<CheckboxProps>, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      value,\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n\n    return (\n      <CheckboxProvider\n        __scopeCheckbox={__scopeCheckbox}\n        checked={checked}\n        defaultChecked={defaultChecked}\n        disabled={disabled}\n        required={required}\n        onCheckedChange={onCheckedChange}\n        name={name}\n        form={form}\n        value={value}\n        // @ts-expect-error\n        internal_do_not_use_render={({ isFormControl }: CheckboxContextValue) => (\n          <>\n            <CheckboxTrigger\n              {...checkboxProps}\n              ref={forwardedRef}\n              // @ts-expect-error\n              __scopeCheckbox={__scopeCheckbox}\n            />\n            {isFormControl && (\n              <CheckboxBubbleInput\n                // @ts-expect-error\n                __scopeCheckbox={__scopeCheckbox}\n              />\n            )}\n          </>\n        )}\n      />\n    );\n  }\n);\n\nCheckbox.displayName = CHECKBOX_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'CheckboxIndicator';\n\ntype CheckboxIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface CheckboxIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CheckboxIndicator = React.forwardRef<CheckboxIndicatorElement, CheckboxIndicatorProps>(\n  (props: ScopedProps<CheckboxIndicatorProps>, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return (\n      <Presence\n        present={forceMount || isIndeterminate(context.checked) || context.checked === true}\n      >\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n          style={{ pointerEvents: 'none', ...props.style }}\n        />\n      </Presence>\n    );\n  }\n);\n\nCheckboxIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'CheckboxBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface CheckboxBubbleInputProps extends Omit<InputProps, 'checked'> {}\n\nconst CheckboxBubbleInput = React.forwardRef<HTMLInputElement, CheckboxBubbleInputProps>(\n  ({ __scopeCheckbox, ...props }: ScopedProps<CheckboxBubbleInputProps>, forwardedRef) => {\n    const {\n      control,\n      hasConsumerStoppedPropagationRef,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      name,\n      value,\n      form,\n      bubbleInput,\n      setBubbleInput,\n    } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n\n    const composedRefs = useComposedRefs(forwardedRef, setBubbleInput);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = bubbleInput;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n\n      const bubbles = !hasConsumerStoppedPropagationRef.current;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        input.indeterminate = isIndeterminate(checked);\n        setChecked.call(input, isIndeterminate(checked) ? false : checked);\n        input.dispatchEvent(event);\n      }\n    }, [bubbleInput, prevChecked, checked, hasConsumerStoppedPropagationRef]);\n\n    const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n    return (\n      <Primitive.input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={defaultChecked ?? defaultCheckedRef.current}\n        required={required}\n        disabled={disabled}\n        name={name}\n        value={value}\n        form={form}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n          // We transform because the input is absolutely positioned but we have\n          // rendered it **after** the button. This pulls it back to sit on top\n          // of the button.\n          transform: 'translateX(-100%)',\n        }}\n      />\n    );\n  }\n);\n\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction isFunction(value: unknown): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createCheckboxScope,\n  //\n  Checkbox,\n  CheckboxProvider,\n  CheckboxTrigger,\n  CheckboxIndicator,\n  CheckboxBubbleInput,\n  //\n  Checkbox as Root,\n  CheckboxProvider as Provider,\n  CheckboxTrigger as Trigger,\n  CheckboxIndicator as Indicator,\n  CheckboxBubbleInput as BubbleInput,\n};\nexport type {\n  CheckboxProps,\n  CheckboxProviderProps,\n  CheckboxTriggerProps,\n  CheckboxIndicatorProps,\n  CheckboxBubbleInputProps,\n  CheckedState,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAiGtB,SAgIM,UAhIN,KAgIM,YAhIN;;;;;;;;;;;;AA7FJ,IAAM,gBAAgB;AAGtB,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,8KAAI,qBAAA,EAAmB,aAAa;AAqBrF,IAAM,CAAC,sBAAsB,kBAAkB,CAAA,GAC7C,sBAA4C,aAAa;AAkB3D,SAAS,iBACP,KAAA,EACA;IACA,MAAM,EACJ,eAAA,EACA,SAAS,WAAA,EACT,QAAA,EACA,cAAA,EACA,QAAA,EACA,IAAA,EACA,IAAA,EACA,eAAA,EACA,QAAA,EACA,QAAQ,IAAA,EAAA,mBAAA;IAER,0BAAA,EACF,GAAI;IAEJ,MAAM,CAAC,SAAS,UAAU,CAAA,mMAAI,uBAAA,EAAqB;QACjD,MAAM;QACN,aAAa,kBAAkB;QAC/B,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,SAAS,UAAU,CAAA,qKAAU,WAAA,EAAmC,IAAI;IAC3E,MAAM,CAAC,aAAa,cAAc,CAAA,IAAU,4KAAA,EAAkC,IAAI;IAClF,MAAM,qMAAyC,SAAA,EAAO,KAAK;IAC3D,MAAM,gBAAgB,UAClB,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,OAAA,CAAQ,MAAM,IAAA,iFAAA;IAElC;IAEJ,MAAM,UAAuC;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,gBAAgB,cAAc,IAAI,QAAQ;QAC1D;QACA;QACA;IACF;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;QACC,OAAO;QACN,GAAI,OAAA;QAEJ,UAAA,WAAW,0BAA0B,IAAI,2BAA2B,OAAO,IAAI;IAAA;AAGtF;AAMA,IAAM,eAAe;AAUrB,IAAM,mLAAwB,cAAA,EAC5B,CACE,EAAE,eAAA,EAAiB,SAAA,EAAW,OAAA,EAAS,GAAG,cAAc,CAAA,EACxD,iBACG;IACH,MAAM,EACJ,OAAA,EACA,KAAA,EACA,QAAA,EACA,OAAA,EACA,QAAA,EACA,UAAA,EACA,UAAA,EACA,gCAAA,EACA,aAAA,EACA,WAAA,EACF,GAAI,mBAAmB,cAAc,eAAe;IACpD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,UAAU;IAE7D,MAAM,yBAA+B,2KAAA,EAAO,OAAO;sKAC7C,YAAA;qCAAU,MAAM;YACpB,MAAM,OAAO,SAAS;YACtB,IAAI,MAAM;gBACR,MAAM;uDAAQ,IAAM,WAAW,uBAAuB,OAAO;;gBAC7D,KAAK,gBAAA,CAAiB,SAAS,KAAK;gBACpC;iDAAO,IAAM,KAAK,mBAAA,CAAoB,SAAS,KAAK;;YACtD;QACF;oCAAG;QAAC;QAAS,UAAU;KAAC;IAExB,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,MAAK;QACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;QACnD,iBAAe;QACf,cAAY,SAAS,OAAO;QAC5B,iBAAe,WAAW,KAAK,KAAA;QAC/B;QACA;QACC,GAAG,aAAA;QACJ,KAAK;QACL,WAAW,2LAAA,EAAqB,WAAW,CAAC,UAAU;YAEpD,IAAI,MAAM,GAAA,KAAQ,QAAS,CAAA,MAAM,cAAA,CAAe;QAClD,CAAC;QACD,6KAAS,uBAAA,EAAqB,SAAS,CAAC,UAAU;YAChD,WAAW,CAAC,cAAiB,gBAAgB,WAAW,IAAI,OAAO,CAAC,WAAY;YAChF,IAAI,eAAe,eAAe;gBAChC,iCAAiC,OAAA,GAAU,MAAM,oBAAA,CAAqB;gBAMtE,IAAI,CAAC,iCAAiC,OAAA,CAAS,CAAA,MAAM,eAAA,CAAgB;YACvE;QACF,CAAC;IAAA;AAGP;AAGF,gBAAgB,WAAA,GAAc;AAe9B,IAAM,6KAAiB,aAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EACJ,eAAA,EACA,IAAA,EACA,OAAA,EACA,cAAA,EACA,QAAA,EACA,QAAA,EACA,KAAA,EACA,eAAA,EACA,IAAA,EACA,GAAG,eACL,GAAI;IAEJ,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,kBAAA;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,4BAA4B,CAAC,EAAE,aAAA,CAAc,CAAA,GAC3C,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;gBACE,UAAA;oBAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAA;wBACE,GAAG,aAAA;wBACJ,KAAK;wBAEL;oBAAA;oBAED,iBACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qBAAA;wBAEC;oBAAA;iBACF;YAAA,CAEJ;IAAA;AAIR;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,iBAAiB;AAYvB,IAAM,qBAA0B,8KAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,eAAA,EAAiB,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC3D,MAAM,UAAU,mBAAmB,gBAAgB,eAAe;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QACC,SAAS,cAAc,gBAAgB,QAAQ,OAAO,KAAK,QAAQ,OAAA,KAAY;QAE/E,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;YACC,cAAY,SAAS,QAAQ,OAAO;YACpC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;YACtC,GAAG,cAAA;YACJ,KAAK;YACL,OAAO;gBAAE,eAAe;gBAAQ,GAAG,MAAM,KAAA;YAAM;QAAA;IACjD;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,oBAAoB;AAK1B,IAAM,wLAA4B,aAAA,EAChC,CAAC,EAAE,eAAA,EAAiB,GAAG,MAAM,CAAA,EAA0C,iBAAiB;IACtF,MAAM,EACJ,OAAA,EACA,gCAAA,EACA,OAAA,EACA,cAAA,EACA,QAAA,EACA,QAAA,EACA,IAAA,EACA,KAAA,EACA,IAAA,EACA,WAAA,EACA,cAAA,EACF,GAAI,mBAAmB,mBAAmB,eAAe;IAEzD,MAAM,gBAAe,oMAAA,EAAgB,cAAc,cAAc;IACjE,MAAM,iMAAc,cAAA,EAAY,OAAO;IACvC,MAAM,cAAc,yLAAA,EAAQ,OAAO;sKAG7B,YAAA;yCAAU,MAAM;YACpB,MAAM,QAAQ;YACd,IAAI,CAAC,MAAO,CAAA;YAEZ,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;YAC3C,MAAM,aAAa,OAAO,wBAAA,CACxB,YACA;YAEF,MAAM,aAAa,WAAW,GAAA;YAE9B,MAAM,UAAU,CAAC,iCAAiC,OAAA;YAClD,IAAI,gBAAgB,WAAW,YAAY;gBACzC,MAAM,QAAQ,IAAI,MAAM,SAAS;oBAAE;gBAAQ,CAAC;gBAC5C,MAAM,aAAA,GAAgB,gBAAgB,OAAO;gBAC7C,WAAW,IAAA,CAAK,OAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;gBACjE,MAAM,aAAA,CAAc,KAAK;YAC3B;QACF;wCAAG;QAAC;QAAa;QAAa;QAAS,gCAAgC;KAAC;IAExE,MAAM,sLAA0B,SAAA,EAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;IACjF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,KAAA,EAAV;QACC,MAAK;QACL,eAAW;QACX,gBAAgB,kBAAkB,kBAAkB,OAAA;QACpD;QACA;QACA;QACA;QACA;QACC,GAAG,KAAA;QACJ,UAAU,CAAA;QACV,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YACT,GAAG,WAAA;YACH,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;YAAA,sEAAA;YAAA,qEAAA;YAAA,iBAAA;YAIR,WAAW;QACb;IAAA;AAGN;AAGF,oBAAoB,WAAA,GAAc;AAIlC,SAAS,WAAW,KAAA,EAAkD;IACpE,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,gBAAgB,OAAA,EAAoD;IAC3E,OAAO,YAAY;AACrB;AAEA,SAAS,SAAS,OAAA,EAAuB;IACvC,OAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,0KAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-callback-ref/src/use-callback-ref.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;;AAMvB,SAAS,eAAkD,QAAA,EAA4B;IACrF,MAAM,gLAAoB,SAAA,EAAO,QAAQ;sKAEnC,YAAA;oCAAU,MAAM;YACpB,YAAY,OAAA,GAAU;QACxB,CAAC;;IAGD,yKAAa,UAAA;kCAAQ;0CAAO,CAAA,GAAI,OAAS,YAAY,OAAA,GAAU,GAAG,IAAI;;iCAAS,CAAC,CAAC;AACnF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-is-hydrated/src/use-is-hydrated.tsx"], "sourcesContent": ["import { useSyncExternalStore } from 'use-sync-external-store/shim';\n\n/**\n * Determines whether or not the component tree has been hydrated.\n */\nexport function useIsHydrated() {\n  return useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\n\nfunction subscribe() {\n  return () => {};\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B;;AAK9B,SAAS,gBAAgB;IAC9B,gLAAO,uBAAA,EACL;8CACA,IAAM;;8CACN,IAAM;;AAEV;AAEA,SAAS,YAAY;IACnB,OAAO,KAAO,CAAD;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-avatar/src/avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ComponentRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ComponentRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction resolveLoadingStatus(image: HTMLImageElement | null, src?: string): ImageLoadingStatus {\n  if (!image) {\n    return 'idle';\n  }\n  if (!src) {\n    return 'error';\n  }\n  if (image.src !== src) {\n    image.src = src;\n  }\n  return image.complete && image.naturalWidth > 0 ? 'loaded' : 'loading';\n}\n\nfunction useImageLoadingStatus(\n  src: string | undefined,\n  { referrerPolicy, crossOrigin }: AvatarImageProps\n) {\n  const isHydrated = useIsHydrated();\n  const imageRef = React.useRef<HTMLImageElement | null>(null);\n  const image = (() => {\n    if (!isHydrated) return null;\n    if (!imageRef.current) {\n      imageRef.current = new window.Image();\n    }\n    return imageRef.current;\n  })();\n\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>(() =>\n    resolveLoadingStatus(image, src)\n  );\n\n  useLayoutEffect(() => {\n    setLoadingStatus(resolveLoadingStatus(image, src));\n  }, [image, src]);\n\n  useLayoutEffect(() => {\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      setLoadingStatus(status);\n    };\n\n    if (!image) return;\n\n    const handleLoad = updateStatus('loaded');\n    const handleError = updateStatus('error');\n    image.addEventListener('load', handleLoad);\n    image.addEventListener('error', handleError);\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n    if (typeof crossOrigin === 'string') {\n      image.crossOrigin = crossOrigin;\n    }\n\n    return () => {\n      image.removeEventListener('load', handleLoad);\n      image.removeEventListener('error', handleError);\n    };\n  }, [image, crossOrigin, referrerPolicy]);\n\n  return loadingStatus;\n}\n\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;AAoCtB;;;;;;;;;AA5BR,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAM9F,IAAM,2KAAe,aAAA,EACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EAAE,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IAC1C,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,qKAAU,WAAA,EAA6B,MAAM;IAC7F,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA,4BAA4B;QAE5B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;YAAgB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA;AAG1D;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,+KAAoB,cAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAA,EAAK,wBAAwB,KAAO,CAAD,AAAC,EAAG,GAAG,WAAW,CAAA,GAAI;IAChF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,qBAAqB,sBAAsB,KAAK,UAAU;IAChE,MAAM,sNAA4B,iBAAA;iEAAe,CAAC,WAA+B;YAC/E,sBAAsB,MAAM;YAC5B,QAAQ,0BAAA,CAA2B,MAAM;QAC3C,CAAC;;IAED,CAAA,GAAA,sLAAA,CAAA,kBAAA;uCAAgB,MAAM;YACpB,IAAI,uBAAuB,QAAQ;gBACjC,0BAA0B,kBAAkB;YAC9C;QACF;sCAAG;QAAC;QAAoB,yBAAyB;KAAC;IAElD,OAAO,uBAAuB,WAC5B,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;QAAc;IAAA,CAAU,IAC1D;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,mLAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,OAAA,EAAS,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,iBAAiB,eAAe,aAAa;IAC7D,MAAM,CAAC,WAAW,YAAY,CAAA,qKAAU,WAAA,EAAS,YAAY,KAAA,CAAS;QAEhE,0KAAA;oCAAU,MAAM;YACpB,IAAI,YAAY,KAAA,GAAW;gBACzB,MAAM,UAAU,OAAO,UAAA;wDAAW,IAAM,aAAa,IAAI;uDAAG,OAAO;gBACnE;gDAAO,IAAM,OAAO,YAAA,CAAa,OAAO;;YAC1C;QACF;mCAAG;QAAC,OAAO;KAAC;IAEZ,OAAO,aAAa,QAAQ,kBAAA,KAAuB,WACjD,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qLAAA,CAAU,IAAA,EAAV;QAAgB,GAAG,aAAA;QAAe,KAAK;IAAA,CAAc,IACpD;AACN;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,qBAAqB,KAAA,EAAgC,GAAA,EAAkC;IAC9F,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI,MAAM,GAAA,KAAQ,KAAK;QACrB,MAAM,GAAA,GAAM;IACd;IACA,OAAO,MAAM,QAAA,IAAY,MAAM,YAAA,GAAe,IAAI,WAAW;AAC/D;AAEA,SAAS,sBACP,GAAA,EACA,EAAE,cAAA,EAAgB,WAAA,CAAY,CAAA,EAC9B;IACA,MAAM,cAAa,wMAAA,CAAc;IACjC,MAAM,6KAAiB,SAAA,EAAgC,IAAI;IAC3D,MAAM,QAAA,CAAS,MAAM;QACnB,IAAI,CAAC,WAAY,CAAA,OAAO;QACxB,IAAI,CAAC,SAAS,OAAA,EAAS;YACrB,SAAS,OAAA,GAAU,IAAI,OAAO,KAAA,CAAM;QACtC;QACA,OAAO,SAAS,OAAA;IAClB,CAAA,EAAG;IAEH,MAAM,CAAC,eAAe,gBAAgB,CAAA,qKAAU,WAAA;0CAA6B,IAC3E,qBAAqB,OAAO,GAAG;;IAGjC,CAAA,GAAA,sLAAA,CAAA,kBAAA;iDAAgB,MAAM;YACpB,iBAAiB,qBAAqB,OAAO,GAAG,CAAC;QACnD;gDAAG;QAAC;QAAO,GAAG;KAAC;IAEf,CAAA,GAAA,sLAAA,CAAA,kBAAA;iDAAgB,MAAM;YACpB,MAAM;sEAAe,CAAC;8EAA+B,MAAM;4BACzD,iBAAiB,MAAM;wBACzB;;;YAEA,IAAI,CAAC,MAAO,CAAA;YAEZ,MAAM,aAAa,aAAa,QAAQ;YACxC,MAAM,cAAc,aAAa,OAAO;YACxC,MAAM,gBAAA,CAAiB,QAAQ,UAAU;YACzC,MAAM,gBAAA,CAAiB,SAAS,WAAW;YAC3C,IAAI,gBAAgB;gBAClB,MAAM,cAAA,GAAiB;YACzB;YACA,IAAI,OAAO,gBAAgB,UAAU;gBACnC,MAAM,WAAA,GAAc;YACtB;YAEA;yDAAO,MAAM;oBACX,MAAM,mBAAA,CAAoB,QAAQ,UAAU;oBAC5C,MAAM,mBAAA,CAAoB,SAAS,WAAW;gBAChD;;QACF;gDAAG;QAAC;QAAO;QAAa,cAAc;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-collection/src/collection-legacy.tsx", "file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-collection/src/collection.tsx", "file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-collection/src/ordered-dictionary.ts"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n"], "names": ["React", "createContextScope", "useComposedRefs", "createSlot", "jsx", "createCollection", "createContextScope", "React", "useComposedRefs", "createSlot", "itemData"], "mappings": ";;;;;AAAA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AACnC,SAAS,uBAAuB;AAChC,SAAS,kBAA6B;AAuChC;;;;;;;AA1BN,SAAS,iBAAiE,IAAA,EAAc;IAKtF,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,8KAAI,qBAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,wBAAwB,oBAAoB,CAAA,GAAI,wBACrD,eACA;QAAE,eAAe;YAAE,SAAS;QAAK;QAAG,SAAS,aAAA,GAAA,IAAI,IAAI;IAAE;IAGzD,MAAM,qBAA2E,CAAC,UAAU;QAC1F,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,oKAAM,UAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,wKAAU,UAAA,CAAM,MAAA,CAAgC,aAAA,GAAA,IAAI,IAAI,CAAC,EAAE,OAAA;QACjE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,wBAAA;YAAuB;YAAc;YAAkB,eAAe;YACpE;QAAA,CACH;IAEJ;IAEA,mBAAmB,WAAA,GAAc;IAMjC,MAAM,uBAAuB,OAAO;IAEpC,MAAM,6LAAqB,aAAA,EAAW,oBAAoB;IAC1D,MAAM,+KAAiB,UAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,kMAAe,kBAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,iMAAyB,aAAA,EAAW,cAAc;IACxD,MAAM,mLAAqB,UAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,oKAAM,UAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;QACtD,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,6JAAA,CAAA,UAAA,CAAM,SAAA;6DAAU,MAAM;gBACpB,QAAQ,OAAA,CAAQ,GAAA,CAAI,KAAK;oBAAE;oBAAK,GAAI,QAAA;gBAAiC,CAAC;gBACtE;qEAAO,IAAM,KAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,GAAG;;YAC9C,CAAC;;QAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,UAAU,qBAAqB,OAAO,sBAAsB,KAAK;QAEvE,MAAM,yKAAW,UAAA,CAAM,WAAA;oEAAY,MAAM;gBACvC,MAAM,iBAAiB,QAAQ,aAAA,CAAc,OAAA;gBAC7C,IAAI,CAAC,eAAgB,CAAA,OAAO,CAAC,CAAA;gBAC7B,MAAM,eAAe,MAAM,IAAA,CAAK,eAAe,gBAAA,CAAiB,CAAA,CAAA,EAAI,cAAc,CAAA,CAAA,CAAG,CAAC;gBACtF,MAAM,QAAQ,MAAM,IAAA,CAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,CAAC;gBACjD,MAAM,eAAe,MAAM,IAAA;yFACzB,CAAC,GAAG,IAAM,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ,IAAI,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ;;gBAEtF,OAAO;YACT;mEAAG;YAAC,QAAQ,aAAA;YAAe,QAAQ,OAAO;SAAC;QAE3C,OAAO;IACT;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;QACA;KACF;AACF;;;;;;AE9HA,IAAM,iBAAiB,aAAA,GAAA,IAAI,QAAwC;AAC5D,IAAM,cAAN,MAAM,qBAA0B,IAAU;KAC/C,IAAA,CAAA;IAGA,YAAY,OAAA,CAA+C;QACzD,KAAA,CAAM,OAAO;QACb,IAAA,EAAK,IAAA,GAAQ,CAAC;eAAG,KAAA,CAAM,KAAK,CAAC;SAAA;QAC7B,eAAe,GAAA,CAAI,IAAA,EAAM,IAAI;IAC/B;IAEA,IAAI,GAAA,EAAQ,KAAA,EAAU;QACpB,IAAI,eAAe,GAAA,CAAI,IAAI,GAAG;YAC5B,IAAI,IAAA,CAAK,GAAA,CAAI,GAAG,GAAG;gBACjB,IAAA,EAAK,IAAA,CAAM,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ,GAAG,CAAC,CAAA,GAAI;YACxC,OAAO;gBACL,IAAA,EAAK,IAAA,CAAM,IAAA,CAAK,GAAG;YACrB;QACF;QACA,KAAA,CAAM,IAAI,KAAK,KAAK;QACpB,OAAO,IAAA;IACT;IAEA,OAAO,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACtC,MAAM,MAAM,IAAA,CAAK,GAAA,CAAI,GAAG;QACxB,MAAM,SAAS,IAAA,EAAK,IAAA,CAAM,MAAA;QAC1B,MAAM,gBAAgB,cAAc,KAAK;QACzC,IAAI,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;QAChE,MAAM,YAAY,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;QAElE,IAAI,cAAc,IAAA,CAAK,IAAA,IAAS,OAAO,cAAc,IAAA,CAAK,IAAA,GAAO,KAAM,cAAc,CAAA,GAAI;YACvF,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACnB,OAAO,IAAA;QACT;QAEA,MAAM,OAAO,IAAA,CAAK,IAAA,GAAA,CAAQ,MAAM,IAAI,CAAA;QAMpC,IAAI,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAM,OAAO,CAAC;eAAG,IAAA,CAAK,KAAK;SAAA;QAC3B,IAAI;QACJ,IAAI,aAAa;QACjB,IAAA,IAAS,IAAI,aAAa,IAAI,MAAM,IAAK;YACvC,IAAI,gBAAgB,GAAG;gBACrB,IAAI,UAAU,IAAA,CAAK,CAAC,CAAA;gBACpB,IAAI,IAAA,CAAK,CAAC,CAAA,KAAM,KAAK;oBACnB,UAAU,IAAA,CAAK,IAAI,CAAC,CAAA;gBACtB;gBACA,IAAI,KAAK;oBAEP,IAAA,CAAK,MAAA,CAAO,GAAG;gBACjB;gBACA,YAAY,IAAA,CAAK,GAAA,CAAI,OAAO;gBAC5B,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACrB,OAAO;gBACL,IAAI,CAAC,cAAc,IAAA,CAAK,IAAI,CAAC,CAAA,KAAM,KAAK;oBACtC,aAAa;gBACf;gBACA,MAAM,aAAa,IAAA,CAAK,aAAa,IAAI,IAAI,CAAC,CAAA;gBAC9C,MAAM,eAAe;gBACrB,YAAY,IAAA,CAAK,GAAA,CAAI,UAAU;gBAC/B,IAAA,CAAK,MAAA,CAAO,UAAU;gBACtB,IAAA,CAAK,GAAA,CAAI,YAAY,YAAY;YACnC;QACF;QACA,OAAO,IAAA;IACT;IAEA,KAAK,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACpC,MAAM,OAAO,IAAI,aAAY,IAAI;QACjC,KAAK,MAAA,CAAO,OAAO,KAAK,KAAK;QAC7B,OAAO;IACT;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG,IAAI;QACxC,IAAI,QAAQ,GAAG;YACb,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,UAAU,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACrC,MAAM,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,OAAO,QAAQ,KAAK;IACzC;IAEA,MAAM,GAAA,EAAQ;QACZ,IAAI,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;QAClC,QAAQ,UAAU,CAAA,KAAM,UAAU,IAAA,CAAK,IAAA,GAAO,IAAI,CAAA,IAAK,QAAQ;QAC/D,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,SAAS,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACpC,MAAM,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG,QAAQ,KAAK;IAC7C;IAEA,QAAQ;QACN,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAC;IACvB;IAEA,OAAO;QACL,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAA,CAAE;IACxB;IAEA,QAAQ;QACN,IAAA,EAAK,IAAA,GAAQ,CAAC,CAAA;QACd,OAAO,KAAA,CAAM,MAAM;IACrB;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,UAAU,KAAA,CAAM,OAAO,GAAG;QAChC,IAAI,SAAS;YACX,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG,GAAG,CAAC;QAC9C;QACA,OAAO;IACT;IAEA,SAAS,KAAA,EAAe;QACtB,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;QAC5B,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG;QACxB;QACA,OAAO;IACT;IAEA,GAAG,KAAA,EAAe;QAChB,MAAM,MAAM,GAAG,IAAA,EAAK,IAAA,EAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG;QACrB;IACF;IAEA,QAAQ,KAAA,EAAmC;QACzC,MAAM,MAAM,GAAG,IAAA,CAAK,KAAA,EAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO;gBAAC;gBAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAE;aAAA;QAC7B;IACF;IAEA,QAAQ,GAAA,EAAQ;QACd,OAAO,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;IAC/B;IAEA,MAAM,KAAA,EAAe;QACnB,OAAO,GAAG,IAAA,EAAK,IAAA,EAAO,KAAK;IAC7B;IAEA,KAAK,GAAA,EAAQ,MAAA,EAAgB;QAC3B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,EAAA,CAAG,IAAI;IACrB;IAEA,QAAQ,GAAA,EAAQ,MAAA,EAAgB;QAC9B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,KAAA,CAAM,IAAI;IACxB;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,KAAA;IACT;IAEA,UACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,CAAA;IACT;IAYA,OACE,SAAA,EACA,OAAA,EACA;QACA,MAAM,UAAyB,CAAC,CAAA;QAChC,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,QAAQ,IAAA,CAAK,KAAK;YACpB;YACA;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,IACE,UAAA,EACA,OAAA,EACmB;QACnB,MAAM,UAAoB,CAAC,CAAA;QAC3B,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,QAAQ,IAAA,CAAK;gBAAC,KAAA,CAAM,CAAC,CAAA;gBAAG,QAAQ,KAAA,CAAM,YAAY,SAAS;oBAAC;oBAAO;oBAAO,IAAI;iBAAC,CAAC;aAAC;YACjF;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IA6BA,OAAA,GACK,IAAA,EASH;QACA,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,QAAQ;QACZ,IAAI,cAAc,gBAAgB,IAAA,CAAK,EAAA,CAAG,CAAC;QAC3C,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,UAAU,KAAK,KAAK,MAAA,KAAW,GAAG;gBACpC,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;YACA;QACF;QACA,OAAO;IACT;IA6BA,YAAA,GACK,IAAA,EASH;QACA,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,cAAc,gBAAgB,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE;QAC5C,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,QAAQ,IAAA,CAAK,EAAA,CAAG,KAAK;YAC3B,IAAI,UAAU,IAAA,CAAK,IAAA,GAAO,KAAK,KAAK,MAAA,KAAW,GAAG;gBAChD,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;QACF;QACA,OAAO;IACT;IAEA,SAAS,SAAA,EAAiE;QACxE,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA,CAAE,IAAA,CAAK,SAAS;QAClD,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,aAAgC;QAC9B,MAAM,WAAW,IAAI,aAAkB;QACvC,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,SAAS,GAAA,CAAI,KAAK,OAAO;QAC3B;QACA,OAAO;IACT;IAKA,UAAA,GAAa,IAAA,EAAgE;QAC3E,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA;QAClC,QAAQ,MAAA,CAAO,GAAG,IAAI;QACtB,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,MAAM,KAAA,EAAgB,GAAA,EAAc;QAClC,MAAM,SAAS,IAAI,aAAkB;QACrC,IAAI,OAAO,IAAA,CAAK,IAAA,GAAO;QAEvB,IAAI,UAAU,KAAA,GAAW;YACvB,OAAO;QACT;QAEA,IAAI,QAAQ,GAAG;YACb,QAAQ,QAAQ,IAAA,CAAK,IAAA;QACvB;QAEA,IAAI,QAAQ,KAAA,KAAa,MAAM,GAAG;YAChC,OAAO,MAAM;QACf;QAEA,IAAA,IAAS,QAAQ,OAAO,SAAS,MAAM,QAAS;YAC9C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,OAAO,GAAA,CAAI,KAAK,OAAO;QACzB;QACA,OAAO;IACT;IAEA,MACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,CAAC,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC5D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;AACF;AAUA,SAAS,GAAM,KAAA,EAAqB,KAAA,EAA8B;IAChE,IAAI,QAAQ,MAAM,SAAA,EAAW;QAC3B,OAAO,MAAM,SAAA,CAAU,EAAA,CAAG,IAAA,CAAK,OAAO,KAAK;IAC7C;IACA,MAAM,cAAc,YAAY,OAAO,KAAK;IAC5C,OAAO,gBAAgB,CAAA,IAAK,KAAA,IAAY,KAAA,CAAM,WAAW,CAAA;AAC3D;AAEA,SAAS,YAAY,KAAA,EAAuB,KAAA,EAAe;IACzD,MAAM,SAAS,MAAM,MAAA;IACrB,MAAM,gBAAgB,cAAc,KAAK;IACzC,MAAM,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;IAClE,OAAO,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;AACzD;AAEA,SAAS,cAAc,MAAA,EAAgB;IAErC,OAAO,WAAW,UAAU,WAAW,IAAI,IAAI,KAAK,KAAA,CAAM,MAAM;AAClE;;ADtbA,SAASK,kBAGP,IAAA,EAAc;IAKd,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,IAAIC,+LAAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,2BAA2B,oBAAoB,CAAA,GAAI,wBACxD,eACA;QACE,mBAAmB;QACnB,eAAe;YAAE,SAAS;QAAK;QAC/B,qBAAqB;YAAE,SAAS;QAAK;QACrC,SAAS,IAAI,YAAY;QACzB,YAAY,IAAM,KAAA;IACpB;IAQF,MAAM,qBAID,CAAC,EAAE,KAAA,EAAO,GAAG,MAAM,CAAA,KAAM;QAC5B,OAAO,QACL,aAAA,OAAAF,6KAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc,IAEjD,aAAA,GAAAA,iLAAAA,EAAC,gBAAA;YAAgB,GAAG,KAAA;QAAA,CAAO;IAE/B;IACA,mBAAmB,WAAA,GAAc;IAEjC,MAAM,iBAGD,CAAC,UAAU;QACd,MAAM,QAAQ,kBAAkB;QAChC,OAAO,aAAA,8KAAAA,MAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc;IAC1D;IACA,eAAe,WAAA,GAAc,gBAAgB;IAE7C,MAAM,yBAID,CAAC,UAAU;QACd,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;QACnC,MAAM,oKAAMG,UAAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,iKAAIA,UAAAA,CAAM,QAAA,CACtD;QAEF,MAAM,eAAcC,oMAAAA,EAAgB,KAAK,oBAAoB;QAC7D,MAAM,CAAC,SAAS,UAAU,CAAA,GAAI;sKAE9BD,UAAAA,CAAM,SAAA;kEAAU,MAAM;gBACpB,IAAI,CAAC,kBAAmB,CAAA;gBAExB,MAAM,WAAW;mFAAqB,KAkBtC,CAlB4C,AAkB3C;;gBACD,SAAS,OAAA,CAAQ,mBAAmB;oBAClC,WAAW;oBACX,SAAS;gBACX,CAAC;gBACD;0EAAO,MAAM;wBACX,SAAS,UAAA,CAAW;oBACtB;;YACF;iEAAG;YAAC,iBAAiB;SAAC;QAEtB,OACE,aAAA,8KAAAH,MAAAA,EAAC,2BAAA;YACC;YACA;YACA;YACA,eAAe;YACf,qBAAqB;YACrB;YAEC;QAAA;IAGP;IAEA,uBAAuB,WAAA,GAAc,gBAAgB;IAMrD,MAAM,uBAAuB,OAAO;IAEpC,MAAM,6LAAqBK,aAAAA,EAAW,oBAAoB;IAC1D,MAAM,+KAAiBF,UAAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,gBAAeC,oMAAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,8KAAAJ,MAAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,iMAAyBK,aAAAA,EAAW,cAAc;IACxD,MAAM,qBAAqBF,wKAAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,oKAAMA,UAAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,CAAC,SAAS,UAAU,CAAA,gKAAIA,WAAAA,CAAM,QAAA,CAA6B,IAAI;QACrE,MAAM,kMAAeC,kBAAAA,EAAgB,cAAc,KAAK,UAAU;QAClE,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,MAAM,EAAE,UAAA,CAAW,CAAA,GAAI;QAEvB,MAAM,cAAcD,wKAAAA,CAAM,MAAA,CAAO,QAAQ;QACzC,IAAI,CAAC,aAAa,YAAY,OAAA,EAAS,QAAQ,GAAG;YAChD,YAAY,OAAA,GAAU;QACxB;QACA,MAAM,mBAAmB,YAAY,OAAA;sKAErCA,UAAAA,CAAM,SAAA;8DAAU,MAAM;gBACpB,MAAMG,YAAW;gBACjB;sEAAW,CAAC,QAAQ;wBAClB,IAAI,CAAC,SAAS;4BACZ,OAAO;wBACT;wBAEA,IAAI,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;4BACrB,IAAI,GAAA,CAAI,SAAS;gCAAE,GAAIA,SAAAA;gCAAkC;4BAAQ,CAAC;4BAClE,OAAO,IAAI,QAAA,CAAS,sBAAsB;wBAC5C;wBAEA,OAAO,IACJ,GAAA,CAAI,SAAS;4BAAE,GAAIA,SAAAA;4BAAkC;wBAAQ,CAAC,EAC9D,QAAA,CAAS,sBAAsB;oBACpC,CAAC;;gBAED;sEAAO,MAAM;wBACX;8EAAW,CAAC,QAAQ;gCAClB,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;oCACjC,OAAO;gCACT;gCACA,IAAI,MAAA,CAAO,OAAO;gCAClB,OAAO,IAAI,YAAY,GAAG;4BAC5B,CAAC;;oBACH;;YACF;6DAAG;YAAC;YAAS;YAAkB,UAAU;SAAC;QAE1C,OACE,aAAA,GAAAN,iLAAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,oBAAoB;QAC3B,OAAOG,wKAAAA,CAAM,QAAA,CAAyC,IAAI,YAAY,CAAC;IACzE;IAMA,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,qBAAqB,OAAO,sBAAsB,KAAK;QAE3E,OAAO;IACT;IAEA,MAAM,YAAY;QAChB;QACA;QACA;IACF;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;KACF;AACF;AAKA,SAAS,aAAa,CAAA,EAAQ,CAAA,EAAQ;IACpC,IAAI,MAAM,EAAG,CAAA,OAAO;IACpB,IAAI,OAAO,MAAM,YAAY,OAAO,MAAM,SAAU,CAAA,OAAO;IAC3D,IAAI,KAAK,QAAQ,KAAK,KAAM,CAAA,OAAO;IACnC,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,IAAI,MAAM,MAAA,KAAW,MAAM,MAAA,CAAQ,CAAA,OAAO;IAC1C,KAAA,MAAW,OAAO,MAAO;QACvB,IAAI,CAAC,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,GAAG,GAAG,EAAG,CAAA,OAAO;QAC1D,IAAI,CAAA,CAAE,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,CAAG,CAAA,OAAO;IAChC;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAY,CAAA,EAAY;IAClD,OAAO,CAAC,CAAA,CAAE,EAAE,uBAAA,CAAwB,CAAC,IAAI,KAAK,2BAAA;AAChD;AAEA,SAAS,uBACP,CAAA,EACA,CAAA,EACA;IACA,OAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,IAAW,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,GAC1B,IACA,mBAAmB,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,CAAE,CAAC,CAAA,CAAE,OAAO,IAC3C,CAAA,IACA;AACR;AAEA,SAAS,qBAAqB,QAAA,EAAsB;IAClD,MAAM,WAAW,IAAI,iBAAiB,CAAC,kBAAkB;QACvD,KAAA,MAAW,YAAY,cAAe;YACpC,IAAI,SAAS,IAAA,KAAS,aAAa;gBACjC,SAAS;gBACT;YACF;QACF;IACF,CAAC;IAED,OAAO;AACT", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 2097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-direction/src/direction.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AAed;;;AAZT,IAAM,qLAAyB,gBAAA,EAAqC,KAAA,CAAS;AAU7E,IAAM,oBAAsD,CAAC,UAAU;IACrE,MAAM,EAAE,GAAA,EAAK,QAAA,CAAS,CAAA,GAAI;IAC1B,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAiB,QAAA,EAAjB;QAA0B,OAAO;QAAM;IAAA,CAAS;AAC1D;AAIA,SAAS,aAAa,QAAA,EAAsB;IAC1C,MAAM,8KAAkB,aAAA,EAAW,gBAAgB;IACnD,OAAO,YAAY,aAAa;AAClC;AAEA,IAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-use-escape-keydown/src/use-escape-keydown.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;AACvB,SAAS,sBAAsB;;;AAK/B,SAAS,iBACP,mBAAA,EACA,gBAA0B,YAAY,QAAA,EACtC;IACA,MAAM,4MAAkB,iBAAA,EAAe,mBAAmB;IAEpD,8KAAA;sCAAU,MAAM;YACpB,MAAM;4DAAgB,CAAC,UAAyB;oBAC9C,IAAI,MAAM,GAAA,KAAQ,UAAU;wBAC1B,gBAAgB,KAAK;oBACvB;gBACF;;YACA,cAAc,gBAAA,CAAiB,WAAW,eAAe;gBAAE,SAAS;YAAK,CAAC;YAC1E;8CAAO,IAAM,cAAc,mBAAA,CAAoB,WAAW,eAAe;wBAAE,SAAS;oBAAK,CAAC;;QAC5F;qCAAG;QAAC;QAAiB,aAAa;KAAC;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-dismissable-layer/src/dismissable-layer.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled!); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ComponentRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      } else {\n        // We need to remove the event listener in case the outside click has been canceled.\n        // See: https://github.com/radix-ui/primitives/issues/2171\n        ownerDocument.removeEventListener('click', handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n"], "names": ["node", "handleAndDispatchPointerDownOutsideEvent"], "mappings": ";;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,WAAW,mCAAmC;AACvD,SAAS,uBAAuB;AAChC,SAAS,sBAAsB;AAC/B,SAAS,wBAAwB;AAqJ3B;;;;;;;;;AA/IN,IAAM,yBAAyB;AAC/B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AAEtB,IAAI;AAEJ,IAAM,4LAAgC,gBAAA,EAAc;IAClD,QAAQ,aAAA,GAAA,IAAI,IAA6B;IACzC,wCAAwC,aAAA,GAAA,IAAI,IAA6B;IACzE,UAAU,aAAA,GAAA,IAAI,IAAmC;AACnD,CAAC;AAsCD,IAAM,qLAAyB,aAAA,EAC7B,CAAC,OAAO,iBAAiB;IACvB,MAAM,EACJ,8BAA8B,KAAA,EAC9B,eAAA,EACA,oBAAA,EACA,cAAA,EACA,iBAAA,EACA,SAAA,EACA,GAAG,YACL,GAAI;IACJ,MAAM,4KAAgB,aAAA,EAAW,uBAAuB;IACxD,MAAM,CAAC,MAAM,OAAO,CAAA,OAAU,yKAAA,EAAyC,IAAI;IAC3E,MAAM,gBAAgB,MAAM,iBAAiB,YAAY;IACzD,MAAM,CAAC,EAAE,KAAK,CAAA,qKAAU,WAAA,EAAS,CAAC,CAAC;IACnC,MAAM,kMAAe,kBAAA,EAAgB;0DAAc,CAACA,QAAS,QAAQA,KAAI,CAAC;;IAC1E,MAAM,SAAS,MAAM,IAAA,CAAK,QAAQ,MAAM;IACxC,MAAM,CAAC,4CAA4C,CAAA,GAAI,CAAC;WAAG,QAAQ,sCAAsC;KAAA,CAAE,KAAA,CAAM,CAAA,CAAE;IACnH,MAAM,oDAAoD,OAAO,OAAA,CAAQ,4CAA6C;IACtH,MAAM,QAAQ,OAAO,OAAO,OAAA,CAAQ,IAAI,IAAI,CAAA;IAC5C,MAAM,8BAA8B,QAAQ,sCAAA,CAAuC,IAAA,GAAO;IAC1F,MAAM,yBAAyB,SAAS;IAExC,MAAM,qBAAqB;sEAAsB,CAAC,UAAU;YAC1D,MAAM,SAAS,MAAM,MAAA;YACrB,MAAM,wBAAwB,CAAC;mBAAG,QAAQ,QAAQ;aAAA,CAAE,IAAA;oGAAK,CAAC,SAAW,OAAO,QAAA,CAAS,MAAM,CAAC;;YAC5F,IAAI,CAAC,0BAA0B,sBAAuB,CAAA;YACtD,uBAAuB,KAAK;YAC5B,oBAAoB,KAAK;YACzB,IAAI,CAAC,MAAM,gBAAA,CAAkB,CAAA,YAAY;QAC3C;qEAAG,aAAa;IAEhB,MAAM,eAAe;0DAAgB,CAAC,UAAU;YAC9C,MAAM,SAAS,MAAM,MAAA;YACrB,MAAM,kBAAkB,CAAC;mBAAG,QAAQ,QAAQ;aAAA,CAAE,IAAA;kFAAK,CAAC,SAAW,OAAO,QAAA,CAAS,MAAM,CAAC;;YACtF,IAAI,gBAAiB,CAAA;YACrB,iBAAiB,KAAK;YACtB,oBAAoB,KAAK;YACzB,IAAI,CAAC,MAAM,gBAAA,CAAkB,CAAA,YAAY;QAC3C;yDAAG,aAAa;IAEhB,CAAA,GAAA,uLAAA,CAAA,mBAAA;6CAAiB,CAAC,UAAU;YAC1B,MAAM,iBAAiB,UAAU,QAAQ,MAAA,CAAO,IAAA,GAAO;YACvD,IAAI,CAAC,eAAgB,CAAA;YACrB,kBAAkB,KAAK;YACvB,IAAI,CAAC,MAAM,gBAAA,IAAoB,WAAW;gBACxC,MAAM,cAAA,CAAe;gBACrB,UAAU;YACZ;QACF;4CAAG,aAAa;sKAEV,YAAA;sCAAU,MAAM;YACpB,IAAI,CAAC,KAAM,CAAA;YACX,IAAI,6BAA6B;gBAC/B,IAAI,QAAQ,sCAAA,CAAuC,IAAA,KAAS,GAAG;oBAC7D,4BAA4B,cAAc,IAAA,CAAK,KAAA,CAAM,aAAA;oBACrD,cAAc,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB;gBAC3C;gBACA,QAAQ,sCAAA,CAAuC,GAAA,CAAI,IAAI;YACzD;YACA,QAAQ,MAAA,CAAO,GAAA,CAAI,IAAI;YACvB,eAAe;YACf;8CAAO,MAAM;oBACX,IACE,+BACA,QAAQ,sCAAA,CAAuC,IAAA,KAAS,GACxD;wBACA,cAAc,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB;oBAC3C;gBACF;;QACF;qCAAG;QAAC;QAAM;QAAe;QAA6B,OAAO;KAAC;sKAQxD,YAAA;sCAAU,MAAM;YACpB;8CAAO,MAAM;oBACX,IAAI,CAAC,KAAM,CAAA;oBACX,QAAQ,MAAA,CAAO,MAAA,CAAO,IAAI;oBAC1B,QAAQ,sCAAA,CAAuC,MAAA,CAAO,IAAI;oBAC1D,eAAe;gBACjB;;QACF;qCAAG;QAAC;QAAM,OAAO;KAAC;QAEZ,0KAAA;sCAAU,MAAM;YACpB,MAAM;2DAAe,IAAM,MAAM,CAAC,CAAC;;YACnC,SAAS,gBAAA,CAAiB,gBAAgB,YAAY;YACtD;8CAAO,IAAM,SAAS,mBAAA,CAAoB,gBAAgB,YAAY;;QACxE;qCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACE,GAAG,UAAA;QACJ,KAAK;QACL,OAAO;YACL,eAAe,8BACX,yBACE,SACA,SACF,KAAA;YACJ,GAAG,MAAM,KAAA;QACX;QACA,oLAAgB,uBAAA,EAAqB,MAAM,cAAA,EAAgB,aAAa,cAAc;QACtF,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,aAAa,aAAa;QACnF,0LAAsB,uBAAA,EACpB,MAAM,oBAAA,EACN,mBAAmB,oBAAA;IACrB;AAGN;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,cAAc;AAKpB,IAAM,2LAA+B,aAAA,EAGnC,CAAC,OAAO,iBAAiB;IACzB,MAAM,4KAAgB,aAAA,EAAW,uBAAuB;IACxD,MAAM,OAAY,0KAAA,EAAsC,IAAI;IAC5D,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;sKAEhD,YAAA;4CAAU,MAAM;YACpB,MAAM,OAAO,IAAI,OAAA;YACjB,IAAI,MAAM;gBACR,QAAQ,QAAA,CAAS,GAAA,CAAI,IAAI;gBACzB;wDAAO,MAAM;wBACX,QAAQ,QAAA,CAAS,MAAA,CAAO,IAAI;oBAC9B;;YACF;QACF;2CAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,KAAA;QAAO,KAAK;IAAA,CAAc;AACtD,CAAC;AAED,uBAAuB,WAAA,GAAc;AAYrC,SAAS,sBACP,oBAAA,EACA,gBAA0B,YAAY,QAAA,EACtC;IACA,MAAM,qNAA2B,iBAAA,EAAe,oBAAoB;IACpE,MAAM,kCAAoC,uKAAA,EAAO,KAAK;IACtD,MAAM,mLAAuB,SAAA;wDAAO,KAAO,CAAD,AAAE;;sKAEtC,YAAA;2CAAU,MAAM;YACpB,MAAM;qEAAoB,CAAC,UAAwB;oBACjD,IAAI,MAAM,MAAA,IAAU,CAAC,4BAA4B,OAAA,EAAS;wBAGxD,IAASC;2HAAT,WAAoD;gCAClD,6BACE,sBACA,0BACA,aACA;oCAAE,UAAU;gCAAK;4BAErB;;wBAPS,IAAA,2CAAAA;wBAFT,MAAM,cAAc;4BAAE,eAAe;wBAAM;wBAuB3C,IAAI,MAAM,WAAA,KAAgB,SAAS;4BACjC,cAAc,mBAAA,CAAoB,SAAS,eAAe,OAAO;4BACjE,eAAe,OAAA,GAAUA;4BACzB,cAAc,gBAAA,CAAiB,SAAS,eAAe,OAAA,EAAS;gCAAE,MAAM;4BAAK,CAAC;wBAChF,OAAO;4BACLA,0CAAyC;wBAC3C;oBACF,OAAO;wBAGL,cAAc,mBAAA,CAAoB,SAAS,eAAe,OAAO;oBACnE;oBACA,4BAA4B,OAAA,GAAU;gBACxC;;YAcA,MAAM,UAAU,OAAO,UAAA;2DAAW,MAAM;oBACtC,cAAc,gBAAA,CAAiB,eAAe,iBAAiB;gBACjE;0DAAG,CAAC;YACJ;mDAAO,MAAM;oBACX,OAAO,YAAA,CAAa,OAAO;oBAC3B,cAAc,mBAAA,CAAoB,eAAe,iBAAiB;oBAClE,cAAc,mBAAA,CAAoB,SAAS,eAAe,OAAO;gBACnE;;QACF;0CAAG;QAAC;QAAe,wBAAwB;KAAC;IAE5C,OAAO;QAAA,4DAAA;QAEL,sBAAsB,IAAO,4BAA4B,OAAA,GAAU;IACrE;AACF;AAMA,SAAS,gBACP,cAAA,EACA,gBAA0B,YAAY,QAAA,EACtC;IACA,MAAM,yBAAqB,uMAAA,EAAe,cAAc;IACxD,MAAM,8LAAkC,SAAA,EAAO,KAAK;sKAE9C,YAAA;qCAAU,MAAM;YACpB,MAAM;yDAAc,CAAC,UAAsB;oBACzC,IAAI,MAAM,MAAA,IAAU,CAAC,0BAA0B,OAAA,EAAS;wBACtD,MAAM,cAAc;4BAAE,eAAe;wBAAM;wBAC3C,6BAA6B,eAAe,oBAAoB,aAAa;4BAC3E,UAAU;wBACZ,CAAC;oBACH;gBACF;;YACA,cAAc,gBAAA,CAAiB,WAAW,WAAW;YACrD;6CAAO,IAAM,cAAc,mBAAA,CAAoB,WAAW,WAAW;;QACvE;oCAAG;QAAC;QAAe,kBAAkB;KAAC;IAEtC,OAAO;QACL,gBAAgB,IAAO,0BAA0B,OAAA,GAAU;QAC3D,eAAe,IAAO,0BAA0B,OAAA,GAAU;IAC5D;AACF;AAEA,SAAS,iBAAiB;IACxB,MAAM,QAAQ,IAAI,YAAY,cAAc;IAC5C,SAAS,aAAA,CAAc,KAAK;AAC9B;AAEA,SAAS,6BACP,IAAA,EACA,OAAA,EACA,MAAA,EACA,EAAE,QAAA,CAAS,CAAA,EACX;IACA,MAAM,SAAS,OAAO,aAAA,CAAc,MAAA;IACpC,MAAM,QAAQ,IAAI,YAAY,MAAM;QAAE,SAAS;QAAO,YAAY;QAAM;IAAO,CAAC;IAChF,IAAI,QAAS,CAAA,OAAO,gBAAA,CAAiB,MAAM,SAA0B;QAAE,MAAM;IAAK,CAAC;IAEnF,IAAI,UAAU;QACZ,CAAA,GAAA,wKAAA,CAAA,8BAAA,EAA4B,QAAQ,KAAK;IAC3C,OAAO;QACL,OAAO,aAAA,CAAc,KAAK;IAC5B;AACF;AAEA,IAAM,OAAO;AACb,IAAM,SAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-focus-guards/src/focus-guards.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;;;AAGvB,IAAI,QAAQ;AAEZ,SAAS,YAAY,KAAA,EAAY;IAC/B,eAAe;IACf,OAAO,MAAM,QAAA;AACf;AAMA,SAAS,iBAAiB;QAClB,0KAAA;oCAAU,MAAM;YACpB,MAAM,aAAa,SAAS,gBAAA,CAAiB,0BAA0B;YACvE,SAAS,IAAA,CAAK,qBAAA,CAAsB,cAAc,UAAA,CAAW,CAAC,CAAA,IAAK,iBAAiB,CAAC;YACrF,SAAS,IAAA,CAAK,qBAAA,CAAsB,aAAa,UAAA,CAAW,CAAC,CAAA,IAAK,iBAAiB,CAAC;YACpF;YAEA;4CAAO,MAAM;oBACX,IAAI,UAAU,GAAG;wBACf,SAAS,gBAAA,CAAiB,0BAA0B,EAAE,OAAA;wDAAQ,CAAC,OAAS,KAAK,MAAA,CAAO,CAAC;;oBACvF;oBACA;gBACF;;QACF;mCAAG,CAAC,CAAC;AACP;AAEA,SAAS,mBAAmB;IAC1B,MAAM,UAAU,SAAS,aAAA,CAAc,MAAM;IAC7C,QAAQ,YAAA,CAAa,0BAA0B,EAAE;IACjD,QAAQ,QAAA,GAAW;IACnB,QAAQ,KAAA,CAAM,OAAA,GAAU;IACxB,QAAQ,KAAA,CAAM,OAAA,GAAU;IACxB,QAAQ,KAAA,CAAM,QAAA,GAAW;IACzB,QAAQ,KAAA,CAAM,aAAA,GAAgB;IAC9B,OAAO;AACT;AAEA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-focus-scope/src/focus-scope.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n"], "names": ["handleFocusIn", "handleFocusOut", "handleMutations", "container"], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAwM3B;;;;;;;AAtMJ,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;IAAE,SAAS;IAAO,YAAY;AAAK;AAQzD,IAAM,mBAAmB;AAgCzB,IAAM,+KAAmB,aAAA,EAA+C,CAAC,OAAO,iBAAiB;IAC/F,MAAM,EACJ,OAAO,KAAA,EACP,UAAU,KAAA,EACV,kBAAkB,oBAAA,EAClB,oBAAoB,sBAAA,EACpB,GAAG,YACL,GAAI;IACJ,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,6KAAA,EAA6B,IAAI;IACzE,MAAM,6MAAmB,iBAAA,EAAe,oBAAoB;IAC5D,MAAM,sBAAqB,0MAAA,EAAe,sBAAsB;IAChE,MAAM,0LAA8B,SAAA,EAA2B,IAAI;IACnE,MAAM,eAAe,qMAAA,EAAgB;oDAAc,CAAC,OAAS,aAAa,IAAI,CAAC;;IAE/E,MAAM,+KAAmB,SAAA,EAAO;QAC9B,QAAQ;QACR,QAAQ;YACN,IAAA,CAAK,MAAA,GAAS;QAChB;QACA,SAAS;YACP,IAAA,CAAK,MAAA,GAAS;QAChB;IACF,CAAC,EAAE,OAAA;QAGG,0KAAA;gCAAU,MAAM;YACpB,IAAI,SAAS;gBACX,IAASA;2DAAT,SAAuB,KAAA,EAAmB;wBACxC,IAAI,WAAW,MAAA,IAAU,CAAC,UAAW,CAAA;wBACrC,MAAM,SAAS,MAAM,MAAA;wBACrB,IAAI,UAAU,QAAA,CAAS,MAAM,GAAG;4BAC9B,sBAAsB,OAAA,GAAU;wBAClC,OAAO;4BACL,MAAM,sBAAsB,OAAA,EAAS;gCAAE,QAAQ;4BAAK,CAAC;wBACvD;oBACF;0DAESC;4DAAT,SAAwB,KAAA,EAAmB;wBACzC,IAAI,WAAW,MAAA,IAAU,CAAC,UAAW,CAAA;wBACrC,MAAM,gBAAgB,MAAM,aAAA;wBAY5B,IAAI,kBAAkB,KAAM,CAAA;wBAI5B,IAAI,CAAC,UAAU,QAAA,CAAS,aAAa,GAAG;4BACtC,MAAM,sBAAsB,OAAA,EAAS;gCAAE,QAAQ;4BAAK,CAAC;wBACvD;oBACF;2DAKSC;6DAAT,SAAyB,SAAA,EAA6B;wBACpD,MAAM,iBAAiB,SAAS,aAAA;wBAChC,IAAI,mBAAmB,SAAS,IAAA,CAAM,CAAA;wBACtC,KAAA,MAAW,YAAY,UAAW;4BAChC,IAAI,SAAS,YAAA,CAAa,MAAA,GAAS,EAAG,CAAA,MAAM,SAAS;wBACvD;oBACF;;gBA1CS,IAAA,gBAAAF,gBAUA,iBAAAC,iBA0BA,kBAAAC;gBAQT,SAAS,gBAAA,CAAiB,WAAWF,cAAa;gBAClD,SAAS,gBAAA,CAAiB,YAAYC,eAAc;gBACpD,MAAM,mBAAmB,IAAI,iBAAiBC,gBAAe;gBAC7D,IAAI,UAAW,CAAA,iBAAiB,OAAA,CAAQ,WAAW;oBAAE,WAAW;oBAAM,SAAS;gBAAK,CAAC;gBAErF;4CAAO,MAAM;wBACX,SAAS,mBAAA,CAAoB,WAAWF,cAAa;wBACrD,SAAS,mBAAA,CAAoB,YAAYC,eAAc;wBACvD,iBAAiB,UAAA,CAAW;oBAC9B;;YACF;QACF;+BAAG;QAAC;QAAS;QAAW,WAAW,MAAM;KAAC;sKAEpC,YAAA;gCAAU,MAAM;YACpB,IAAI,WAAW;gBACb,iBAAiB,GAAA,CAAI,UAAU;gBAC/B,MAAM,2BAA2B,SAAS,aAAA;gBAC1C,MAAM,sBAAsB,UAAU,QAAA,CAAS,wBAAwB;gBAEvE,IAAI,CAAC,qBAAqB;oBACxB,MAAM,aAAa,IAAI,YAAY,oBAAoB,aAAa;oBACpE,UAAU,gBAAA,CAAiB,oBAAoB,gBAAgB;oBAC/D,UAAU,aAAA,CAAc,UAAU;oBAClC,IAAI,CAAC,WAAW,gBAAA,EAAkB;wBAChC,WAAW,YAAY,sBAAsB,SAAS,CAAC,GAAG;4BAAE,QAAQ;wBAAK,CAAC;wBAC1E,IAAI,SAAS,aAAA,KAAkB,0BAA0B;4BACvD,MAAM,SAAS;wBACjB;oBACF;gBACF;gBAEA;4CAAO,MAAM;wBACX,UAAU,mBAAA,CAAoB,oBAAoB,gBAAgB;wBAKlE;oDAAW,MAAM;gCACf,MAAM,eAAe,IAAI,YAAY,sBAAsB,aAAa;gCACxE,UAAU,gBAAA,CAAiB,sBAAsB,kBAAkB;gCACnE,UAAU,aAAA,CAAc,YAAY;gCACpC,IAAI,CAAC,aAAa,gBAAA,EAAkB;oCAClC,MAAM,4BAA4B,SAAS,IAAA,EAAM;wCAAE,QAAQ;oCAAK,CAAC;gCACnE;gCAEA,UAAU,mBAAA,CAAoB,sBAAsB,kBAAkB;gCAEtE,iBAAiB,MAAA,CAAO,UAAU;4BACpC;mDAAG,CAAC;oBACN;;YACF;QACF;+BAAG;QAAC;QAAW;QAAkB;QAAoB,UAAU;KAAC;IAGhE,MAAM,kLAAsB,cAAA;iDAC1B,CAAC,UAA+B;YAC9B,IAAI,CAAC,QAAQ,CAAC,QAAS,CAAA;YACvB,IAAI,WAAW,MAAA,CAAQ,CAAA;YAEvB,MAAM,WAAW,MAAM,GAAA,KAAQ,SAAS,CAAC,MAAM,MAAA,IAAU,CAAC,MAAM,OAAA,IAAW,CAAC,MAAM,OAAA;YAClF,MAAM,iBAAiB,SAAS,aAAA;YAEhC,IAAI,YAAY,gBAAgB;gBAC9B,MAAME,aAAY,MAAM,aAAA;gBACxB,MAAM,CAAC,OAAO,IAAI,CAAA,GAAI,iBAAiBA,UAAS;gBAChD,MAAM,4BAA4B,SAAS;gBAG3C,IAAI,CAAC,2BAA2B;oBAC9B,IAAI,mBAAmBA,WAAW,CAAA,MAAM,cAAA,CAAe;gBACzD,OAAO;oBACL,IAAI,CAAC,MAAM,QAAA,IAAY,mBAAmB,MAAM;wBAC9C,MAAM,cAAA,CAAe;wBACrB,IAAI,KAAM,CAAA,MAAM,OAAO;4BAAE,QAAQ;wBAAK,CAAC;oBACzC,OAAA,IAAW,MAAM,QAAA,IAAY,mBAAmB,OAAO;wBACrD,MAAM,cAAA,CAAe;wBACrB,IAAI,KAAM,CAAA,MAAM,MAAM;4BAAE,QAAQ;wBAAK,CAAC;oBACxC;gBACF;YACF;QACF;gDACA;QAAC;QAAM;QAAS,WAAW,MAAM;KAAA;IAGnC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAc,UAAU,CAAA;QAAK,GAAG,UAAA;QAAY,KAAK;QAAc,WAAW;IAAA,CAAe;AAE9F,CAAC;AAED,WAAW,WAAA,GAAc;AAUzB,SAAS,WAAW,UAAA,EAA2B,EAAE,SAAS,KAAA,CAAM,CAAA,GAAI,CAAC,CAAA,EAAG;IACtE,MAAM,2BAA2B,SAAS,aAAA;IAC1C,KAAA,MAAW,aAAa,WAAY;QAClC,MAAM,WAAW;YAAE;QAAO,CAAC;QAC3B,IAAI,SAAS,aAAA,KAAkB,yBAA0B,CAAA;IAC3D;AACF;AAKA,SAAS,iBAAiB,SAAA,EAAwB;IAChD,MAAM,aAAa,sBAAsB,SAAS;IAClD,MAAM,QAAQ,YAAY,YAAY,SAAS;IAC/C,MAAM,OAAO,YAAY,WAAW,OAAA,CAAQ,GAAG,SAAS;IACxD,OAAO;QAAC;QAAO,IAAI;KAAA;AACrB;AAYA,SAAS,sBAAsB,SAAA,EAAwB;IACrD,MAAM,QAAuB,CAAC,CAAA;IAC9B,MAAM,SAAS,SAAS,gBAAA,CAAiB,WAAW,WAAW,YAAA,EAAc;QAC3E,YAAY,CAAC,SAAc;YACzB,MAAM,gBAAgB,KAAK,OAAA,KAAY,WAAW,KAAK,IAAA,KAAS;YAChE,IAAI,KAAK,QAAA,IAAY,KAAK,MAAA,IAAU,cAAe,CAAA,OAAO,WAAW,WAAA;YAIrE,OAAO,KAAK,QAAA,IAAY,IAAI,WAAW,aAAA,GAAgB,WAAW,WAAA;QACpE;IACF,CAAC;IACD,MAAO,OAAO,QAAA,CAAS,EAAG,MAAM,IAAA,CAAK,OAAO,WAA0B;IAGtE,OAAO;AACT;AAMA,SAAS,YAAY,QAAA,EAAyB,SAAA,EAAwB;IACpE,KAAA,MAAW,WAAW,SAAU;QAE9B,IAAI,CAAC,SAAS,SAAS;YAAE,MAAM;QAAU,CAAC,EAAG,CAAA,OAAO;IACtD;AACF;AAEA,SAAS,SAAS,IAAA,EAAmB,EAAE,IAAA,CAAK,CAAA,EAA2B;IACrE,IAAI,iBAAiB,IAAI,EAAE,UAAA,KAAe,SAAU,CAAA,OAAO;IAC3D,MAAO,KAAM;QAEX,IAAI,SAAS,KAAA,KAAa,SAAS,KAAM,CAAA,OAAO;QAChD,IAAI,iBAAiB,IAAI,EAAE,OAAA,KAAY,OAAQ,CAAA,OAAO;QACtD,OAAO,KAAK,aAAA;IACd;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,OAAA,EAAmE;IAC5F,OAAO,mBAAmB,oBAAoB,YAAY;AAC5D;AAEA,SAAS,MAAM,OAAA,EAAkC,EAAE,SAAS,KAAA,CAAM,CAAA,GAAI,CAAC,CAAA,EAAG;IAExE,IAAI,WAAW,QAAQ,KAAA,EAAO;QAC5B,MAAM,2BAA2B,SAAS,aAAA;QAE1C,QAAQ,KAAA,CAAM;YAAE,eAAe;QAAK,CAAC;QAErC,IAAI,YAAY,4BAA4B,kBAAkB,OAAO,KAAK,QACxE,QAAQ,MAAA,CAAO;IACnB;AACF;AAOA,IAAM,mBAAmB,uBAAuB;AAEhD,SAAS,yBAAyB;IAEhC,IAAI,QAAyB,CAAC,CAAA;IAE9B,OAAO;QACL,KAAI,UAAA,EAA2B;YAE7B,MAAM,mBAAmB,KAAA,CAAM,CAAC,CAAA;YAChC,IAAI,eAAe,kBAAkB;gBACnC,kBAAkB,MAAM;YAC1B;YAEA,QAAQ,YAAY,OAAO,UAAU;YACrC,MAAM,OAAA,CAAQ,UAAU;QAC1B;QAEA,QAAO,UAAA,EAA2B;YAChC,QAAQ,YAAY,OAAO,UAAU;YACrC,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO;QACnB;IACF;AACF;AAEA,SAAS,YAAe,KAAA,EAAY,IAAA,EAAS;IAC3C,MAAM,eAAe,CAAC;WAAG,KAAK;KAAA;IAC9B,MAAM,QAAQ,aAAa,OAAA,CAAQ,IAAI;IACvC,IAAI,UAAU,CAAA,GAAI;QAChB,aAAa,MAAA,CAAO,OAAO,CAAC;IAC9B;IACA,OAAO;AACT;AAEA,SAAS,YAAY,KAAA,EAAsB;IACzC,OAAO,MAAM,MAAA,CAAO,CAAC,OAAS,KAAK,OAAA,KAAY,GAAG;AACpD;AAEA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-id/src/id.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We spaces with `.trim().toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)[' useId '.trim().toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;;;AAGhC,IAAM,aAAc,6JAAA,CAAc,UAAU,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA,IAAA,CAAM,IAAM,KAAA,CAAA;AACzE,IAAI,QAAQ;AAEZ,SAAS,MAAM,eAAA,EAAkC;IAC/C,MAAM,CAAC,IAAI,KAAK,CAAA,GAAU,8JAAA,QAAA,CAA6B,WAAW,CAAC;IAEnE,CAAA,GAAA,sLAAA,CAAA,kBAAA;iCAAgB,MAAM;YACpB,IAAI,CAAC,gBAAiB,CAAA;yCAAM,CAAC,UAAY,WAAW,OAAO,OAAO,CAAC;;QACrE;gCAAG;QAAC,eAAe;KAAC;IACpB,OAAO,mBAAA,CAAoB,KAAK,CAAA,MAAA,EAAS,EAAE,EAAA,GAAK,EAAA;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-arrow/src/arrow.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ComponentRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AAwBQ;;;;AAlBlC,IAAM,OAAO;AAMb,IAAM,0KAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,MAAM,EAAE,QAAA,EAAU,QAAQ,EAAA,EAAI,SAAS,CAAA,EAAG,GAAG,WAAW,CAAA,GAAI;IAC5D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACE,GAAG,UAAA;QACJ,KAAK;QACL;QACA;QACA,SAAQ;QACR,qBAAoB;QAGnB,UAAA,MAAM,OAAA,GAAU,WAAW,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YAAQ,QAAO;QAAA,CAAiB;IAAA;AAGnE,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-popper/src/popper.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ComponentRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n"], "names": ["PopperArrow", "Root"], "mappings": ";;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB;;AAWA,YAAY,oBAAoB;AAChC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,eAAe;AAkCpB;;;;;;;;;;;;AA5BJ,IAAM,eAAe;IAAC;IAAO;IAAS;IAAU,MAAM;CAAA;AACtD,IAAM,gBAAgB;IAAC;IAAS;IAAU,KAAK;CAAA;AAS/C,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAM/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAK9F,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EAAE,aAAA,EAAe,QAAA,CAAS,CAAA,GAAI;IACpC,MAAM,CAAC,QAAQ,SAAS,CAAA,oKAAU,YAAA,EAA4B,IAAI;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QAAgB,gBAAgB;QACnE;IAAA,CACH;AAEJ;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,cAAc;AAQpB,IAAM,iLAAqB,aAAA,EACzB,CAAC,OAAuC,iBAAiB;IACvD,MAAM,EAAE,aAAA,EAAe,UAAA,EAAY,GAAG,YAAY,CAAA,GAAI;IACtD,MAAM,UAAU,iBAAiB,aAAa,aAAa;IAC3D,MAAM,wKAAY,SAAA,EAA4B,IAAI;IAClD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;IAEhD,8KAAA;kCAAU,MAAM;YAIpB,QAAQ,cAAA,CAAe,YAAY,WAAW,IAAI,OAAO;QAC3D,CAAC;;IAED,OAAO,aAAa,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;QAAe,GAAG,WAAA;QAAa,KAAK;IAAA,CAAc;AAChF;AAGF,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAUrB,IAAM,CAAC,uBAAuB,iBAAiB,CAAA,GAC7C,oBAA+C,YAAY;AAoB7D,IAAM,kLAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EACJ,aAAA,EACA,OAAO,QAAA,EACP,aAAa,CAAA,EACb,QAAQ,QAAA,EACR,cAAc,CAAA,EACd,eAAe,CAAA,EACf,kBAAkB,IAAA,EAClB,oBAAoB,CAAC,CAAA,EACrB,kBAAkB,uBAAuB,CAAA,EACzC,SAAS,SAAA,EACT,mBAAmB,KAAA,EACnB,yBAAyB,WAAA,EACzB,QAAA,EACA,GAAG,cACL,GAAI;IAEJ,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAE5D,MAAM,CAAC,SAAS,UAAU,CAAA,qKAAU,WAAA,EAAgC,IAAI;IACxE,MAAM,eAAe,qMAAA,EAAgB;uDAAc,CAAC,OAAS,WAAW,IAAI,CAAC;;IAE7E,MAAM,CAAC,OAAO,QAAQ,CAAA,oKAAU,YAAA,EAAiC,IAAI;IACrE,MAAM,2LAAY,UAAA,EAAQ,KAAK;IAC/B,MAAM,aAAa,WAAW,SAAS;IACvC,MAAM,cAAc,WAAW,UAAU;IAEzC,MAAM,mBAAoB,OAAA,CAAQ,UAAU,WAAW,MAAM,QAAQ,EAAA;IAErE,MAAM,mBACJ,OAAO,yBAAyB,WAC5B,uBACA;QAAE,KAAK;QAAG,OAAO;QAAG,QAAQ;QAAG,MAAM;QAAG,GAAG,oBAAA;IAAqB;IAEtE,MAAM,WAAW,MAAM,OAAA,CAAQ,iBAAiB,IAAI,oBAAoB;QAAC,iBAAiB;KAAA;IAC1F,MAAM,wBAAwB,SAAS,MAAA,GAAS;IAEhD,MAAM,wBAAwB;QAC5B,SAAS;QACT,UAAU,SAAS,MAAA,CAAO,SAAS;QAAA,iFAAA;QAEnC,aAAa;IACf;IAEA,MAAM,EAAE,IAAA,EAAM,cAAA,EAAgB,SAAA,EAAW,YAAA,EAAc,cAAA,CAAe,CAAA,IAAI,gOAAA,EAAY;QAAA,gGAAA;QAEpF,UAAU;QACV,WAAW;QACX,oBAAA;yCAAsB,CAAA,GAAI,SAAS;gBACjC,MAAM,2MAAU,aAAA,CAAW,IAAG,MAAM;oBAClC,gBAAgB,2BAA2B;gBAC7C,CAAC;gBACD,OAAO;YACT;;QACA,UAAU;YACR,WAAW,QAAQ,MAAA;QACrB;QACA,YAAY;+NACV,SAAA,EAAO;gBAAE,UAAU,aAAa;gBAAa,eAAe;YAAY,CAAC;YACzE,sOACE,QAAA,EAAM;gBACJ,UAAU;gBACV,WAAW;gBACX,SAAS,WAAW,gBAAY,4NAAA,CAAW,KAAI,KAAA;gBAC/C,GAAG,qBAAA;YACL,CAAC;YACH,sOAAmB,OAAA,EAAK;gBAAE,GAAG,qBAAA;YAAsB,CAAC;+NACpD,OAAA,EAAK;gBACH,GAAG,qBAAA;gBACH,KAAA;iDAAO,CAAC,EAAE,QAAA,EAAU,KAAA,EAAO,cAAA,EAAgB,eAAA,CAAgB,CAAA,KAAM;wBAC/D,MAAM,EAAE,OAAO,WAAA,EAAa,QAAQ,YAAA,CAAa,CAAA,GAAI,MAAM,SAAA;wBAC3D,MAAM,eAAe,SAAS,QAAA,CAAS,KAAA;wBACvC,aAAa,WAAA,CAAY,kCAAkC,GAAG,cAAc,CAAA,EAAA,CAAI;wBAChF,aAAa,WAAA,CAAY,mCAAmC,GAAG,eAAe,CAAA,EAAA,CAAI;wBAClF,aAAa,WAAA,CAAY,+BAA+B,GAAG,WAAW,CAAA,EAAA,CAAI;wBAC1E,aAAa,WAAA,CAAY,gCAAgC,GAAG,YAAY,CAAA,EAAA,CAAI;oBAC9E;;YACF,CAAC;YACD,4NAAS,QAAA,EAAgB;gBAAE,SAAS;gBAAO,SAAS;YAAa,CAAC;YAClE,gBAAgB;gBAAE;gBAAY;YAAY,CAAC;YAC3C,uOAAoB,OAAA,EAAK;gBAAE,UAAU;gBAAmB,GAAG,qBAAA;YAAsB,CAAC;SACpF;IACF,CAAC;IAED,MAAM,CAAC,YAAY,WAAW,CAAA,GAAI,6BAA6B,SAAS;IAExE,MAAM,mBAAe,uMAAA,EAAe,QAAQ;IAC5C,CAAA,GAAA,sLAAA,CAAA,kBAAA;yCAAgB,MAAM;YACpB,IAAI,cAAc;gBAChB,eAAe;YACjB;QACF;wCAAG;QAAC;QAAc,YAAY;KAAC;IAE/B,MAAM,SAAS,eAAe,KAAA,EAAO;IACrC,MAAM,SAAS,eAAe,KAAA,EAAO;IACrC,MAAM,oBAAoB,eAAe,KAAA,EAAO,iBAAiB;IAEjE,MAAM,CAAC,eAAe,gBAAgB,CAAA,IAAU,4KAAA,CAAiB;IACjE,CAAA,GAAA,sLAAA,CAAA,kBAAA;yCAAgB,MAAM;YACpB,IAAI,QAAS,CAAA,iBAAiB,OAAO,gBAAA,CAAiB,OAAO,EAAE,MAAM;QACvE;wCAAG;QAAC,OAAO;KAAC;IAEZ,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,OAAA;QACC,KAAK,KAAK,WAAA;QACV,qCAAkC;QAClC,OAAO;YACL,GAAG,cAAA;YACH,WAAW,eAAe,eAAe,SAAA,GAAY;YAAA,mCAAA;YACrD,UAAU;YACV,QAAQ;YACR,CAAC,iCAAwC,CAAA,EAAG;gBAC1C,eAAe,eAAA,EAAiB;gBAChC,eAAe,eAAA,EAAiB;aAClC,CAAE,IAAA,CAAK,GAAG;YAAA,qEAAA;YAAA,wEAAA;YAAA,6CAAA;YAKV,GAAI,eAAe,IAAA,EAAM,mBAAmB;gBAC1C,YAAY;gBACZ,eAAe;YACjB,CAAA;QACF;QAIA,KAAK,MAAM,GAAA;QAEX,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,uBAAA;YACC,OAAO;YACP;YACA,eAAe;YACf;YACA;YACA,iBAAiB;YAEjB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;gBACC,aAAW;gBACX,cAAY;gBACX,GAAG,YAAA;gBACJ,KAAK;gBACL,OAAO;oBACL,GAAG,aAAa,KAAA;oBAAA,0EAAA;oBAAA,gGAAA;oBAGhB,WAAW,CAAC,eAAe,SAAS,KAAA;gBACtC;YAAA;QACF;IACF;AAGN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,aAAa;AAEnB,IAAM,gBAAoC;IACxC,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR;AAMA,IAAM,gLAAoB,aAAA,EAAiD,SAASA,aAClF,KAAA,EACA,YAAA,EACA;IACA,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,iBAAiB,kBAAkB,YAAY,aAAa;IAClE,MAAM,WAAW,aAAA,CAAc,eAAe,UAAU,CAAA;IAExD,OAAA,+EAAA;IAAA,sDAAA;IAAA,sFAAA;IAIE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,QAAA;QACC,KAAK,eAAe,aAAA;QACpB,OAAO;YACL,UAAU;YACV,MAAM,eAAe,MAAA;YACrB,KAAK,eAAe,MAAA;YACpB,CAAC,QAAQ,CAAA,EAAG;YACZ,iBAAiB;gBACf,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,MAAM;YACR,CAAA,CAAE,eAAe,UAAU,CAAA;YAC3B,WAAW;gBACT,KAAK;gBACL,OAAO;gBACP,QAAQ,CAAA,cAAA,CAAA;gBACR,MAAM;YACR,CAAA,CAAE,eAAe,UAAU,CAAA;YAC3B,YAAY,eAAe,eAAA,GAAkB,WAAW,KAAA;QAC1D;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,uKAAgB,OAAA,EAAf;YACE,GAAG,UAAA;YACJ,KAAK;YACL,OAAO;gBACL,GAAG,WAAW,KAAA;gBAAA,oEAAA;gBAEd,SAAS;YACX;QAAA;IACF;AAGN,CAAC;AAED,YAAY,WAAA,GAAc;AAI1B,SAAS,UAAa,KAAA,EAA6B;IACjD,OAAO,UAAU;AACnB;AAEA,IAAM,kBAAkB,CAAC,UAAA,CAAsE;QAC7F,MAAM;QACN;QACA,IAAG,IAAA,EAAM;YACP,MAAM,EAAE,SAAA,EAAW,KAAA,EAAO,cAAA,CAAe,CAAA,GAAI;YAE7C,MAAM,oBAAoB,eAAe,KAAA,EAAO,iBAAiB;YACjE,MAAM,gBAAgB;YACtB,MAAM,aAAa,gBAAgB,IAAI,QAAQ,UAAA;YAC/C,MAAM,cAAc,gBAAgB,IAAI,QAAQ,WAAA;YAEhD,MAAM,CAAC,YAAY,WAAW,CAAA,GAAI,6BAA6B,SAAS;YACxE,MAAM,eAAe;gBAAE,OAAO;gBAAM,QAAQ;gBAAO,KAAK;YAAO,CAAA,CAAE,WAAW,CAAA;YAE5E,MAAM,eAAA,CAAgB,eAAe,KAAA,EAAO,KAAK,CAAA,IAAK,aAAa;YACnE,MAAM,eAAA,CAAgB,eAAe,KAAA,EAAO,KAAK,CAAA,IAAK,cAAc;YAEpE,IAAI,IAAI;YACR,IAAI,IAAI;YAER,IAAI,eAAe,UAAU;gBAC3B,IAAI,gBAAgB,eAAe,GAAG,YAAY,CAAA,EAAA,CAAA;gBAClD,IAAI,GAAG,CAAC,WAAW,CAAA,EAAA,CAAA;YACrB,OAAA,IAAW,eAAe,OAAO;gBAC/B,IAAI,gBAAgB,eAAe,GAAG,YAAY,CAAA,EAAA,CAAA;gBAClD,IAAI,GAAG,MAAM,QAAA,CAAS,MAAA,GAAS,WAAW,CAAA,EAAA,CAAA;YAC5C,OAAA,IAAW,eAAe,SAAS;gBACjC,IAAI,GAAG,CAAC,WAAW,CAAA,EAAA,CAAA;gBACnB,IAAI,gBAAgB,eAAe,GAAG,YAAY,CAAA,EAAA,CAAA;YACpD,OAAA,IAAW,eAAe,QAAQ;gBAChC,IAAI,GAAG,MAAM,QAAA,CAAS,KAAA,GAAQ,WAAW,CAAA,EAAA,CAAA;gBACzC,IAAI,gBAAgB,eAAe,GAAG,YAAY,CAAA,EAAA,CAAA;YACpD;YACA,OAAO;gBAAE,MAAM;oBAAE;oBAAG;gBAAE;YAAE;QAC1B;IACF,CAAA;AAEA,SAAS,6BAA6B,SAAA,EAAsB;IAC1D,MAAM,CAAC,MAAM,QAAQ,QAAQ,CAAA,GAAI,UAAU,KAAA,CAAM,GAAG;IACpD,OAAO;QAAC;QAAc,KAAc;KAAA;AACtC;AAEA,IAAMC,QAAO;AACb,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-portal/src/portal.tsx"], "sourcesContent": ["import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,OAAO,cAAc;AACrB,SAAS,iBAAiB;AAC1B,SAAS,uBAAuB;AAuBJ;;;;;;;AAjB5B,IAAM,cAAc;AAWpB,IAAM,2KAAe,aAAA,EAAuC,CAAC,OAAO,iBAAiB;IACnF,MAAM,EAAE,WAAW,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IACrD,MAAM,CAAC,SAAS,UAAU,CAAA,qKAAU,WAAA,EAAS,KAAK;IAClD,CAAA,GAAA,sLAAA,CAAA,kBAAA;kCAAgB,IAAM,WAAW,IAAI;iCAAG,CAAC,CAAC;IAC1C,MAAM,YAAY,iBAAkB,WAAW,YAAY,UAAU;IACrE,OAAO,iLACH,UAAA,CAAS,YAAA,CAAa,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,WAAA;QAAa,KAAK;IAAA,CAAc,GAAI,SAAS,IACtF;AACN,CAAC;AAED,OAAO,WAAA,GAAc;AAIrB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-roving-focus/src/roving-focus-group.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,aAAa;AACtB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AACrC,SAAS,oBAAoB;AAgEnB;;;;;;;;;;;;;AA5DV,IAAM,cAAc;AACpB,IAAM,gBAAgB;IAAE,SAAS;IAAO,YAAY;AAAK;AAMzD,IAAM,aAAa;AAGnB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,iLAAI,mBAAA,EAGzD,UAAU;AAGZ,IAAM,CAAC,+BAA+B,2BAA2B,CAAA,8KAAI,qBAAA,EACnE,YACA;IAAC,qBAAqB;CAAA;AA+BxB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,8BAAkD,UAAU;AAK9D,IAAM,qLAAyB,aAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,uBAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO,MAAM,uBAAA;YAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;gBAAsB,GAAG,KAAA;gBAAO,KAAK;YAAA,CAAc;QAAA,CACtD;IAAA,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAgB/B,IAAM,yLAA6B,aAAA,EAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,uBAAA,EACA,WAAA,EACA,OAAO,KAAA,EACP,GAAA,EACA,kBAAkB,oBAAA,EAClB,uBAAA,EACA,wBAAA,EACA,YAAA,EACA,4BAA4B,KAAA,EAC5B,GAAG,YACL,GAAI;IACJ,MAAM,wKAAY,SAAA,EAAoC,IAAI;IAC1D,MAAM,mBAAe,iMAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,yLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,mMAAI,uBAAA,EAAqB;QACnE,MAAM;QACN,aAAa,2BAA2B;QACxC,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,oKAAU,YAAA,EAAS,KAAK;IACpE,MAAM,6MAAmB,iBAAA,EAAe,YAAY;IACpD,MAAM,WAAW,cAAc,uBAAuB;IACtD,MAAM,oLAAwB,SAAA,EAAO,KAAK;IAC1C,MAAM,CAAC,qBAAqB,sBAAsB,CAAA,IAAU,4KAAA,EAAS,CAAC;sKAEhE,YAAA;0CAAU,MAAM;YACpB,MAAM,OAAO,IAAI,OAAA;YACjB,IAAI,MAAM;gBACR,KAAK,gBAAA,CAAiB,aAAa,gBAAgB;gBACnD;sDAAO,IAAM,KAAK,mBAAA,CAAoB,aAAa,gBAAgB;;YACrE;QACF;yCAAG;QAAC,gBAAgB;KAAC;IAErB,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA,+KAAmB,cAAA;gDACjB,CAAC,YAAc,oBAAoB,SAAS;+CAC5C;YAAC,mBAAmB;SAAA;QAEtB,kLAAsB,cAAA;gDAAY,IAAM,oBAAoB,IAAI;+CAAG,CAAC,CAAC;QACrE,oBAA0B,gLAAA;gDACxB,IAAM;wDAAuB,CAAC,YAAc,YAAY,CAAC;;+CACzD,CAAC,CAAA;QAEH,yLAA6B,cAAA;gDAC3B,IAAM;wDAAuB,CAAC,YAAc,YAAY,CAAC;;+CACzD,CAAC,CAAA;QAGH,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,UAAU,oBAAoB,wBAAwB,IAAI,CAAA,IAAK;YAC/D,oBAAkB;YACjB,GAAG,UAAA;YACJ,KAAK;YACL,OAAO;gBAAE,SAAS;gBAAQ,GAAG,MAAM,KAAA;YAAM;YACzC,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,MAAM;gBACzD,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,aAAS,uLAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;gBAKtD,MAAM,kBAAkB,CAAC,gBAAgB,OAAA;gBAEzC,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,IAAiB,mBAAmB,CAAC,kBAAkB;oBAChF,MAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;oBAClE,MAAM,aAAA,CAAc,aAAA,CAAc,eAAe;oBAEjD,IAAI,CAAC,gBAAgB,gBAAA,EAAkB;wBACrC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;wBACxD,MAAM,aAAa,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,MAAM;wBACnD,MAAM,cAAc,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,EAAA,KAAO,gBAAgB;wBACrE,MAAM,iBAAiB;4BAAC;4BAAY,aAAa;+BAAG,KAAK;yBAAA,CAAE,MAAA,CACzD;wBAEF,MAAM,iBAAiB,eAAe,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;wBACrE,WAAW,gBAAgB,yBAAyB;oBACtD;gBACF;gBAEA,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,4KAAQ,uBAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,oBAAoB,KAAK,CAAC;QAAA;IAC7E;AAGN,CAAC;AAMD,IAAM,YAAY;AAalB,IAAM,yLAA6B,aAAA,EACjC,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,uBAAA,EACA,YAAY,IAAA,EACZ,SAAS,KAAA,EACT,SAAA,EACA,QAAA,EACA,GAAG,WACL,GAAI;IACJ,MAAM,+KAAS,QAAA,CAAM;IACrB,MAAM,KAAK,aAAa;IACxB,MAAM,UAAU,sBAAsB,WAAW,uBAAuB;IACxE,MAAM,mBAAmB,QAAQ,gBAAA,KAAqB;IACtD,MAAM,WAAW,cAAc,uBAAuB;IAEtD,MAAM,EAAE,kBAAA,EAAoB,qBAAA,EAAuB,gBAAA,CAAiB,CAAA,GAAI;QAElE,0KAAA;0CAAU,MAAM;YACpB,IAAI,WAAW;gBACb,mBAAmB;gBACnB;sDAAO,IAAM,sBAAsB;;YACrC;QACF;yCAAG;QAAC;QAAW;QAAoB,qBAAqB;KAAC;IAEzD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;YACC,UAAU,mBAAmB,IAAI,CAAA;YACjC,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;YACL,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,UAAW,CAAA,MAAM,cAAA,CAAe;qBAEhC,QAAQ,WAAA,CAAY,EAAE;YAC7B,CAAC;YACD,UAAS,0LAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,WAAA,CAAY,EAAE,CAAC;YAC1E,+KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,MAAM,GAAA,KAAQ,SAAS,MAAM,QAAA,EAAU;oBACzC,QAAQ,cAAA,CAAe;oBACvB;gBACF;gBAEA,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,CAAe,CAAA;gBAE1C,MAAM,cAAc,eAAe,OAAO,QAAQ,WAAA,EAAa,QAAQ,GAAG;gBAE1E,IAAI,gBAAgB,KAAA,GAAW;oBAC7B,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,QAAA,CAAU,CAAA;oBACtE,MAAM,cAAA,CAAe;oBACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;oBACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;oBAE1D,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;yBAAA,IAC1C,gBAAgB,UAAU,gBAAgB,QAAQ;wBACzD,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;wBACnD,MAAM,eAAe,eAAe,OAAA,CAAQ,MAAM,aAAa;wBAC/D,iBAAiB,QAAQ,IAAA,GACrB,UAAU,gBAAgB,eAAe,CAAC,IAC1C,eAAe,KAAA,CAAM,eAAe,CAAC;oBAC3C;oBAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gBAC7C;YACF,CAAC;YAEA,UAAA,OAAO,aAAa,aACjB,SAAS;gBAAE;gBAAkB,YAAY,oBAAoB;YAAK,CAAC,IACnE;QAAA;IACN;AAGN;AAGF,qBAAqB,WAAA,GAAc;AAKnC,IAAM,0BAAuD;IAC3D,WAAW;IAAQ,SAAS;IAC5B,YAAY;IAAQ,WAAW;IAC/B,QAAQ;IAAS,MAAM;IACvB,UAAU;IAAQ,KAAK;AACzB;AAEA,SAAS,qBAAqB,GAAA,EAAa,GAAA,EAAiB;IAC1D,IAAI,QAAQ,MAAO,CAAA,OAAO;IAC1B,OAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AAIA,SAAS,eAAe,KAAA,EAA4B,WAAA,EAA2B,GAAA,EAAiB;IAC9F,MAAM,MAAM,qBAAqB,MAAM,GAAA,EAAK,GAAG;IAC/C,IAAI,gBAAgB,cAAc;QAAC;QAAa,YAAY;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACpF,IAAI,gBAAgB,gBAAgB;QAAC;QAAW,WAAW;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACnF,OAAO,uBAAA,CAAwB,GAAG,CAAA;AACpC;AAEA,SAAS,WAAW,UAAA,EAA2B,gBAAgB,KAAA,EAAO;IACpE,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;YAAE;QAAc,CAAC;QACjC,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAM,OAAO;AACb,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-menu/src/menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ComponentRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst Slot = createSlot('MenuContent.ScrollLock');\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n                preventScrollOnEntryFocus\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ComponentRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ComponentRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n"], "names": ["Root", "<PERSON><PERSON>", "Content", "<PERSON><PERSON>", "Arrow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,iBAAiB,mBAAmB;AAC7C,SAAS,0BAA0B;AACnC,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AACjC,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,aAAa;AACtB,YAAY,qBAAqB;AAEjC,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,WAAW,mCAAmC;AACvD,YAAY,sBAAsB;AAElC,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAoGrB;;;;;;;;;;;;;;;;;;;;;;;;AA9FR,IAAM,iBAAiB;IAAC;IAAS,GAAG;CAAA;AACpC,IAAM,aAAa;IAAC;IAAa;IAAU,MAAM;CAAA;AACjD,IAAM,YAAY;IAAC;IAAW;IAAY,KAAK;CAAA;AAC/C,IAAM,kBAAkB,CAAC;OAAG,YAAY;OAAG,SAAS;CAAA;AACpD,IAAM,gBAA6C;IACjD,KAAK,CAAC;WAAG;QAAgB,YAAY;KAAA;IACrC,KAAK,CAAC;WAAG;QAAgB,WAAW;KAAA;AACtC;AACA,IAAM,iBAA8C;IAClD,KAAK;QAAC,WAAW;KAAA;IACjB,KAAK;QAAC,YAAY;KAAA;AACpB;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,gLAAI,oBAAA,EAGzD,SAAS;AAGX,IAAM,CAAC,mBAAmB,eAAe,CAAA,GAAI,gMAAA,EAAmB,WAAW;IACzE;0KACA,oBAAA;mLACA,8BAAA;CACD;AACD,IAAM,2LAAiB,oBAAA,CAAkB;AACzC,IAAM,8MAA2B,8BAAA,CAA4B;AAS7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AASpF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GAAI,kBAAwC,SAAS;AAUhG,IAAM,OAA4B,CAAC,UAAkC;IACnE,MAAM,EAAE,WAAA,EAAa,OAAO,KAAA,EAAO,QAAA,EAAU,GAAA,EAAK,YAAA,EAAc,QAAQ,IAAA,CAAK,CAAA,GAAI;IACjF,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,CAAC,SAAS,UAAU,CAAA,oKAAU,YAAA,EAAoC,IAAI;IAC5E,MAAM,uLAA2B,SAAA,EAAO,KAAK;IAC7C,MAAM,6MAAmB,iBAAA,EAAe,YAAY;IACpD,MAAM,yLAAY,eAAA,EAAa,GAAG;sKAE5B,YAAA;0BAAU,MAAM;YAGpB,MAAM;gDAAgB,MAAM;oBAC1B,mBAAmB,OAAA,GAAU;oBAC7B,SAAS,gBAAA,CAAiB,eAAe,eAAe;wBAAE,SAAS;wBAAM,MAAM;oBAAK,CAAC;oBACrF,SAAS,gBAAA,CAAiB,eAAe,eAAe;wBAAE,SAAS;wBAAM,MAAM;oBAAK,CAAC;gBACvF;;YACA,MAAM;gDAAgB,IAAO,mBAAmB,OAAA,GAAU;;YAC1D,SAAS,gBAAA,CAAiB,WAAW,eAAe;gBAAE,SAAS;YAAK,CAAC;YACrE;kCAAO,MAAM;oBACX,SAAS,mBAAA,CAAoB,WAAW,eAAe;wBAAE,SAAS;oBAAK,CAAC;oBACxE,SAAS,mBAAA,CAAoB,eAAe,eAAe;wBAAE,SAAS;oBAAK,CAAC;oBAC5E,SAAS,mBAAA,CAAoB,eAAe,eAAe;wBAAE,SAAS;oBAAK,CAAC;gBAC9E;;QACF;yBAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,OAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;YACC,OAAO;YACP;YACA,cAAc;YACd;YACA,iBAAiB;YAEjB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,kBAAA;gBACC,OAAO;gBACP,SAAe,gLAAA;wCAAY,IAAM,iBAAiB,KAAK;uCAAG;oBAAC,gBAAgB;iBAAC;gBAC5E;gBACA,KAAK;gBACL;gBAEC;YAAA;QACH;IACF,CACF;AAEJ;AAEA,KAAK,WAAA,GAAc;AAMnB,IAAM,cAAc;AAMpB,IAAM,+KAAmB,aAAA,EACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,WAAA,EAAa,GAAG,YAAY,CAAA,GAAI;IACxC,MAAM,cAAc,eAAe,WAAW;IAC9C,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,SAAA,EAAhB;QAAwB,GAAG,WAAA;QAAc,GAAG,WAAA;QAAa,KAAK;IAAA,CAAc;AACtF;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,kBAAsC,aAAa;IAC5F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,aAAwC,CAAC,UAAwC;IACrF,MAAM,EAAE,WAAA,EAAa,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IACzD,MAAM,UAAU,eAAe,aAAa,WAAW;IACvD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QAAe,OAAO;QAAa;QAClC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAC,SAAA,EAAA;gBAAgB,SAAO;gBAAC;gBACtB;YAAA,CACH;QAAA,CACF;IAAA,CACF;AAEJ;AAEA,WAAW,WAAA,GAAc;AAMzB,IAAM,eAAe;AAUrB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,kBAA2C,YAAY;AAgBzD,IAAM,+KAAoB,cAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;IACtE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;IAEtE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,WAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,WAAA;gBAC3B,UAAA,YAAY,KAAA,GACX,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;oBAAsB,GAAG,YAAA;oBAAc,KAAK;gBAAA,CAAc,IAE3D,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;oBAAyB,GAAG,YAAA;oBAAc,KAAK;gBAAA,CAAc;YAAA,CAElE;QAAA,CACF;IAAA,CACF;AAEJ;AASF,IAAM,yLAA6B,aAAA,EACjC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,wKAAY,SAAA,EAAmC,IAAI;IACzD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;sKAGhD,YAAA;0CAAU,MAAM;YACpB,MAAM,UAAU,IAAI,OAAA;YACpB,IAAI,QAAS,CAAA,uKAAO,cAAA,EAAW,OAAO;QACxC;yCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QAGL,WAAW,QAAQ,IAAA;QAGnB,6BAA6B,QAAQ,IAAA;QACrC,sBAAoB;QAGpB,gBAAgB,2LAAA,EACd,MAAM,cAAA,EACN,CAAC,QAAU,MAAM,cAAA,CAAe,GAChC;YAAE,0BAA0B;QAAM;QAEpC,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;IAAA;AAGjD;AAGF,IAAM,2LAAgC,cAAA,EAGpC,CAAC,OAA8C,iBAAiB;IAChE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,sBAAsB;QACtB,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;IAAA;AAGjD,CAAC;AAgDD,IAAM,QAAO,oLAAA,EAAW,wBAAwB;AAEhD,IAAM,oLAAwB,aAAA,EAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,WAAA,EACA,OAAO,KAAA,EACP,SAAA,EACA,eAAA,EACA,gBAAA,EACA,2BAAA,EACA,YAAA,EACA,eAAA,EACA,oBAAA,EACA,cAAA,EACA,iBAAA,EACA,SAAA,EACA,oBAAA,EACA,GAAG,cACL,GAAI;IACJ,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,cAAc,mBAAmB,cAAc,WAAW;IAChE,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,WAAW,cAAc,WAAW;IAC1C,MAAM,CAAC,eAAe,gBAAgB,CAAA,qKAAU,WAAA,EAAwB,IAAI;IAC5E,MAAM,cAAmB,0KAAA,EAAuB,IAAI;IACpD,MAAM,iMAAe,mBAAA,EAAgB,cAAc,YAAY,QAAQ,eAAe;IACtF,MAAM,6KAAiB,SAAA,EAAO,CAAC;IAC/B,MAAM,8KAAkB,SAAA,EAAO,EAAE;IACjC,MAAM,uBAA6B,2KAAA,EAAO,CAAC;IAC3C,MAAM,0LAA8B,SAAA,EAA2B,IAAI;IACnE,MAAM,gBAAsB,2KAAA,EAAa,OAAO;IAChD,MAAM,oLAAwB,SAAA,EAAO,CAAC;IAEtC,MAAM,oBAAoB,uBAAuB,wOAAA,iKAAqB,WAAA;IACtE,MAAM,yBAAyB,uBAC3B;QAAE,IAAI;QAAM,gBAAgB;IAAK,IACjC,KAAA;IAEJ,MAAM,wBAAwB,CAAC,QAAgB;QAC7C,MAAM,SAAS,UAAU,OAAA,GAAU;QACnC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;QACxD,MAAM,cAAc,SAAS,aAAA;QAC7B,MAAM,eAAe,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,WAAW,GAAG;QAC7E,MAAM,SAAS,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,SAAS;QACjD,MAAM,YAAY,aAAa,QAAQ,QAAQ,YAAY;QAC3D,MAAM,UAAU,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,SAAA,KAAc,SAAS,GAAG,IAAI;QAGxE,CAAC,SAAS,aAAa,KAAA,EAAe;YACpC,UAAU,OAAA,GAAU;YACpB,OAAO,YAAA,CAAa,SAAS,OAAO;YACpC,IAAI,UAAU,GAAI,CAAA,SAAS,OAAA,GAAU,OAAO,UAAA,CAAW,IAAM,aAAa,EAAE,GAAG,GAAI;QACrF,CAAA,EAAG,MAAM;QAET,IAAI,SAAS;YAKX,WAAW,IAAO,QAAwB,KAAA,CAAM,CAAC;QACnD;IACF;sKAEM,YAAA;qCAAU,MAAM;YACpB;6CAAO,IAAM,OAAO,YAAA,CAAa,SAAS,OAAO;;QACnD;oCAAG,CAAC,CAAC;IAIL,CAAA,GAAA,8KAAA,CAAA,iBAAA,CAAe;IAEf,MAAM,+BAAiC,4KAAA;iEAAY,CAAC,UAA8B;YAChF,MAAM,kBAAkB,cAAc,OAAA,KAAY,sBAAsB,OAAA,EAAS;YACjF,OAAO,mBAAmB,qBAAqB,OAAO,sBAAsB,OAAA,EAAS,IAAI;QAC3F;gEAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,+KAAmB,cAAA;2CACjB,CAAC,UAAU;gBACT,IAAI,yBAAyB,KAAK,EAAG,CAAA,MAAM,cAAA,CAAe;YAC5D;0CACA;YAAC,wBAAwB;SAAA;QAE3B,+KAAmB,cAAA;2CACjB,CAAC,UAAU;gBACT,IAAI,yBAAyB,KAAK,EAAG,CAAA;gBACrC,WAAW,OAAA,EAAS,MAAM;gBAC1B,iBAAiB,IAAI;YACvB;0CACA;YAAC,wBAAwB;SAAA;QAE3B,iLAAsB,eAAA;2CACpB,CAAC,UAAU;gBACT,IAAI,yBAAyB,KAAK,EAAG,CAAA,MAAM,cAAA,CAAe;YAC5D;0CACA;YAAC,wBAAwB;SAAA;QAE3B;QACA,8LAAkC,cAAA;2CAAY,CAAC,WAAW;gBACxD,sBAAsB,OAAA,GAAU;YAClC;0CAAG,CAAC,CAAC;QAEL,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;YAAmB,GAAG,sBAAA;YACrB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,gLAAC,aAAA,EAAA;gBACC,SAAO;gBACP,SAAS;gBACT,sLAAkB,uBAAA,EAAqB,iBAAiB,CAAC,UAAU;oBAGjE,MAAM,cAAA,CAAe;oBACrB,WAAW,OAAA,EAAS,MAAM;wBAAE,eAAe;oBAAK,CAAC;gBACnD,CAAC;gBACD,oBAAoB;gBAEpB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sLAAC,mBAAA,EAAA;oBACC,SAAO;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,iLAAkB,OAAA,EAAjB;wBACC,SAAO;wBACN,GAAG,qBAAA;wBACJ,KAAK,YAAY,GAAA;wBACjB,aAAY;wBACZ;wBACA,kBAAkB;wBAClB,0BAA0B;wBAC1B,kBAAc,uLAAA,EAAqB,cAAc,CAAC,UAAU;4BAE1D,IAAI,CAAC,YAAY,kBAAA,CAAmB,OAAA,CAAS,CAAA,MAAM,cAAA,CAAe;wBACpE,CAAC;wBACD,2BAAyB;wBAEzB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,UAAA,EAAhB;4BACC,MAAK;4BACL,oBAAiB;4BACjB,cAAY,aAAa,QAAQ,IAAI;4BACrC,2BAAwB;4BACxB,KAAK,YAAY,GAAA;4BAChB,GAAG,WAAA;4BACH,GAAG,YAAA;4BACJ,KAAK;4BACL,OAAO;gCAAE,SAAS;gCAAQ,GAAG,aAAa,KAAA;4BAAM;4BAChD,+KAAW,uBAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;gCAEjE,MAAM,SAAS,MAAM,MAAA;gCACrB,MAAM,kBACJ,OAAO,OAAA,CAAQ,2BAA2B,MAAM,MAAM,aAAA;gCACxD,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;gCAC7D,MAAM,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW;gCAC5C,IAAI,iBAAiB;oCAEnB,IAAI,MAAM,GAAA,KAAQ,MAAO,CAAA,MAAM,cAAA,CAAe;oCAC9C,IAAI,CAAC,iBAAiB,eAAgB,CAAA,sBAAsB,MAAM,GAAG;gCACvE;gCAEA,MAAM,UAAU,WAAW,OAAA;gCAC3B,IAAI,MAAM,MAAA,KAAW,QAAS,CAAA;gCAC9B,IAAI,CAAC,gBAAgB,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA;gCAC1C,MAAM,cAAA,CAAe;gCACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;gCACxD,MAAM,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;gCAC5D,IAAI,UAAU,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,eAAe,OAAA,CAAQ;gCAC1D,WAAW,cAAc;4BAC3B,CAAC;4BACD,4KAAQ,uBAAA,EAAqB,MAAM,MAAA,EAAQ,CAAC,UAAU;gCAEpD,IAAI,CAAC,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,MAAM,GAAG;oCAC/C,OAAO,YAAA,CAAa,SAAS,OAAO;oCACpC,UAAU,OAAA,GAAU;gCACtB;4BACF,CAAC;4BACD,mLAAe,uBAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;gCACnB,MAAM,SAAS,MAAM,MAAA;gCACrB,MAAM,qBAAqB,gBAAgB,OAAA,KAAY,MAAM,OAAA;gCAI7D,IAAI,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,KAAK,oBAAoB;oCAC9D,MAAM,SAAS,MAAM,OAAA,GAAU,gBAAgB,OAAA,GAAU,UAAU;oCACnE,cAAc,OAAA,GAAU;oCACxB,gBAAgB,OAAA,GAAU,MAAM,OAAA;gCAClC;4BACF,CAAC;wBACH;oBACF;gBACF;YACF;QACF,CACF;IAAA;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAMnB,IAAM,YAAkB,+KAAA,EACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAc,MAAK;QAAS,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AACxE;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,aAAa;AAKnB,IAAM,YAAkB,+KAAA,EACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC3D;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,YAAY;AAClB,IAAM,cAAc;AAOpB,IAAM,eAAiB,2KAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAW,KAAA,EAAO,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IACrD,MAAM,wKAAY,SAAA,EAAuB,IAAI;IAC7C,MAAM,cAAc,mBAAmB,WAAW,MAAM,WAAW;IACnE,MAAM,iBAAiB,sBAAsB,WAAW,MAAM,WAAW;IACzE,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,qLAAyB,SAAA,EAAO,KAAK;IAE3C,MAAM,eAAe,MAAM;QACzB,MAAM,WAAW,IAAI,OAAA;QACrB,IAAI,CAAC,YAAY,UAAU;YACzB,MAAM,kBAAkB,IAAI,YAAY,aAAa;gBAAE,SAAS;gBAAM,YAAY;YAAK,CAAC;YACxF,SAAS,gBAAA,CAAiB,aAAa,CAAC,QAAU,WAAW,KAAK,GAAG;gBAAE,MAAM;YAAK,CAAC;YACnF,CAAA,GAAA,wKAAA,CAAA,8BAAA,EAA4B,UAAU,eAAe;YACrD,IAAI,gBAAgB,gBAAA,EAAkB;gBACpC,iBAAiB,OAAA,GAAU;YAC7B,OAAO;gBACL,YAAY,OAAA,CAAQ;YACtB;QACF;IACF;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;QACE,GAAG,SAAA;QACJ,KAAK;QACL;QACA,UAAS,0LAAA,EAAqB,MAAM,OAAA,EAAS,YAAY;QACzD,eAAe,CAAC,UAAU;YACxB,MAAM,aAAA,GAAgB,KAAK;YAC3B,iBAAiB,OAAA,GAAU;QAC7B;QACA,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;YAI9D,IAAI,CAAC,iBAAiB,OAAA,CAAS,CAAA,MAAM,aAAA,EAAe,MAAM;QAC5D,CAAC;QACD,WAAW,2LAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;YAC1D,MAAM,gBAAgB,eAAe,SAAA,CAAU,OAAA,KAAY;YAC3D,IAAI,YAAa,iBAAiB,MAAM,GAAA,KAAQ,IAAM,CAAA;YACtD,IAAI,eAAe,QAAA,CAAS,MAAM,GAAG,GAAG;gBACtC,MAAM,aAAA,CAAc,KAAA,CAAM;gBAO1B,MAAM,cAAA,CAAe;YACvB;QACF,CAAC;IAAA;AAGP;AAGF,SAAS,WAAA,GAAc;AAUvB,IAAM,iLAAqB,aAAA,EACzB,CAAC,OAAuC,iBAAiB;IACvD,MAAM,EAAE,WAAA,EAAa,WAAW,KAAA,EAAO,SAAA,EAAW,GAAG,UAAU,CAAA,GAAI;IACnE,MAAM,iBAAiB,sBAAsB,WAAW,WAAW;IACnE,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,wKAAY,SAAA,EAAuB,IAAI;IAC7C,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,CAAC,WAAW,YAAY,CAAA,qKAAU,WAAA,EAAS,KAAK;IAGtD,MAAM,CAAC,aAAa,cAAc,CAAA,OAAU,yKAAA,EAAS,EAAE;sKACjD,YAAA;kCAAU,MAAM;YACpB,MAAM,WAAW,IAAI,OAAA;YACrB,IAAI,UAAU;gBACZ,eAAA,CAAgB,SAAS,WAAA,IAAe,EAAA,EAAI,IAAA,CAAK,CAAC;YACpD;QACF;iCAAG;QAAC,UAAU,QAAQ;KAAC;IAEvB,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA,WAAW,aAAa;QAExB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,iLAAkB,OAAA,EAAjB;YAAsB,SAAO;YAAE,GAAG,qBAAA;YAAuB,WAAW,CAAC;YACpE,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;gBACC,MAAK;gBACL,oBAAkB,YAAY,KAAK,KAAA;gBACnC,iBAAe,YAAY,KAAA;gBAC3B,iBAAe,WAAW,KAAK,KAAA;gBAC9B,GAAG,SAAA;gBACJ,KAAK;gBAYL,mLAAe,uBAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;oBACnB,IAAI,UAAU;wBACZ,eAAe,WAAA,CAAY,KAAK;oBAClC,OAAO;wBACL,eAAe,WAAA,CAAY,KAAK;wBAChC,IAAI,CAAC,MAAM,gBAAA,EAAkB;4BAC3B,MAAM,OAAO,MAAM,aAAA;4BACnB,KAAK,KAAA,CAAM;gCAAE,eAAe;4BAAK,CAAC;wBACpC;oBACF;gBACF,CAAC;gBAEH,oLAAgB,uBAAA,EACd,MAAM,cAAA,EACN,UAAU,CAAC,QAAU,eAAe,WAAA,CAAY,KAAK,CAAC;gBAExD,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,aAAa,IAAI,CAAC;gBACrE,4KAAQ,uBAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,aAAa,KAAK,CAAC;YAAA;QACtE,CACF;IAAA;AAGN;AAOF,IAAM,qBAAqB;AAY3B,IAAM,mBAAyB,+KAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,UAAU,KAAA,EAAO,eAAA,EAAiB,GAAG,kBAAkB,CAAA,GAAI;IACnE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,uBAAA;QAAsB,OAAO,MAAM,WAAA;QAAa;QAC/C,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,UAAA;YACC,MAAK;YACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;YAClD,GAAG,iBAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,OAAO;YACnC,8KAAU,uBAAA,EACR,kBAAkB,QAAA,EAClB,IAAM,kBAAkB,gBAAgB,OAAO,IAAI,OAAO,CAAC,OAAO,GAClE;gBAAE,0BAA0B;YAAM;QACpC;IACF,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,mBAAmB;AAEzB,IAAM,CAAC,oBAAoB,oBAAoB,CAAA,GAAI,kBACjD,kBACA;IAAE,OAAO,KAAA;IAAW,eAAe,KAAO,CAAD;AAAG;AAS9C,IAAM,qBAAuB,2KAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,KAAA,EAAO,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IAChD,MAAM,8MAAoB,iBAAA,EAAe,aAAa;IACtD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;QAAmB,OAAO,MAAM,WAAA;QAAa;QAAc,eAAe;QACzE,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,UAAA;YAAY,KAAK;QAAA,CAAc;IAAA,CAChD;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,kBAAkB;AAOxB,IAAM,kLAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,KAAA,EAAO,GAAG,eAAe,CAAA,GAAI;IACrC,MAAM,UAAU,qBAAqB,iBAAiB,MAAM,WAAW;IACvE,MAAM,UAAU,UAAU,QAAQ,KAAA;IAClC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,uBAAA;QAAsB,OAAO,MAAM,WAAA;QAAa;QAC/C,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,UAAA;YACC,MAAK;YACL,gBAAc;YACb,GAAG,cAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,OAAO;YACnC,UAAU,2LAAA,EACR,eAAe,QAAA,EACf,IAAM,QAAQ,aAAA,GAAgB,KAAK,GACnC;gBAAE,0BAA0B;YAAM;QACpC;IACF,CACF;AAEJ;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,sBAAsB;AAI5B,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GAAI,kBACvD,qBACA;IAAE,SAAS;AAAM;AAanB,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,WAAA,EAAa,UAAA,EAAY,GAAG,mBAAmB,CAAA,GAAI;IAC3D,MAAM,mBAAmB,wBAAwB,qBAAqB,WAAW;IACjF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QACC,SACE,cACA,gBAAgB,iBAAiB,OAAO,KACxC,iBAAiB,OAAA,KAAY;QAG/B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;YACE,GAAG,kBAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,iBAAiB,OAAO;QAAA;IACtD;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,iBAAiB;AAKvB,IAAM,kLAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,WAAA,EAAa,GAAG,eAAe,CAAA,GAAI;IAC3C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACC,MAAK;QACL,oBAAiB;QAChB,GAAG,cAAA;QACJ,KAAK;IAAA;AAGX;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,aAAa;AAMnB,IAAM,8KAAkB,aAAA,EACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,MAAM,cAAc,eAAe,WAAW;IAC9C,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAiB,QAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AACpF;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,WAAW;AASjB,IAAM,CAAC,iBAAiB,iBAAiB,CAAA,GAAI,kBAAuC,QAAQ;AAQ5F,IAAM,UAAkC,CAAC,UAAqC;IAC5E,MAAM,EAAE,WAAA,EAAa,QAAA,EAAU,OAAO,KAAA,EAAO,YAAA,CAAa,CAAA,GAAI;IAC9D,MAAM,oBAAoB,eAAe,UAAU,WAAW;IAC9D,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,CAAC,SAAS,UAAU,CAAA,qKAAU,WAAA,EAAuC,IAAI;IAC/E,MAAM,CAAC,SAAS,UAAU,CAAA,OAAU,yKAAA,EAAoC,IAAI;IAC5E,MAAM,6MAAmB,iBAAA,EAAe,YAAY;sKAG9C,YAAA;6BAAU,MAAM;YACpB,IAAI,kBAAkB,IAAA,KAAS,MAAO,CAAA,iBAAiB,KAAK;YAC5D;qCAAO,IAAM,iBAAiB,KAAK;;QACrC;4BAAG;QAAC,kBAAkB,IAAA;QAAM,gBAAgB;KAAC;IAE7C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAiB,6KAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;YACC,OAAO;YACP;YACA,cAAc;YACd;YACA,iBAAiB;YAEjB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAA;gBACC,OAAO;gBACP,iLAAW,QAAA,CAAM;gBACjB,iLAAW,QAAA,CAAM;gBACjB;gBACA,iBAAiB;gBAEhB;YAAA;QACH;IACF,CACF;AAEJ;AAEA,QAAQ,WAAA,GAAc;AAMtB,IAAM,mBAAmB;AAKzB,IAAM,qBAAuB,2KAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,UAAU,eAAe,kBAAkB,MAAM,WAAW;IAClE,MAAM,cAAc,mBAAmB,kBAAkB,MAAM,WAAW;IAC1E,MAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;IACxE,MAAM,iBAAiB,sBAAsB,kBAAkB,MAAM,WAAW;IAChF,MAAM,iLAAqB,SAAA,EAAsB,IAAI;IACrD,MAAM,EAAE,oBAAA,EAAsB,0BAAA,CAA2B,CAAA,GAAI;IAC7D,MAAM,QAAQ;QAAE,aAAa,MAAM,WAAA;IAAY;IAE/C,MAAM,mLAAuB,cAAA;sDAAY,MAAM;YAC7C,IAAI,aAAa,OAAA,CAAS,CAAA,OAAO,YAAA,CAAa,aAAa,OAAO;YAClE,aAAa,OAAA,GAAU;QACzB;qDAAG,CAAC,CAAC;QAEC,0KAAA;oCAAU,IAAM;mCAAgB;QAAC,cAAc;KAAC;sKAEhD,YAAA;oCAAU,MAAM;YACpB,MAAM,oBAAoB,qBAAqB,OAAA;YAC/C;4CAAO,MAAM;oBACX,OAAO,YAAA,CAAa,iBAAiB;oBACrC,2BAA2B,IAAI;gBACjC;;QACF;mCAAG;QAAC;QAAsB,0BAA0B;KAAC;IAErD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,YAAA;QAAW,SAAO;QAAE,GAAG,KAAA;QACtB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;YACC,IAAI,WAAW,SAAA;YACf,iBAAc;YACd,iBAAe,QAAQ,IAAA;YACvB,iBAAe,WAAW,SAAA;YAC1B,cAAY,aAAa,QAAQ,IAAI;YACpC,GAAG,KAAA;YACJ,wLAAK,cAAA,EAAY,cAAc,WAAW,eAAe;YAGzD,SAAS,CAAC,UAAU;gBAClB,MAAM,OAAA,GAAU,KAAK;gBACrB,IAAI,MAAM,QAAA,IAAY,MAAM,gBAAA,CAAkB,CAAA;gBAM9C,MAAM,aAAA,CAAc,KAAA,CAAM;gBAC1B,IAAI,CAAC,QAAQ,IAAA,CAAM,CAAA,QAAQ,YAAA,CAAa,IAAI;YAC9C;YACA,mBAAe,uLAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;gBACnB,eAAe,WAAA,CAAY,KAAK;gBAChC,IAAI,MAAM,gBAAA,CAAkB,CAAA;gBAC5B,IAAI,CAAC,MAAM,QAAA,IAAY,CAAC,QAAQ,IAAA,IAAQ,CAAC,aAAa,OAAA,EAAS;oBAC7D,eAAe,0BAAA,CAA2B,IAAI;oBAC9C,aAAa,OAAA,GAAU,OAAO,UAAA,CAAW,MAAM;wBAC7C,QAAQ,YAAA,CAAa,IAAI;wBACzB,eAAe;oBACjB,GAAG,GAAG;gBACR;YACF,CAAC;YAEH,gBAAgB,2LAAA,EACd,MAAM,cAAA,EACN,UAAU,CAAC,UAAU;gBACnB,eAAe;gBAEf,MAAM,cAAc,QAAQ,OAAA,EAAS,sBAAsB;gBAC3D,IAAI,aAAa;oBAEf,MAAM,OAAO,QAAQ,OAAA,EAAS,QAAQ;oBACtC,MAAM,YAAY,SAAS;oBAC3B,MAAM,QAAQ,YAAY,CAAA,IAAK;oBAC/B,MAAM,kBAAkB,WAAA,CAAY,YAAY,SAAS,OAAO,CAAA;oBAChE,MAAM,iBAAiB,WAAA,CAAY,YAAY,UAAU,MAAM,CAAA;oBAE/D,eAAe,0BAAA,CAA2B;wBACxC,MAAM;4BAAA,4DAAA;4BAAA,qCAAA;4BAGJ;gCAAE,GAAG,MAAM,OAAA,GAAU;gCAAO,GAAG,MAAM,OAAA;4BAAQ;4BAC7C;gCAAE,GAAG;gCAAiB,GAAG,YAAY,GAAA;4BAAI;4BACzC;gCAAE,GAAG;gCAAgB,GAAG,YAAY,GAAA;4BAAI;4BACxC;gCAAE,GAAG;gCAAgB,GAAG,YAAY,MAAA;4BAAO;4BAC3C;gCAAE,GAAG;gCAAiB,GAAG,YAAY,MAAA;4BAAO;yBAC9C;wBACA;oBACF,CAAC;oBAED,OAAO,YAAA,CAAa,qBAAqB,OAAO;oBAChD,qBAAqB,OAAA,GAAU,OAAO,UAAA,CACpC,IAAM,eAAe,0BAAA,CAA2B,IAAI,GACpD;gBAEJ,OAAO;oBACL,eAAe,cAAA,CAAe,KAAK;oBACnC,IAAI,MAAM,gBAAA,CAAkB,CAAA;oBAG5B,eAAe,0BAAA,CAA2B,IAAI;gBAChD;YACF,CAAC;YAEH,+KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,MAAM,gBAAgB,eAAe,SAAA,CAAU,OAAA,KAAY;gBAC3D,IAAI,MAAM,QAAA,IAAa,iBAAiB,MAAM,GAAA,KAAQ,IAAM,CAAA;gBAC5D,IAAI,aAAA,CAAc,YAAY,GAAG,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oBACtD,QAAQ,YAAA,CAAa,IAAI;oBAGzB,QAAQ,OAAA,EAAS,MAAM;oBAEvB,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,mBAAmB;AAezB,IAAM,mLAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;IACtE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,gBAAgB,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;IACtE,MAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;IACxE,MAAM,wKAAY,SAAA,EAA8B,IAAI;IACpD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,WAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,WAAA;gBAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAA;oBACC,IAAI,WAAW,SAAA;oBACf,mBAAiB,WAAW,SAAA;oBAC3B,GAAG,eAAA;oBACJ,KAAK;oBACL,OAAM;oBACN,MAAM,YAAY,GAAA,KAAQ,QAAQ,SAAS;oBAC3C,6BAA6B;oBAC7B,sBAAsB;oBACtB,WAAW;oBACX,iBAAiB,CAAC,UAAU;wBAE1B,IAAI,YAAY,kBAAA,CAAmB,OAAA,CAAS,CAAA,IAAI,OAAA,EAAS,MAAM;wBAC/D,MAAM,cAAA,CAAe;oBACvB;oBAGA,kBAAkB,CAAC,QAAU,MAAM,cAAA,CAAe;oBAClD,oLAAgB,uBAAA,EAAqB,MAAM,cAAA,EAAgB,CAAC,UAAU;wBAGpE,IAAI,MAAM,MAAA,KAAW,WAAW,OAAA,CAAS,CAAA,QAAQ,YAAA,CAAa,KAAK;oBACrE,CAAC;oBACD,qLAAiB,uBAAA,EAAqB,MAAM,eAAA,EAAiB,CAAC,UAAU;wBACtE,YAAY,OAAA,CAAQ;wBAEpB,MAAM,cAAA,CAAe;oBACvB,CAAC;oBACD,+KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;wBAE1D,MAAM,kBAAkB,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,MAAqB;wBAChF,MAAM,aAAa,cAAA,CAAe,YAAY,GAAG,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG;wBACrE,IAAI,mBAAmB,YAAY;4BACjC,QAAQ,YAAA,CAAa,KAAK;4BAE1B,WAAW,OAAA,EAAS,MAAM;4BAE1B,MAAM,cAAA,CAAe;wBACvB;oBACF,CAAC;gBAAA;YACH,CACF;QAAA,CACF;IAAA,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,aAAa,IAAA,EAAe;IACnC,OAAO,OAAO,SAAS;AACzB;AAEA,SAAS,gBAAgB,OAAA,EAAoD;IAC3E,OAAO,YAAY;AACrB;AAEA,SAAS,gBAAgB,OAAA,EAAuB;IAC9C,OAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;AAEA,SAAS,WAAW,UAAA,EAA2B;IAC7C,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;QAChB,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAmBA,SAAS,aAAa,MAAA,EAAkB,MAAA,EAAgB,YAAA,EAAuB;IAC7E,MAAM,aAAa,OAAO,MAAA,GAAS,KAAK,MAAM,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,CAAC,OAAS,SAAS,MAAA,CAAO,CAAC,CAAC;IAC7F,MAAM,mBAAmB,aAAa,MAAA,CAAO,CAAC,CAAA,GAAK;IACnD,MAAM,oBAAoB,eAAe,OAAO,OAAA,CAAQ,YAAY,IAAI,CAAA;IACxE,IAAI,gBAAgB,UAAU,QAAQ,KAAK,GAAA,CAAI,mBAAmB,CAAC,CAAC;IACpE,MAAM,sBAAsB,iBAAiB,MAAA,KAAW;IACxD,IAAI,oBAAqB,CAAA,gBAAgB,cAAc,MAAA,CAAO,CAAC,IAAM,MAAM,YAAY;IACvF,MAAM,YAAY,cAAc,IAAA,CAAK,CAAC,QACpC,MAAM,WAAA,CAAY,EAAE,UAAA,CAAW,iBAAiB,WAAA,CAAY,CAAC;IAE/D,OAAO,cAAc,eAAe,YAAY,KAAA;AAClD;AASA,SAAS,iBAAiB,KAAA,EAAc,OAAA,EAAkB;IACxD,MAAM,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,GAAI;IACjB,IAAI,SAAS;IACb,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,GAAS,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAI,IAAK;QACnE,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QAGd,MAAM,YAAc,KAAK,MAAQ,KAAK,KAAQ,IAAA,CAAK,KAAK,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,KAAK,EAAA,IAAM;QACrF,IAAI,UAAW,CAAA,SAAS,CAAC;IAC3B;IAEA,OAAO;AACT;AAEA,SAAS,qBAAqB,KAAA,EAA2B,IAAA,EAAgB;IACvE,IAAI,CAAC,KAAM,CAAA,OAAO;IAClB,MAAM,YAAY;QAAE,GAAG,MAAM,OAAA;QAAS,GAAG,MAAM,OAAA;IAAQ;IACvD,OAAO,iBAAiB,WAAW,IAAI;AACzC;AAEA,SAAS,UAAa,OAAA,EAAqE;IACzF,OAAO,CAAC,QAAW,MAAM,WAAA,KAAgB,UAAU,QAAQ,KAAK,IAAI,KAAA;AACtE;AAEA,IAAMA,QAAO;AACb,IAAMC,UAAS;AACf,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAMC,QAAO;AACb,IAAM,eAAe;AACrB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,YAAY;AAClB,IAAMC,SAAQ;AACd,IAAM,MAAM;AACZ,IAAM,aAAa;AACnB,IAAM,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-dropdown-menu/src/dropdown-menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: 'DropdownMenuSub',\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n"], "names": ["Root", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,iBAAiB;AAC1B,YAAY,mBAAmB;AAE/B,SAAS,aAAa;AAuEhB;;;;;;;;;;;;AA7DN,IAAM,qBAAqB;AAG3B,IAAM,CAAC,2BAA2B,uBAAuB,CAAA,8KAAI,qBAAA,EAC3D,oBACA;wKAAC,kBAAe;CAAA;AAElB,IAAM,uLAAe,kBAAA,CAAgB;AAYrC,IAAM,CAAC,sBAAsB,sBAAsB,CAAA,GACjD,0BAAoD,kBAAkB;AAWxE,IAAM,eAA4C,CAAC,UAA0C;IAC3F,MAAM,EACJ,mBAAA,EACA,QAAA,EACA,GAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,QAAQ,IAAA,EACV,GAAI;IACJ,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,aAAmB,2KAAA,EAA0B,IAAI;IACvD,MAAM,CAAC,MAAM,OAAO,CAAA,mMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;QACC,OAAO;QACP,iLAAW,QAAA,CAAM;QACjB;QACA,gLAAW,SAAA,CAAM;QACjB;QACA,cAAc;QACd,gLAAoB,cAAA;wCAAY,IAAM;gDAAQ,CAAC,WAAa,CAAC,QAAQ;;uCAAG;YAAC,OAAO;SAAC;QACjF;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,OAAA,EAAd;YAAoB,GAAG,SAAA;YAAW;YAAY,cAAc;YAAS;YAAU;YAC7E;QAAA,CACH;IAAA;AAGN;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAMrB,IAAM,sBAA4B,+KAAA,EAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,mBAAA,EAAqB,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,uBAAuB,cAAc,mBAAmB;IACxE,MAAM,YAAY,aAAa,mBAAmB;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,SAAA,EAAd;QAAqB,SAAO;QAAE,GAAG,SAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,IAAI,QAAQ,SAAA;YACZ,iBAAc;YACd,iBAAe,QAAQ,IAAA;YACvB,iBAAe,QAAQ,IAAA,GAAO,QAAQ,SAAA,GAAY,KAAA;YAClD,cAAY,QAAQ,IAAA,GAAO,SAAS;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACC,GAAG,YAAA;YACJ,KAAK,iMAAA,EAAY,cAAc,QAAQ,UAAU;YACjD,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gBAGlE,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,YAAA,CAAa;oBAGrB,IAAI,CAAC,QAAQ,IAAA,CAAM,CAAA,MAAM,cAAA,CAAe;gBAC1C;YACF,CAAC;YACD,eAAW,uLAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,SAAU,CAAA;gBACd,IAAI;oBAAC;oBAAS,GAAG;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,YAAA,CAAa;gBAC7D,IAAI,MAAM,GAAA,KAAQ,YAAa,CAAA,QAAQ,YAAA,CAAa,IAAI;gBAGxD,IAAI;oBAAC;oBAAS;oBAAK,WAAW;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,MAAM,cAAA,CAAe;YAC5E,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,cAAc;AAKpB,IAAM,qBAAwD,CAC5D,UACG;IACH,MAAM,EAAE,mBAAA,EAAqB,GAAG,YAAY,CAAA,GAAI;IAChD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,SAAA,EAAd;QAAsB,GAAG,SAAA;QAAY,GAAG,WAAA;IAAA,CAAa;AAC/D;AAEA,mBAAmB,WAAA,GAAc;AAMjC,IAAM,eAAe;AAMrB,IAAM,wLAA4B,aAAA,EAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,mBAAA,EAAqB,GAAG,aAAa,CAAA,GAAI;IACjD,MAAM,UAAU,uBAAuB,cAAc,mBAAmB;IACxE,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,4LAAgC,SAAA,EAAO,KAAK;IAElD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,UAAA,EAAd;QACC,IAAI,QAAQ,SAAA;QACZ,mBAAiB,QAAQ,SAAA;QACxB,GAAG,SAAA;QACH,GAAG,YAAA;QACJ,KAAK;QACL,sLAAkB,uBAAA,EAAqB,MAAM,gBAAA,EAAkB,CAAC,UAAU;YACxE,IAAI,CAAC,wBAAwB,OAAA,CAAS,CAAA,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;YACxE,wBAAwB,OAAA,GAAU;YAElC,MAAM,cAAA,CAAe;QACvB,CAAC;QACD,uLAAmB,uBAAA,EAAqB,MAAM,iBAAA,EAAmB,CAAC,UAAU;YAC1E,MAAM,gBAAgB,MAAM,MAAA,CAAO,aAAA;YACnC,MAAM,gBAAgB,cAAc,MAAA,KAAW,KAAK,cAAc,OAAA,KAAY;YAC9E,MAAM,eAAe,cAAc,MAAA,KAAW,KAAK;YACnD,IAAI,CAAC,QAAQ,KAAA,IAAS,aAAc,CAAA,wBAAwB,OAAA,GAAU;QACxE,CAAC;QACD,OAAO;YACL,GAAG,MAAM,KAAA;YAAA,iDAAA;YAET,GAAG;gBACD,kDACE;gBACF,iDAAiD;gBACjD,kDACE;gBACF,uCAAuC;gBACvC,wCAAwC;YAC1C,CAAA;QACF;IAAA;AAGN;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,aAAa;AAMnB,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,QAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,aAAa;AAMnB,IAAM,wBAA0B,2KAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,QAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,YAAY;AAMlB,IAAM,qLAAyB,aAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,mBAAA,EAAqB,GAAG,UAAU,CAAA,GAAI;IAC9C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAe,2KAAA,EAAd;QAAoB,GAAG,SAAA;QAAY,GAAG,SAAA;QAAW,KAAK;IAAA,CAAc;AAC9E;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,qBAAqB;AAM3B,IAAM,6LAAiC,aAAA,EAGrC,CAAC,OAAmD,iBAAiB;IACrE,MAAM,EAAE,mBAAA,EAAqB,GAAG,kBAAkB,CAAA,GAAI;IACtD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAe,mLAAA,EAAd;QAA4B,GAAG,SAAA;QAAY,GAAG,iBAAA;QAAmB,KAAK;IAAA,CAAc;AAC9F,CAAC;AAED,yBAAyB,WAAA,GAAc;AAMvC,IAAM,mBAAmB;AAMzB,IAAM,0LAA+B,cAAA,EAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,aAAA,EAAd;QAA0B,GAAG,SAAA;QAAY,GAAG,eAAA;QAAiB,KAAK;IAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,WAAA,GAAc;AAMrC,IAAM,kBAAkB;AAMxB,IAAM,0LAA8B,aAAA,EAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EAAE,mBAAA,EAAqB,GAAG,eAAe,CAAA,GAAI;IACnD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,YAAA,EAAd;QAAyB,GAAG,SAAA;QAAY,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,WAAA,GAAc;AAMpC,IAAM,iBAAiB;AAMvB,IAAM,6BAAkC,8KAAA,EAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,mBAAA,EAAqB,GAAG,mBAAmB,CAAA,GAAI;IACvD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,gBAAA,EAAd;QAA6B,GAAG,SAAA;QAAY,GAAG,kBAAA;QAAoB,KAAK;IAAA,CAAc;AAChG,CAAC;AAED,0BAA0B,WAAA,GAAc;AAMxC,IAAM,iBAAiB;AAMvB,IAAM,0LAA8B,aAAA,EAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EAAE,mBAAA,EAAqB,GAAG,eAAe,CAAA,GAAI;IACnD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,YAAA,EAAd;QAAyB,GAAG,SAAA;QAAY,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,WAAA,GAAc;AAMpC,IAAM,aAAa;AAMnB,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,QAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAahC,IAAM,kBAAkD,CACtD,UACG;IACH,MAAM,EAAE,mBAAA,EAAqB,QAAA,EAAU,MAAM,QAAA,EAAU,YAAA,EAAc,WAAA,CAAY,CAAA,GAAI;IACrF,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,CAAC,MAAM,OAAO,CAAA,mMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,qKAAe,OAAA,EAAd;QAAmB,GAAG,SAAA;QAAW;QAAY,cAAc;QACzD;IAAA,CACH;AAEJ;AAMA,IAAM,mBAAmB;AAMzB,IAAM,2LAA+B,aAAA,EAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAe,iLAAA,EAAd;QAA0B,GAAG,SAAA;QAAY,GAAG,eAAA;QAAiB,KAAK;IAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,WAAA,GAAc;AAMrC,IAAM,mBAAmB;AAMzB,IAAM,2LAA+B,aAAA,EAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAElD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sKAAe,aAAA,EAAd;QACE,GAAG,SAAA;QACH,GAAG,eAAA;QACJ,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YAAA,iDAAA;YAET,GAAG;gBACD,kDAAkD;gBAClD,iDAAiD;gBACjD,kDAAkD;gBAClD,uCAAuC;gBACvC,wCAAwC;YAC1C,CAAA;QACF;IAAA;AAGN,CAAC;AAED,uBAAuB,WAAA,GAAc;AAIrC,IAAMA,QAAO;AACb,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;AACd,IAAMC,SAAQ;AACd,IAAMC,QAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,SAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-tabs/src/tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "names": ["Root"], "mappings": ";;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,mCAAmC;AAC5C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAE1B,SAAS,oBAAoB;AAC7B,SAAS,4BAA4B;AACrC,SAAS,aAAa;AAoFd;;;;;;;;;;;;;AA5ER,IAAM,YAAY;AAGlB,IAAM,CAAC,mBAAmB,eAAe,CAAA,8KAAI,qBAAA,EAAmB,WAAW;mLACzE,8BAAA;CACD;AACD,IAAM,8MAA2B,8BAAA,CAA4B;AAW7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AA6BpF,IAAM,yKAAa,aAAA,EACjB,CAAC,OAA+B,iBAAiB;IAC/C,MAAM,EACJ,WAAA,EACA,OAAO,SAAA,EACP,aAAA,EACA,YAAA,EACA,cAAc,YAAA,EACd,GAAA,EACA,iBAAiB,WAAA,EACjB,GAAG,WACL,GAAI;IACJ,MAAM,yLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,OAAO,QAAQ,CAAA,mMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,UAAU;QACV,aAAa,gBAAgB;QAC7B,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;QACC,OAAO;QACP,8KAAQ,QAAA,CAAM;QACd;QACA,eAAe;QACf;QACA,KAAK;QACL;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,KAAK;YACL,oBAAkB;YACjB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,KAAK,WAAA,GAAc;AAMnB,IAAM,gBAAgB;AAOtB,IAAM,6KAAiB,aAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAA,EAAa,OAAO,IAAA,EAAM,GAAG,UAAU,CAAA,GAAI;IACnD,MAAM,UAAU,eAAe,eAAe,WAAW;IACzD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAkB,sLAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,aAAa,QAAQ,WAAA;QACrB,KAAK,QAAQ,GAAA;QACb;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,MAAK;YACL,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,eAAe;AAQrB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAClE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,gLAAkB,QAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,WAAW,CAAC;QACZ,QAAQ;QAER,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe;YACf,iBAAe;YACf,cAAY,aAAa,WAAW;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACA,IAAI;YACH,GAAG,YAAA;YACJ,KAAK;YACL,cAAa,0LAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,aAAA,CAAc,KAAK;gBAC7B,OAAO;oBAEL,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,+KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI;oBAAC;oBAAK,OAAO;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,aAAA,CAAc,KAAK;YACrE,CAAC;YACD,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,MAAM;gBAGjD,MAAM,wBAAwB,QAAQ,cAAA,KAAmB;gBACzD,IAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;oBACrD,QAAQ,aAAA,CAAc,KAAK;gBAC7B;YACF,CAAC;QAAA;IACH;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,eAAe;AAarB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,UAAA,EAAY,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,MAAM,iMAAqC,SAAA,EAAO,UAAU;sKAEtD,YAAA;iCAAU,MAAM;YACpB,MAAM,MAAM;6CAAsB,IAAO,6BAA6B,OAAA,GAAU,KAAM;;YACtF;yCAAO,IAAM,qBAAqB,GAAG;;QACvC;gCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,yKAAC,YAAA,EAAA;QAAS,SAAS,cAAc;QAC9B,UAAA,CAAC,EAAE,OAAA,CAAQ,CAAA,GACV,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;gBACC,cAAY,aAAa,WAAW;gBACpC,oBAAkB,QAAQ,WAAA;gBAC1B,MAAK;gBACL,mBAAiB;gBACjB,QAAQ,CAAC;gBACT,IAAI;gBACJ,UAAU;gBACT,GAAG,YAAA;gBACJ,KAAK;gBACL,OAAO;oBACL,GAAG,MAAM,KAAA;oBACT,mBAAmB,6BAA6B,OAAA,GAAU,OAAO,KAAA;gBACnE;gBAEC,UAAA,WAAW;YAAA;IACd,CAEJ;AAEJ;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,IAAMA,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-dialog/src/dialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,eAAe,0BAA0B;AAClD,SAAS,aAAa;AACtB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,kBAAkB;AAC3B,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,oBAAoB;AAC7B,SAAS,kBAAkB;AAC3B,SAAS,kBAAkB;AAsDvB,SA2VM,UA3VN,KA2VM,YA3VN;;;;;;;;;;;;;;;;;;AA9CJ,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAc/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAU9F,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EACJ,aAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,QAAQ,IAAA,EACV,GAAI;IACJ,MAAM,+KAAmB,SAAA,EAA0B,IAAI;IACvD,MAAM,+KAAmB,SAAA,EAA6B,IAAI;IAC1D,MAAM,CAAC,MAAM,OAAO,CAAA,mMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA;QACA,iLAAW,QAAA,CAAM;QACjB,+KAAS,QAAA,CAAM;QACf,qLAAe,QAAA,CAAM;QACrB;QACA,cAAc;QACd,gLAAoB,cAAA;kCAAY,IAAM;0CAAQ,CAAC,WAAa,CAAC,QAAQ;;iCAAG;YAAC,OAAO;SAAC;QACjF;QAEC;IAAA;AAGP;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,kLAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,wMAAqB,kBAAA,EAAgB,cAAc,QAAQ,UAAU;IAC3E,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,iBAAc;QACd,iBAAe,QAAQ,IAAA;QACvB,iBAAe,QAAQ,SAAA;QACvB,cAAY,SAAS,QAAQ,IAAI;QAChC,GAAG,YAAA;QACJ,KAAK;QACL,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,QAAQ,YAAY;IAAA;AAGvE;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,aAAa;IAC9F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,eAA4C,CAAC,UAA0C;IAC3F,MAAM,EAAE,aAAA,EAAe,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IAC3D,MAAM,UAAU,iBAAiB,aAAa,aAAa;IAC3D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QACnC,wKAAM,WAAA,CAAS,GAAA,CAAI,UAAU,CAAC,QAC7B,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;gBAAS,SAAS,cAAc,QAAQ,IAAA;gBACvC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAC,SAAA,EAAA;oBAAgB,SAAO;oBAAC;oBACtB,UAAA;gBAAA,CACH;YAAA,CACF,CACD;IAAA,CACH;AAEJ;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAWrB,IAAM,iLAAsB,cAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OAAO,QAAQ,KAAA,GACb,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACvC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;YAAmB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAC1D,IACE;AACN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,WAAO,iLAAA,EAAW,4BAA4B;AAEpD,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,OAAA,oFAAA;IAAA,gDAAA;IAGE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,wOAAA,EAAA;QAAa,IAAI;QAAM,gBAAc;QAAC,QAAQ;YAAC,QAAQ,UAAU;SAAA;QAChE,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,cAAY,SAAS,QAAQ,IAAI;YAChC,GAAG,YAAA;YACJ,KAAK;YAEL,OAAO;gBAAE,eAAe;gBAAQ,GAAG,aAAa,KAAA;YAAM;QAAA;IACxD,CACF;AAEJ;AAOF,IAAM,eAAe;AAWrB,IAAM,iLAAsB,cAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACtC,UAAA,QAAQ,KAAA,GACP,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;YAAoB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc,IAEzD,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,uBAAA;YAAuB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAEhE;AAEJ;AAGF,cAAc,WAAA,GAAc;AAQ5B,IAAM,uLAA2B,aAAA,EAC/B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,+KAAmB,SAAA,EAAuB,IAAI;IACpD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,QAAQ,UAAA,EAAY,UAAU;sKAG3E,YAAA;wCAAU,MAAM;YACpB,MAAM,UAAU,WAAW,OAAA;YAC3B,IAAI,QAAS,CAAA,wKAAO,aAAA,EAAW,OAAO;QACxC;uCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QAGL,WAAW,QAAQ,IAAA;QACnB,6BAA2B;QAC3B,sBAAkB,uLAAA,EAAqB,MAAM,gBAAA,EAAkB,CAAC,UAAU;YACxE,MAAM,cAAA,CAAe;YACrB,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;QACpC,CAAC;QACD,uBAAsB,0LAAA,EAAqB,MAAM,oBAAA,EAAsB,CAAC,UAAU;YAChF,MAAM,gBAAgB,MAAM,MAAA,CAAO,aAAA;YACnC,MAAM,gBAAgB,cAAc,MAAA,KAAW,KAAK,cAAc,OAAA,KAAY;YAC9E,MAAM,eAAe,cAAc,MAAA,KAAW,KAAK;YAInD,IAAI,aAAc,CAAA,MAAM,cAAA,CAAe;QACzC,CAAC;QAGD,gBAAgB,2LAAA,EAAqB,MAAM,cAAA,EAAgB,CAAC,QAC1D,MAAM,cAAA,CAAe;IACvB;AAGN;AAKF,IAAM,4BAA8B,2KAAA,EAClC,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,4LAAgC,SAAA,EAAO,KAAK;IAClD,MAAM,6LAAiC,SAAA,EAAO,KAAK;IAEnD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,kBAAkB,CAAC,UAAU;YAC3B,MAAM,gBAAA,GAAmB,KAAK;YAE9B,IAAI,CAAC,MAAM,gBAAA,EAAkB;gBAC3B,IAAI,CAAC,wBAAwB,OAAA,CAAS,CAAA,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;gBAExE,MAAM,cAAA,CAAe;YACvB;YAEA,wBAAwB,OAAA,GAAU;YAClC,yBAAyB,OAAA,GAAU;QACrC;QACA,mBAAmB,CAAC,UAAU;YAC5B,MAAM,iBAAA,GAAoB,KAAK;YAE/B,IAAI,CAAC,MAAM,gBAAA,EAAkB;gBAC3B,wBAAwB,OAAA,GAAU;gBAClC,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,eAAe;oBACrD,yBAAyB,OAAA,GAAU;gBACrC;YACF;YAKA,MAAM,SAAS,MAAM,MAAA;YACrB,MAAM,kBAAkB,QAAQ,UAAA,CAAW,OAAA,EAAS,SAAS,MAAM;YACnE,IAAI,gBAAiB,CAAA,MAAM,cAAA,CAAe;YAM1C,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,aAAa,yBAAyB,OAAA,EAAS;gBACrF,MAAM,cAAA,CAAe;YACvB;QACF;IAAA;AAGN;AA6BF,IAAM,sLAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,eAAA,EAAiB,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IACzF,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,+KAAmB,SAAA,EAAuB,IAAI;IACpD,MAAM,iMAAe,mBAAA,EAAgB,cAAc,UAAU;IAI7D,CAAA,GAAA,8KAAA,CAAA,iBAAA,CAAe;IAEf,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;QACE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,gLAAC,aAAA,EAAA;gBACC,SAAO;gBACP,MAAI;gBACJ,SAAS;gBACT,kBAAkB;gBAClB,oBAAoB;gBAEpB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sLAAC,mBAAA,EAAA;oBACC,MAAK;oBACL,IAAI,QAAQ,SAAA;oBACZ,oBAAkB,QAAQ,aAAA;oBAC1B,mBAAiB,QAAQ,OAAA;oBACzB,cAAY,SAAS,QAAQ,IAAI;oBAChC,GAAG,YAAA;oBACJ,KAAK;oBACL,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;gBAAA;YAC7C;YAGA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;gBACE,UAAA;oBAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,cAAA;wBAAa,SAAS,QAAQ,OAAA;oBAAA,CAAS;oBACxC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;wBAAmB;wBAAwB,eAAe,QAAQ,aAAA;oBAAA,CAAe;iBAAA;YAAA,CACpF;SAAA;IAAA,CAEJ;AAEJ;AAOF,IAAM,aAAa;AAMnB,IAAM,eAAoB,8KAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,EAAA,EAAV;QAAa,IAAI,QAAQ,OAAA;QAAU,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC/E;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,mBAAmB;AAMzB,IAAM,qBAA0B,8KAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,iBAAiB,CAAA,GAAI;IAC/C,MAAM,UAAU,iBAAiB,kBAAkB,aAAa;IAChE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,CAAA,EAAV;QAAY,IAAI,QAAQ,aAAA;QAAgB,GAAG,gBAAA;QAAkB,KAAK;IAAA,CAAc;AAC1F;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,aAAa;AAKnB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACJ,GAAG,UAAA;QACJ,KAAK;QACL,UAAS,0LAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,YAAA,CAAa,KAAK,CAAC;IAAA;AAGpF;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,SAAS,IAAA,EAAe;IAC/B,OAAO,OAAO,SAAS;AACzB;AAEA,IAAM,qBAAqB;AAE3B,IAAM,CAAC,iBAAiB,iBAAiB,CAAA,IAAI,0LAAA,EAAc,oBAAoB;IAC7E,aAAa;IACb,WAAW;IACX,UAAU;AACZ,CAAC;AAID,IAAM,eAA4C,CAAC,EAAE,OAAA,CAAQ,CAAA,KAAM;IACjE,MAAM,sBAAsB,kBAAkB,kBAAkB;IAEhE,MAAM,UAAU,CAAA,EAAA,EAAK,oBAAoB,WAAW,CAAA,gBAAA,EAAmB,oBAAoB,SAAS,CAAA;;0BAAA,EAE1E,oBAAoB,SAAS,CAAA;;0EAAA,EAEmB,oBAAoB,QAAQ,EAAA;sKAEhG,YAAA;kCAAU,MAAM;YACpB,IAAI,SAAS;gBACX,MAAM,WAAW,SAAS,cAAA,CAAe,OAAO;gBAChD,IAAI,CAAC,SAAU,CAAA,QAAQ,KAAA,CAAM,OAAO;YACtC;QACF;iCAAG;QAAC;QAAS,OAAO;KAAC;IAErB,OAAO;AACT;AAEA,IAAM,2BAA2B;AAOjC,IAAM,qBAAwD,CAAC,EAAE,UAAA,EAAY,aAAA,CAAc,CAAA,KAAM;IAC/F,MAAM,4BAA4B,kBAAkB,wBAAwB;IAC5E,MAAM,UAAU,CAAA,0EAAA,EAA6E,0BAA0B,WAAW,CAAA,EAAA,CAAA;sKAE5H,YAAA;wCAAU,MAAM;YACpB,MAAM,gBAAgB,WAAW,OAAA,EAAS,aAAa,kBAAkB;YAEzE,IAAI,iBAAiB,eAAe;gBAClC,MAAM,iBAAiB,SAAS,cAAA,CAAe,aAAa;gBAC5D,IAAI,CAAC,eAAgB,CAAA,QAAQ,IAAA,CAAK,OAAO;YAC3C;QACF;uCAAG;QAAC;QAAS;QAAY,aAAa;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-separator/src/separator.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n *  Separator\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Separator';\nconst DEFAULT_ORIENTATION = 'horizontal';\nconst ORIENTATIONS = ['horizontal', 'vertical'] as const;\n\ntype Orientation = (typeof ORIENTATIONS)[number];\ntype SeparatorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SeparatorProps extends PrimitiveDivProps {\n  /**\n   * Either `vertical` or `horizontal`. Defaults to `horizontal`.\n   */\n  orientation?: Orientation;\n  /**\n   * Whether or not the component is purely decorative. When true, accessibility-related attributes\n   * are updated so that that the rendered element is removed from the accessibility tree.\n   */\n  decorative?: boolean;\n}\n\nconst Separator = React.forwardRef<SeparatorElement, SeparatorProps>((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  // `aria-orientation` defaults to `horizontal` so we only need it if `orientation` is vertical\n  const ariaOrientation = orientation === 'vertical' ? orientation : undefined;\n  const semanticProps = decorative\n    ? { role: 'none' }\n    : { 'aria-orientation': ariaOrientation, role: 'separator' };\n\n  return (\n    <Primitive.div\n      data-orientation={orientation}\n      {...semanticProps}\n      {...domProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nSeparator.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isValidOrientation(orientation: any): orientation is Orientation {\n  return ORIENTATIONS.includes(orientation);\n}\n\nconst Root = Separator;\n\nexport {\n  Separator,\n  //\n  Root,\n};\nexport type { SeparatorProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AAmCtB;;;;AA7BJ,IAAM,OAAO;AACb,IAAM,sBAAsB;AAC5B,IAAM,eAAe;IAAC;IAAc,UAAU;CAAA;AAiB9C,IAAM,8KAAkB,aAAA,EAA6C,CAAC,OAAO,iBAAiB;IAC5F,MAAM,EAAE,UAAA,EAAY,aAAa,kBAAkB,mBAAA,EAAqB,GAAG,SAAS,CAAA,GAAI;IACxF,MAAM,cAAc,mBAAmB,eAAe,IAAI,kBAAkB;IAE5E,MAAM,kBAAkB,gBAAgB,aAAa,cAAc,KAAA;IACnE,MAAM,gBAAgB,aAClB;QAAE,MAAM;IAAO,IACf;QAAE,oBAAoB;QAAiB,MAAM;IAAY;IAE7D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACC,oBAAkB;QACjB,GAAG,aAAA;QACH,GAAG,QAAA;QACJ,KAAK;IAAA;AAGX,CAAC;AAED,UAAU,WAAA,GAAc;AAIxB,SAAS,mBAAmB,WAAA,EAA8C;IACxE,OAAO,aAAa,QAAA,CAAS,WAAW;AAC1C;AAEA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-switch/src/switch.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Switch\n * -----------------------------------------------------------------------------------------------*/\n\nconst SWITCH_NAME = 'Switch';\n\ntype ScopedProps<P> = P & { __scopeSwitch?: Scope };\nconst [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\n\ntype SwitchContextValue = { checked: boolean; disabled?: boolean };\nconst [SwitchProvider, useSwitchContext] = createSwitchContext<SwitchContextValue>(SWITCH_NAME);\n\ntype SwitchElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SwitchProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  defaultChecked?: boolean;\n  required?: boolean;\n  onCheckedChange?(checked: boolean): void;\n}\n\nconst Switch = React.forwardRef<SwitchElement, SwitchProps>(\n  (props: ScopedProps<SwitchProps>, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked ?? false,\n      onChange: onCheckedChange,\n      caller: SWITCH_NAME,\n    });\n\n    return (\n      <SwitchProvider scope={__scopeSwitch} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"switch\"\n          aria-checked={checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...switchProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if switch is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect switch updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <SwitchBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </SwitchProvider>\n    );\n  }\n);\n\nSwitch.displayName = SWITCH_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SwitchThumb';\n\ntype SwitchThumbElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SwitchThumbProps extends PrimitiveSpanProps {}\n\nconst SwitchThumb = React.forwardRef<SwitchThumbElement, SwitchThumbProps>(\n  (props: ScopedProps<SwitchThumbProps>, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return (\n      <Primitive.span\n        data-state={getState(context.checked)}\n        data-disabled={context.disabled ? '' : undefined}\n        {...thumbProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSwitchThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SwitchBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface SwitchBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst SwitchBubbleInput = React.forwardRef<HTMLInputElement, SwitchBubbleInputProps>(\n  (\n    {\n      __scopeSwitch,\n      control,\n      checked,\n      bubbles = true,\n      ...props\n    }: ScopedProps<SwitchBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    return (\n      <input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={checked}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Switch;\nconst Thumb = SwitchThumb;\n\nexport {\n  createSwitchScope,\n  //\n  Switch,\n  SwitchThumb,\n  //\n  Root,\n  Thumb,\n};\nexport type { SwitchProps, SwitchThumbProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,SAAS,iBAAiB;AAoDpB,SACE,KADF;;;;;;;;;;;AA5CN,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAG/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAW9F,IAAM,2KAAe,aAAA,EACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EACJ,aAAA,EACA,IAAA,EACA,SAAS,WAAA,EACT,cAAA,EACA,QAAA,EACA,QAAA,EACA,QAAQ,IAAA,EACR,eAAA,EACA,IAAA,EACA,GAAG,aACL,GAAI;IACJ,MAAM,CAAC,QAAQ,SAAS,CAAA,GAAU,6KAAA,EAAmC,IAAI;IACzE,MAAM,kMAAe,kBAAA,EAAgB;gDAAc,CAAC,OAAS,UAAU,IAAI,CAAC;;IAC5E,MAAM,qMAAyC,SAAA,EAAO,KAAK;IAE3D,MAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,OAAA,CAAQ,MAAM,IAAI;IAClE,MAAM,CAAC,SAAS,UAAU,CAAA,mMAAI,uBAAA,EAAqB;QACjD,MAAM;QACN,aAAa,kBAAkB;QAC/B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QAAkB;QACtD,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qLAAA,CAAU,MAAA,EAAV;gBACC,MAAK;gBACL,MAAK;gBACL,gBAAc;gBACd,iBAAe;gBACf,cAAY,SAAS,OAAO;gBAC5B,iBAAe,WAAW,KAAK,KAAA;gBAC/B;gBACA;gBACC,GAAG,WAAA;gBACJ,KAAK;gBACL,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;oBACtD,WAAW,CAAC,cAAgB,CAAC,WAAW;oBACxC,IAAI,eAAe;wBACjB,iCAAiC,OAAA,GAAU,MAAM,oBAAA,CAAqB;wBAItE,IAAI,CAAC,iCAAiC,OAAA,CAAS,CAAA,MAAM,eAAA,CAAgB;oBACvE;gBACF,CAAC;YAAA;YAEF,iBACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;gBACC,SAAS;gBACT,SAAS,CAAC,iCAAiC,OAAA;gBAC3C;gBACA;gBACA;gBACA;gBACA;gBACA;gBAIA,OAAO;oBAAE,WAAW;gBAAoB;YAAA;SAC1C;IAAA,CAEJ;AAEJ;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAMnB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QACC,cAAY,SAAS,QAAQ,OAAO;QACpC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACtC,GAAG,UAAA;QACJ,KAAK;IAAA;AAGX;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,oBAAoB;AAS1B,IAAM,wBAA0B,2KAAA,EAC9B,CACE,EACE,aAAA,EACA,OAAA,EACA,OAAA,EACA,UAAU,IAAA,EACV,GAAG,OACL,EACA,iBACG;IACH,MAAM,wKAAY,SAAA,EAAyB,IAAI;IAC/C,MAAM,eAAe,qMAAA,EAAgB,KAAK,YAAY;IACtD,MAAM,iMAAc,cAAA,EAAY,OAAO;IACvC,MAAM,eAAc,wLAAA,EAAQ,OAAO;sKAG7B,YAAA;uCAAU,MAAM;YACpB,MAAM,QAAQ,IAAI,OAAA;YAClB,IAAI,CAAC,MAAO,CAAA;YAEZ,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;YAC3C,MAAM,aAAa,OAAO,wBAAA,CACxB,YACA;YAEF,MAAM,aAAa,WAAW,GAAA;YAC9B,IAAI,gBAAgB,WAAW,YAAY;gBACzC,MAAM,QAAQ,IAAI,MAAM,SAAS;oBAAE;gBAAQ,CAAC;gBAC5C,WAAW,IAAA,CAAK,OAAO,OAAO;gBAC9B,MAAM,aAAA,CAAc,KAAK;YAC3B;QACF;sCAAG;QAAC;QAAa;QAAS,OAAO;KAAC;IAElC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,SAAA;QACC,MAAK;QACL,eAAW;QACX,gBAAgB;QACf,GAAG,KAAA;QACJ,UAAU,CAAA;QACV,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YACT,GAAG,WAAA;YACH,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;QACV;IAAA;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,SAAS,OAAA,EAAkB;IAClC,OAAO,UAAU,YAAY;AAC/B;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-visually-hidden/src/visually-hidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: 'absolute',\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: 'hidden',\n  clip: 'rect(0, 0, 0, 0)',\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}) satisfies React.CSSProperties;\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n  //\n  VISUALLY_HIDDEN_STYLES,\n};\nexport type { VisuallyHiddenProps };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AA6BpB;;;;AAvBN,IAAM,yBAAyB,OAAO,MAAA,CAAO;IAAA,qFAAA;IAE3C,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,SAAS;IACT,QAAQ,CAAA;IACR,UAAU;IACV,MAAM;IACN,YAAY;IACZ,UAAU;AACZ,CAAC;AAED,IAAM,OAAO;AAMb,IAAM,mLAAuB,aAAA,EAC3B,CAAC,OAAO,iBAAiB;IACvB,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,OAAO;YAAE,GAAG,sBAAA;YAAwB,GAAG,MAAM,KAAA;QAAM;IAAA;AAGzD;AAGF,eAAe,WAAA,GAAc;AAI7B,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/%40radix-ui/react-toast/src/toast.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { createContextScope } from '@radix-ui/react-context';\nimport * as DismissableLayer from '@radix-ui/react-dismissable-layer';\nimport { Portal } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { VisuallyHidden } from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * ToastProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'ToastProvider';\n\nconst [Collection, useCollection, createCollectionScope] = createCollection<ToastElement>('Toast');\n\ntype SwipeDirection = 'up' | 'down' | 'left' | 'right';\ntype ToastProviderContextValue = {\n  label: string;\n  duration: number;\n  swipeDirection: SwipeDirection;\n  swipeThreshold: number;\n  toastCount: number;\n  viewport: ToastViewportElement | null;\n  onViewportChange(viewport: ToastViewportElement): void;\n  onToastAdd(): void;\n  onToastRemove(): void;\n  isFocusedToastEscapeKeyDownRef: React.MutableRefObject<boolean>;\n  isClosePausedRef: React.MutableRefObject<boolean>;\n};\n\ntype ScopedProps<P> = P & { __scopeToast?: Scope };\nconst [createToastContext, createToastScope] = createContextScope('Toast', [createCollectionScope]);\nconst [ToastProviderProvider, useToastProviderContext] =\n  createToastContext<ToastProviderContextValue>(PROVIDER_NAME);\n\ninterface ToastProviderProps {\n  children?: React.ReactNode;\n  /**\n   * An author-localized label for each toast. Used to help screen reader users\n   * associate the interruption with a toast.\n   * @defaultValue 'Notification'\n   */\n  label?: string;\n  /**\n   * Time in milliseconds that each toast should remain visible for.\n   * @defaultValue 5000\n   */\n  duration?: number;\n  /**\n   * Direction of pointer swipe that should close the toast.\n   * @defaultValue 'right'\n   */\n  swipeDirection?: SwipeDirection;\n  /**\n   * Distance in pixels that the swipe must pass before a close is triggered.\n   * @defaultValue 50\n   */\n  swipeThreshold?: number;\n}\n\nconst ToastProvider: React.FC<ToastProviderProps> = (props: ScopedProps<ToastProviderProps>) => {\n  const {\n    __scopeToast,\n    label = 'Notification',\n    duration = 5000,\n    swipeDirection = 'right',\n    swipeThreshold = 50,\n    children,\n  } = props;\n  const [viewport, setViewport] = React.useState<ToastViewportElement | null>(null);\n  const [toastCount, setToastCount] = React.useState(0);\n  const isFocusedToastEscapeKeyDownRef = React.useRef(false);\n  const isClosePausedRef = React.useRef(false);\n\n  if (!label.trim()) {\n    console.error(\n      `Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`\n    );\n  }\n\n  return (\n    <Collection.Provider scope={__scopeToast}>\n      <ToastProviderProvider\n        scope={__scopeToast}\n        label={label}\n        duration={duration}\n        swipeDirection={swipeDirection}\n        swipeThreshold={swipeThreshold}\n        toastCount={toastCount}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        onToastAdd={React.useCallback(() => setToastCount((prevCount) => prevCount + 1), [])}\n        onToastRemove={React.useCallback(() => setToastCount((prevCount) => prevCount - 1), [])}\n        isFocusedToastEscapeKeyDownRef={isFocusedToastEscapeKeyDownRef}\n        isClosePausedRef={isClosePausedRef}\n      >\n        {children}\n      </ToastProviderProvider>\n    </Collection.Provider>\n  );\n};\n\nToastProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'ToastViewport';\nconst VIEWPORT_DEFAULT_HOTKEY = ['F8'];\nconst VIEWPORT_PAUSE = 'toast.viewportPause';\nconst VIEWPORT_RESUME = 'toast.viewportResume';\n\ntype ToastViewportElement = React.ComponentRef<typeof Primitive.ol>;\ntype PrimitiveOrderedListProps = React.ComponentPropsWithoutRef<typeof Primitive.ol>;\ninterface ToastViewportProps extends PrimitiveOrderedListProps {\n  /**\n   * The keys to use as the keyboard shortcut that will move focus to the toast viewport.\n   * @defaultValue ['F8']\n   */\n  hotkey?: string[];\n  /**\n   * An author-localized label for the toast viewport to provide context for screen reader users\n   * when navigating page landmarks. The available `{hotkey}` placeholder will be replaced for you.\n   * @defaultValue 'Notifications ({hotkey})'\n   */\n  label?: string;\n}\n\nconst ToastViewport = React.forwardRef<ToastViewportElement, ToastViewportProps>(\n  (props: ScopedProps<ToastViewportProps>, forwardedRef) => {\n    const {\n      __scopeToast,\n      hotkey = VIEWPORT_DEFAULT_HOTKEY,\n      label = 'Notifications ({hotkey})',\n      ...viewportProps\n    } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = React.useRef<HTMLDivElement>(null);\n    const headFocusProxyRef = React.useRef<FocusProxyElement>(null);\n    const tailFocusProxyRef = React.useRef<FocusProxyElement>(null);\n    const ref = React.useRef<ToastViewportElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const hasToasts = context.toastCount > 0;\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        // we use `event.code` as it is consistent regardless of meta keys that were pressed.\n        // for example, `event.key` for `Control+Alt+t` is `†` and `t !== †`\n        const isHotkeyPressed =\n          hotkey.length !== 0 && hotkey.every((key) => (event as any)[key] || event.code === key);\n        if (isHotkeyPressed) ref.current?.focus();\n      };\n      document.addEventListener('keydown', handleKeyDown);\n      return () => document.removeEventListener('keydown', handleKeyDown);\n    }, [hotkey]);\n\n    React.useEffect(() => {\n      const wrapper = wrapperRef.current;\n      const viewport = ref.current;\n      if (hasToasts && wrapper && viewport) {\n        const handlePause = () => {\n          if (!context.isClosePausedRef.current) {\n            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n            viewport.dispatchEvent(pauseEvent);\n            context.isClosePausedRef.current = true;\n          }\n        };\n\n        const handleResume = () => {\n          if (context.isClosePausedRef.current) {\n            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n            viewport.dispatchEvent(resumeEvent);\n            context.isClosePausedRef.current = false;\n          }\n        };\n\n        const handleFocusOutResume = (event: FocusEvent) => {\n          const isFocusMovingOutside = !wrapper.contains(event.relatedTarget as HTMLElement);\n          if (isFocusMovingOutside) handleResume();\n        };\n\n        const handlePointerLeaveResume = () => {\n          const isFocusInside = wrapper.contains(document.activeElement);\n          if (!isFocusInside) handleResume();\n        };\n\n        // Toasts are not in the viewport React tree so we need to bind DOM events\n        wrapper.addEventListener('focusin', handlePause);\n        wrapper.addEventListener('focusout', handleFocusOutResume);\n        wrapper.addEventListener('pointermove', handlePause);\n        wrapper.addEventListener('pointerleave', handlePointerLeaveResume);\n        window.addEventListener('blur', handlePause);\n        window.addEventListener('focus', handleResume);\n        return () => {\n          wrapper.removeEventListener('focusin', handlePause);\n          wrapper.removeEventListener('focusout', handleFocusOutResume);\n          wrapper.removeEventListener('pointermove', handlePause);\n          wrapper.removeEventListener('pointerleave', handlePointerLeaveResume);\n          window.removeEventListener('blur', handlePause);\n          window.removeEventListener('focus', handleResume);\n        };\n      }\n    }, [hasToasts, context.isClosePausedRef]);\n\n    const getSortedTabbableCandidates = React.useCallback(\n      ({ tabbingDirection }: { tabbingDirection: 'forwards' | 'backwards' }) => {\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem) => {\n          const toastNode = toastItem.ref.current!;\n          const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];\n          return tabbingDirection === 'forwards'\n            ? toastTabbableCandidates\n            : toastTabbableCandidates.reverse();\n        });\n        return (\n          tabbingDirection === 'forwards' ? tabbableCandidates.reverse() : tabbableCandidates\n        ).flat();\n      },\n      [getItems]\n    );\n\n    React.useEffect(() => {\n      const viewport = ref.current;\n      // We programmatically manage tabbing as we are unable to influence\n      // the source order with portals, this allows us to reverse the\n      // tab order so that it runs from most recent toast to least\n      if (viewport) {\n        const handleKeyDown = (event: KeyboardEvent) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === 'Tab' && !isMetaKey;\n\n          if (isTabKey) {\n            const focusedElement = document.activeElement;\n            const isTabbingBackwards = event.shiftKey;\n            const targetIsViewport = event.target === viewport;\n\n            // If we're back tabbing after jumping to the viewport then we simply\n            // proxy focus out to the preceding document\n            if (targetIsViewport && isTabbingBackwards) {\n              headFocusProxyRef.current?.focus();\n              return;\n            }\n\n            const tabbingDirection = isTabbingBackwards ? 'backwards' : 'forwards';\n            const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });\n            const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);\n            if (focusFirst(sortedCandidates.slice(index + 1))) {\n              event.preventDefault();\n            } else {\n              // If we can't focus that means we're at the edges so we\n              // proxy to the corresponding exit point and let the browser handle\n              // tab/shift+tab keypress and implicitly pass focus to the next valid element in the document\n              isTabbingBackwards\n                ? headFocusProxyRef.current?.focus()\n                : tailFocusProxyRef.current?.focus();\n            }\n          }\n        };\n\n        // Toasts are not in the viewport React tree so we need to bind DOM events\n        viewport.addEventListener('keydown', handleKeyDown);\n        return () => viewport.removeEventListener('keydown', handleKeyDown);\n      }\n    }, [getItems, getSortedTabbableCandidates]);\n\n    return (\n      <DismissableLayer.Branch\n        ref={wrapperRef}\n        role=\"region\"\n        aria-label={label.replace('{hotkey}', hotkeyLabel)}\n        // Ensure virtual cursor from landmarks menus triggers focus/blur for pause/resume\n        tabIndex={-1}\n        // incase list has size when empty (e.g. padding), we remove pointer events so\n        // it doesn't prevent interactions with page elements that it overlays\n        style={{ pointerEvents: hasToasts ? undefined : 'none' }}\n      >\n        {hasToasts && (\n          <FocusProxy\n            ref={headFocusProxyRef}\n            onFocusFromOutsideViewport={() => {\n              const tabbableCandidates = getSortedTabbableCandidates({\n                tabbingDirection: 'forwards',\n              });\n              focusFirst(tabbableCandidates);\n            }}\n          />\n        )}\n        {/**\n         * tabindex on the the list so that it can be focused when items are removed. we focus\n         * the list instead of the viewport so it announces number of items remaining.\n         */}\n        <Collection.Slot scope={__scopeToast}>\n          <Primitive.ol tabIndex={-1} {...viewportProps} ref={composedRefs} />\n        </Collection.Slot>\n        {hasToasts && (\n          <FocusProxy\n            ref={tailFocusProxyRef}\n            onFocusFromOutsideViewport={() => {\n              const tabbableCandidates = getSortedTabbableCandidates({\n                tabbingDirection: 'backwards',\n              });\n              focusFirst(tabbableCandidates);\n            }}\n          />\n        )}\n      </DismissableLayer.Branch>\n    );\n  }\n);\n\nToastViewport.displayName = VIEWPORT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_PROXY_NAME = 'ToastFocusProxy';\n\ntype FocusProxyElement = React.ComponentRef<typeof VisuallyHidden>;\ntype VisuallyHiddenProps = React.ComponentPropsWithoutRef<typeof VisuallyHidden>;\ninterface FocusProxyProps extends VisuallyHiddenProps {\n  onFocusFromOutsideViewport(): void;\n}\n\nconst FocusProxy = React.forwardRef<FocusProxyElement, ScopedProps<FocusProxyProps>>(\n  (props, forwardedRef) => {\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n\n    return (\n      <VisuallyHidden\n        aria-hidden\n        tabIndex={0}\n        {...proxyProps}\n        ref={forwardedRef}\n        // Avoid page scrolling when focus is on the focus proxy\n        style={{ position: 'fixed' }}\n        onFocus={(event) => {\n          const prevFocusedElement = event.relatedTarget as HTMLElement | null;\n          const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n          if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }}\n      />\n    );\n  }\n);\n\nFocusProxy.displayName = FOCUS_PROXY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Toast\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOAST_NAME = 'Toast';\nconst TOAST_SWIPE_START = 'toast.swipeStart';\nconst TOAST_SWIPE_MOVE = 'toast.swipeMove';\nconst TOAST_SWIPE_CANCEL = 'toast.swipeCancel';\nconst TOAST_SWIPE_END = 'toast.swipeEnd';\n\ntype ToastElement = ToastImplElement;\ninterface ToastProps extends Omit<ToastImplProps, keyof ToastImplPrivateProps> {\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst Toast = React.forwardRef<ToastElement, ToastProps>(\n  (props: ScopedProps<ToastProps>, forwardedRef) => {\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? true,\n      onChange: onOpenChange,\n      caller: TOAST_NAME,\n    });\n    return (\n      <Presence present={forceMount || open}>\n        <ToastImpl\n          open={open}\n          {...toastProps}\n          ref={forwardedRef}\n          onClose={() => setOpen(false)}\n          onPause={useCallbackRef(props.onPause)}\n          onResume={useCallbackRef(props.onResume)}\n          onSwipeStart={composeEventHandlers(props.onSwipeStart, (event) => {\n            event.currentTarget.setAttribute('data-swipe', 'start');\n          })}\n          onSwipeMove={composeEventHandlers(props.onSwipeMove, (event) => {\n            const { x, y } = event.detail.delta;\n            event.currentTarget.setAttribute('data-swipe', 'move');\n            event.currentTarget.style.setProperty('--radix-toast-swipe-move-x', `${x}px`);\n            event.currentTarget.style.setProperty('--radix-toast-swipe-move-y', `${y}px`);\n          })}\n          onSwipeCancel={composeEventHandlers(props.onSwipeCancel, (event) => {\n            event.currentTarget.setAttribute('data-swipe', 'cancel');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-y');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-end-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-end-y');\n          })}\n          onSwipeEnd={composeEventHandlers(props.onSwipeEnd, (event) => {\n            const { x, y } = event.detail.delta;\n            event.currentTarget.setAttribute('data-swipe', 'end');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-y');\n            event.currentTarget.style.setProperty('--radix-toast-swipe-end-x', `${x}px`);\n            event.currentTarget.style.setProperty('--radix-toast-swipe-end-y', `${y}px`);\n            setOpen(false);\n          })}\n        />\n      </Presence>\n    );\n  }\n);\n\nToast.displayName = TOAST_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype SwipeEvent = { currentTarget: EventTarget & ToastElement } & Omit<\n  CustomEvent<{ originalEvent: React.PointerEvent; delta: { x: number; y: number } }>,\n  'currentTarget'\n>;\n\nconst [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n  onClose() {},\n});\n\ntype ToastImplElement = React.ComponentRef<typeof Primitive.li>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer.Root>;\ntype ToastImplPrivateProps = { open: boolean; onClose(): void };\ntype PrimitiveListItemProps = React.ComponentPropsWithoutRef<typeof Primitive.li>;\ninterface ToastImplProps extends ToastImplPrivateProps, PrimitiveListItemProps {\n  type?: 'foreground' | 'background';\n  /**\n   * Time in milliseconds that toast should remain visible for. Overrides value\n   * given to `ToastProvider`.\n   */\n  duration?: number;\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPause?(): void;\n  onResume?(): void;\n  onSwipeStart?(event: SwipeEvent): void;\n  onSwipeMove?(event: SwipeEvent): void;\n  onSwipeCancel?(event: SwipeEvent): void;\n  onSwipeEnd?(event: SwipeEvent): void;\n}\n\nconst ToastImpl = React.forwardRef<ToastImplElement, ToastImplProps>(\n  (props: ScopedProps<ToastImplProps>, forwardedRef) => {\n    const {\n      __scopeToast,\n      type = 'foreground',\n      duration: durationProp,\n      open,\n      onClose,\n      onEscapeKeyDown,\n      onPause,\n      onResume,\n      onSwipeStart,\n      onSwipeMove,\n      onSwipeCancel,\n      onSwipeEnd,\n      ...toastProps\n    } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = React.useState<ToastImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const pointerStartRef = React.useRef<{ x: number; y: number } | null>(null);\n    const swipeDeltaRef = React.useRef<{ x: number; y: number } | null>(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = React.useRef(0);\n    const closeTimerRemainingTimeRef = React.useRef(duration);\n    const closeTimerRef = React.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = useCallbackRef(() => {\n      // focus viewport if focus is within toast to read the remaining toast\n      // count to SR users and ensure focus isn't lost\n      const isFocusInToast = node?.contains(document.activeElement);\n      if (isFocusInToast) context.viewport?.focus();\n      onClose();\n    });\n\n    const startTimer = React.useCallback(\n      (duration: number) => {\n        if (!duration || duration === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration);\n      },\n      [handleClose]\n    );\n\n    React.useEffect(() => {\n      const viewport = context.viewport;\n      if (viewport) {\n        const handleResume = () => {\n          startTimer(closeTimerRemainingTimeRef.current);\n          onResume?.();\n        };\n        const handlePause = () => {\n          const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n          closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n          window.clearTimeout(closeTimerRef.current);\n          onPause?.();\n        };\n        viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n        viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n        return () => {\n          viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n          viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n        };\n      }\n    }, [context.viewport, duration, onPause, onResume, startTimer]);\n\n    // start timer when toast opens or duration changes.\n    // we include `open` in deps because closed !== unmounted when animating\n    // so it could reopen before being completely unmounted\n    React.useEffect(() => {\n      if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [open, duration, context.isClosePausedRef, startTimer]);\n\n    React.useEffect(() => {\n      onToastAdd();\n      return () => onToastRemove();\n    }, [onToastAdd, onToastRemove]);\n\n    const announceTextContent = React.useMemo(() => {\n      return node ? getAnnounceTextContent(node) : null;\n    }, [node]);\n\n    if (!context.viewport) return null;\n\n    return (\n      <>\n        {announceTextContent && (\n          <ToastAnnounce\n            __scopeToast={__scopeToast}\n            // Toasts are always role=status to avoid stuttering issues with role=alert in SRs.\n            role=\"status\"\n            aria-live={type === 'foreground' ? 'assertive' : 'polite'}\n            aria-atomic\n          >\n            {announceTextContent}\n          </ToastAnnounce>\n        )}\n\n        <ToastInteractiveProvider scope={__scopeToast} onClose={handleClose}>\n          {ReactDOM.createPortal(\n            <Collection.ItemSlot scope={__scopeToast}>\n              <DismissableLayer.Root\n                asChild\n                onEscapeKeyDown={composeEventHandlers(onEscapeKeyDown, () => {\n                  if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                  context.isFocusedToastEscapeKeyDownRef.current = false;\n                })}\n              >\n                <Primitive.li\n                  // Ensure toasts are announced as status list or status when focused\n                  role=\"status\"\n                  aria-live=\"off\"\n                  aria-atomic\n                  tabIndex={0}\n                  data-state={open ? 'open' : 'closed'}\n                  data-swipe-direction={context.swipeDirection}\n                  {...toastProps}\n                  ref={composedRefs}\n                  style={{ userSelect: 'none', touchAction: 'none', ...props.style }}\n                  onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                    if (event.key !== 'Escape') return;\n                    onEscapeKeyDown?.(event.nativeEvent);\n                    if (!event.nativeEvent.defaultPrevented) {\n                      context.isFocusedToastEscapeKeyDownRef.current = true;\n                      handleClose();\n                    }\n                  })}\n                  onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n                    if (event.button !== 0) return;\n                    pointerStartRef.current = { x: event.clientX, y: event.clientY };\n                  })}\n                  onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n                    if (!pointerStartRef.current) return;\n                    const x = event.clientX - pointerStartRef.current.x;\n                    const y = event.clientY - pointerStartRef.current.y;\n                    const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                    const isHorizontalSwipe = ['left', 'right'].includes(context.swipeDirection);\n                    const clamp = ['left', 'up'].includes(context.swipeDirection)\n                      ? Math.min\n                      : Math.max;\n                    const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                    const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                    const moveStartBuffer = event.pointerType === 'touch' ? 10 : 2;\n                    const delta = { x: clampedX, y: clampedY };\n                    const eventDetail = { originalEvent: event, delta };\n                    if (hasSwipeMoveStarted) {\n                      swipeDeltaRef.current = delta;\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                        discrete: false,\n                      });\n                    } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                      swipeDeltaRef.current = delta;\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                        discrete: false,\n                      });\n                      (event.target as HTMLElement).setPointerCapture(event.pointerId);\n                    } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                      // User is swiping in wrong direction so we disable swipe gesture\n                      // for the current pointer down interaction\n                      pointerStartRef.current = null;\n                    }\n                  })}\n                  onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n                    const delta = swipeDeltaRef.current;\n                    const target = event.target as HTMLElement;\n                    if (target.hasPointerCapture(event.pointerId)) {\n                      target.releasePointerCapture(event.pointerId);\n                    }\n                    swipeDeltaRef.current = null;\n                    pointerStartRef.current = null;\n                    if (delta) {\n                      const toast = event.currentTarget;\n                      const eventDetail = { originalEvent: event, delta };\n                      if (\n                        isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)\n                      ) {\n                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                          discrete: true,\n                        });\n                      } else {\n                        handleAndDispatchCustomEvent(\n                          TOAST_SWIPE_CANCEL,\n                          onSwipeCancel,\n                          eventDetail,\n                          {\n                            discrete: true,\n                          }\n                        );\n                      }\n                      // Prevent click event from triggering on items within the toast when\n                      // pointer up is part of a swipe gesture\n                      toast.addEventListener('click', (event) => event.preventDefault(), {\n                        once: true,\n                      });\n                    }\n                  })}\n                />\n              </DismissableLayer.Root>\n            </Collection.ItemSlot>,\n            context.viewport\n          )}\n        </ToastInteractiveProvider>\n      </>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ninterface ToastAnnounceProps\n  extends Omit<React.ComponentPropsWithoutRef<'div'>, 'children'>,\n    ScopedProps<{ children: string[] }> {}\n\nconst ToastAnnounce: React.FC<ToastAnnounceProps> = (props: ScopedProps<ToastAnnounceProps>) => {\n  const { __scopeToast, children, ...announceProps } = props;\n  const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n  const [renderAnnounceText, setRenderAnnounceText] = React.useState(false);\n  const [isAnnounced, setIsAnnounced] = React.useState(false);\n\n  // render text content in the next frame to ensure toast is announced in NVDA\n  useNextFrame(() => setRenderAnnounceText(true));\n\n  // cleanup after announcing\n  React.useEffect(() => {\n    const timer = window.setTimeout(() => setIsAnnounced(true), 1000);\n    return () => window.clearTimeout(timer);\n  }, []);\n\n  return isAnnounced ? null : (\n    <Portal asChild>\n      <VisuallyHidden {...announceProps}>\n        {renderAnnounceText && (\n          <>\n            {context.label} {children}\n          </>\n        )}\n      </VisuallyHidden>\n    </Portal>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ToastTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'ToastTitle';\n\ntype ToastTitleElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToastTitleProps extends PrimitiveDivProps {}\n\nconst ToastTitle = React.forwardRef<ToastTitleElement, ToastTitleProps>(\n  (props: ScopedProps<ToastTitleProps>, forwardedRef) => {\n    const { __scopeToast, ...titleProps } = props;\n    return <Primitive.div {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nToastTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'ToastDescription';\n\ntype ToastDescriptionElement = React.ComponentRef<typeof Primitive.div>;\ninterface ToastDescriptionProps extends PrimitiveDivProps {}\n\nconst ToastDescription = React.forwardRef<ToastDescriptionElement, ToastDescriptionProps>(\n  (props: ScopedProps<ToastDescriptionProps>, forwardedRef) => {\n    const { __scopeToast, ...descriptionProps } = props;\n    return <Primitive.div {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nToastDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'ToastAction';\n\ntype ToastActionElement = ToastCloseElement;\ninterface ToastActionProps extends ToastCloseProps {\n  /**\n   * A short description for an alternate way to carry out the action. For screen reader users\n   * who will not be able to navigate to the button easily/quickly.\n   * @example <ToastAction altText=\"Goto account settings to upgrade\">Upgrade</ToastAction>\n   * @example <ToastAction altText=\"Undo (Alt+U)\">Undo</ToastAction>\n   */\n  altText: string;\n}\n\nconst ToastAction = React.forwardRef<ToastActionElement, ToastActionProps>(\n  (props: ScopedProps<ToastActionProps>, forwardedRef) => {\n    const { altText, ...actionProps } = props;\n\n    if (!altText.trim()) {\n      console.error(\n        `Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`\n      );\n      return null;\n    }\n\n    return (\n      <ToastAnnounceExclude altText={altText} asChild>\n        <ToastClose {...actionProps} ref={forwardedRef} />\n      </ToastAnnounceExclude>\n    );\n  }\n);\n\nToastAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'ToastClose';\n\ntype ToastCloseElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface ToastCloseProps extends PrimitiveButtonProps {}\n\nconst ToastClose = React.forwardRef<ToastCloseElement, ToastCloseProps>(\n  (props: ScopedProps<ToastCloseProps>, forwardedRef) => {\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n\n    return (\n      <ToastAnnounceExclude asChild>\n        <Primitive.button\n          type=\"button\"\n          {...closeProps}\n          ref={forwardedRef}\n          onClick={composeEventHandlers(props.onClick, interactiveContext.onClose)}\n        />\n      </ToastAnnounceExclude>\n    );\n  }\n);\n\nToastClose.displayName = CLOSE_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype ToastAnnounceExcludeElement = React.ComponentRef<typeof Primitive.div>;\ninterface ToastAnnounceExcludeProps extends PrimitiveDivProps {\n  altText?: string;\n}\n\nconst ToastAnnounceExclude = React.forwardRef<\n  ToastAnnounceExcludeElement,\n  ToastAnnounceExcludeProps\n>((props: ScopedProps<ToastAnnounceExcludeProps>, forwardedRef) => {\n  const { __scopeToast, altText, ...announceExcludeProps } = props;\n\n  return (\n    <Primitive.div\n      data-radix-toast-announce-exclude=\"\"\n      data-radix-toast-announce-alt={altText || undefined}\n      {...announceExcludeProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nfunction getAnnounceTextContent(container: HTMLElement) {\n  const textContent: string[] = [];\n  const childNodes = Array.from(container.childNodes);\n\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === 'none';\n      const isExcluded = node.dataset.radixToastAnnounceExclude === '';\n\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.radixToastAnnounceAlt;\n          if (altText) textContent.push(altText);\n        } else {\n          textContent.push(...getAnnounceTextContent(node));\n        }\n      }\n    }\n  });\n\n  // We return a collection of text rather than a single concatenated string.\n  // This allows SR VO to naturally pause break between nodes while announcing.\n  return textContent;\n}\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction handleAndDispatchCustomEvent<\n  E extends CustomEvent,\n  ReactEvent extends React.SyntheticEvent,\n>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: ReactEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const currentTarget = detail.originalEvent.currentTarget as HTMLElement;\n  const event = new CustomEvent(name, { bubbles: true, cancelable: true, detail });\n  if (handler) currentTarget.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(currentTarget, event);\n  } else {\n    currentTarget.dispatchEvent(event);\n  }\n}\n\nconst isDeltaInDirection = (\n  delta: { x: number; y: number },\n  direction: SwipeDirection,\n  threshold = 0\n) => {\n  const deltaX = Math.abs(delta.x);\n  const deltaY = Math.abs(delta.y);\n  const isDeltaX = deltaX > deltaY;\n  if (direction === 'left' || direction === 'right') {\n    return isDeltaX && deltaX > threshold;\n  } else {\n    return !isDeltaX && deltaY > threshold;\n  }\n};\n\nfunction useNextFrame(callback = () => {}) {\n  const fn = useCallbackRef(callback);\n  useLayoutEffect(() => {\n    let raf1 = 0;\n    let raf2 = 0;\n    raf1 = window.requestAnimationFrame(() => (raf2 = window.requestAnimationFrame(fn)));\n    return () => {\n      window.cancelAnimationFrame(raf1);\n      window.cancelAnimationFrame(raf2);\n    };\n  }, [fn]);\n}\n\nfunction isHTMLElement(node: any): node is HTMLElement {\n  return node.nodeType === node.ELEMENT_NODE;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\n\nconst Provider = ToastProvider;\nconst Viewport = ToastViewport;\nconst Root = Toast;\nconst Title = ToastTitle;\nconst Description = ToastDescription;\nconst Action = ToastAction;\nconst Close = ToastClose;\n\nexport {\n  createToastScope,\n  //\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastAction,\n  ToastClose,\n  //\n  Provider,\n  Viewport,\n  Root,\n  Title,\n  Description,\n  Action,\n  Close,\n};\nexport type {\n  ToastProviderProps,\n  ToastViewportProps,\n  ToastProps,\n  ToastTitleProps,\n  ToastDescriptionProps,\n  ToastActionProps,\n  ToastCloseProps,\n};\n"], "names": ["node", "duration", "event", "Root"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,wBAAwB;AACjC,SAAS,0BAA0B;AACnC,YAAY,sBAAsB;AAClC,SAAS,cAAc;AACvB,SAAS,gBAAgB;AACzB,SAAS,WAAW,mCAAmC;AACvD,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,sBAAsB;AA+EzB,SA0cA,UA1cA,KA2LA,YA3LA;;;;;;;;;;;;;;;;;AAvEN,IAAM,gBAAgB;AAEtB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,iLAAI,mBAAA,EAA+B,OAAO;AAkBjG,IAAM,CAAC,oBAAoB,gBAAgB,CAAA,OAAI,4LAAA,EAAmB,SAAS;IAAC,qBAAqB;CAAC;AAClG,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GACnD,mBAA8C,aAAa;AA2B7D,IAAM,gBAA8C,CAAC,UAA2C;IAC9F,MAAM,EACJ,YAAA,EACA,QAAQ,cAAA,EACR,WAAW,GAAA,EACX,iBAAiB,OAAA,EACjB,iBAAiB,EAAA,EACjB,QAAA,EACF,GAAI;IACJ,MAAM,CAAC,UAAU,WAAW,CAAA,qKAAU,WAAA,EAAsC,IAAI;IAChF,MAAM,CAAC,YAAY,aAAa,CAAA,qKAAU,WAAA,EAAS,CAAC;IACpD,MAAM,mMAAuC,SAAA,EAAO,KAAK;IACzD,MAAM,qLAAyB,SAAA,EAAO,KAAK;IAE3C,IAAI,CAAC,MAAM,IAAA,CAAK,GAAG;QACjB,QAAQ,KAAA,CACN,CAAA,qCAAA,EAAwC,aAAa,CAAA,kCAAA,CAAA;IAEzD;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO;QAC1B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,uBAAA;YACC,OAAO;YACP;YACA;YACA;YACA;YACA;YACA;YACA,kBAAkB;YAClB,8KAAkB,cAAA;6CAAY,IAAM;qDAAc,CAAC,YAAc,YAAY,CAAC;;4CAAG,CAAC,CAAC;YACnF,iLAAqB,cAAA;6CAAY,IAAM;qDAAc,CAAC,YAAc,YAAY,CAAC;;4CAAG,CAAC,CAAC;YACtF;YACA;YAEC;QAAA;IACH,CACF;AAEJ;AAEA,cAAc,WAAA,GAAc;AAM5B,IAAM,gBAAgB;AACtB,IAAM,0BAA0B;IAAC,IAAI;CAAA;AACrC,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AAkBxB,IAAM,kLAAsB,aAAA,EAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EACJ,YAAA,EACA,SAAS,uBAAA,EACT,QAAQ,0BAAA,EACR,GAAG,eACL,GAAI;IACJ,MAAM,UAAU,wBAAwB,eAAe,YAAY;IACnE,MAAM,WAAW,cAAc,YAAY;IAC3C,MAAM,+KAAmB,SAAA,EAAuB,IAAI;IACpD,MAAM,oBAA0B,2KAAA,EAA0B,IAAI;IAC9D,MAAM,sLAA0B,SAAA,EAA0B,IAAI;IAC9D,MAAM,wKAAY,SAAA,EAA6B,IAAI;IACnD,MAAM,eAAe,qMAAA,EAAgB,cAAc,KAAK,QAAQ,gBAAgB;IAChF,MAAM,cAAc,OAAO,IAAA,CAAK,GAAG,EAAE,OAAA,CAAQ,QAAQ,EAAE,EAAE,OAAA,CAAQ,UAAU,EAAE;IAC7E,MAAM,YAAY,QAAQ,UAAA,GAAa;sKAEjC,YAAA;mCAAU,MAAM;YACpB,MAAM;yDAAgB,CAAC,UAAyB;oBAG9C,MAAM,kBACJ,OAAO,MAAA,KAAW,KAAK,OAAO,KAAA;iEAAM,CAAC,MAAS,KAAA,CAAc,GAAG,CAAA,IAAK,MAAM,IAAA,KAAS,GAAG;;oBACxF,IAAI,gBAAiB,CAAA,IAAI,OAAA,EAAS,MAAM;gBAC1C;;YACA,SAAS,gBAAA,CAAiB,WAAW,aAAa;YAClD;2CAAO,IAAM,SAAS,mBAAA,CAAoB,WAAW,aAAa;;QACpE;kCAAG;QAAC,MAAM;KAAC;IAEL,8KAAA;mCAAU,MAAM;YACpB,MAAM,UAAU,WAAW,OAAA;YAC3B,MAAM,WAAW,IAAI,OAAA;YACrB,IAAI,aAAa,WAAW,UAAU;gBACpC,MAAM;2DAAc,MAAM;wBACxB,IAAI,CAAC,QAAQ,gBAAA,CAAiB,OAAA,EAAS;4BACrC,MAAM,aAAa,IAAI,YAAY,cAAc;4BACjD,SAAS,aAAA,CAAc,UAAU;4BACjC,QAAQ,gBAAA,CAAiB,OAAA,GAAU;wBACrC;oBACF;;gBAEA,MAAM;4DAAe,MAAM;wBACzB,IAAI,QAAQ,gBAAA,CAAiB,OAAA,EAAS;4BACpC,MAAM,cAAc,IAAI,YAAY,eAAe;4BACnD,SAAS,aAAA,CAAc,WAAW;4BAClC,QAAQ,gBAAA,CAAiB,OAAA,GAAU;wBACrC;oBACF;;gBAEA,MAAM;oEAAuB,CAAC,UAAsB;wBAClD,MAAM,uBAAuB,CAAC,QAAQ,QAAA,CAAS,MAAM,aAA4B;wBACjF,IAAI,qBAAsB,CAAA,aAAa;oBACzC;;gBAEA,MAAM;wEAA2B,MAAM;wBACrC,MAAM,gBAAgB,QAAQ,QAAA,CAAS,SAAS,aAAa;wBAC7D,IAAI,CAAC,cAAe,CAAA,aAAa;oBACnC;;gBAGA,QAAQ,gBAAA,CAAiB,WAAW,WAAW;gBAC/C,QAAQ,gBAAA,CAAiB,YAAY,oBAAoB;gBACzD,QAAQ,gBAAA,CAAiB,eAAe,WAAW;gBACnD,QAAQ,gBAAA,CAAiB,gBAAgB,wBAAwB;gBACjE,OAAO,gBAAA,CAAiB,QAAQ,WAAW;gBAC3C,OAAO,gBAAA,CAAiB,SAAS,YAAY;gBAC7C;+CAAO,MAAM;wBACX,QAAQ,mBAAA,CAAoB,WAAW,WAAW;wBAClD,QAAQ,mBAAA,CAAoB,YAAY,oBAAoB;wBAC5D,QAAQ,mBAAA,CAAoB,eAAe,WAAW;wBACtD,QAAQ,mBAAA,CAAoB,gBAAgB,wBAAwB;wBACpE,OAAO,mBAAA,CAAoB,QAAQ,WAAW;wBAC9C,OAAO,mBAAA,CAAoB,SAAS,YAAY;oBAClD;;YACF;QACF;kCAAG;QAAC;QAAW,QAAQ,gBAAgB;KAAC;IAExC,MAAM,gMAAoC,cAAA;kEACxC,CAAC,EAAE,gBAAA,CAAiB,CAAA,KAAsD;YACxE,MAAM,aAAa,SAAS;YAC5B,MAAM,qBAAqB,WAAW,GAAA;6FAAI,CAAC,cAAc;oBACvD,MAAM,YAAY,UAAU,GAAA,CAAI,OAAA;oBAChC,MAAM,0BAA0B;wBAAC,WAAW;2BAAG,sBAAsB,SAAS,CAAC;qBAAA;oBAC/E,OAAO,qBAAqB,aACxB,0BACA,wBAAwB,OAAA,CAAQ;gBACtC,CAAC;;YACD,OAAA,CACE,qBAAqB,aAAa,mBAAmB,OAAA,CAAQ,IAAI,kBAAA,EACjE,IAAA,CAAK;QACT;iEACA;QAAC,QAAQ;KAAA;QAGL,0KAAA;mCAAU,MAAM;YACpB,MAAM,WAAW,IAAI,OAAA;YAIrB,IAAI,UAAU;gBACZ,MAAM;6DAAgB,CAAC,UAAyB;wBAC9C,MAAM,YAAY,MAAM,MAAA,IAAU,MAAM,OAAA,IAAW,MAAM,OAAA;wBACzD,MAAM,WAAW,MAAM,GAAA,KAAQ,SAAS,CAAC;wBAEzC,IAAI,UAAU;4BACZ,MAAM,iBAAiB,SAAS,aAAA;4BAChC,MAAM,qBAAqB,MAAM,QAAA;4BACjC,MAAM,mBAAmB,MAAM,MAAA,KAAW;4BAI1C,IAAI,oBAAoB,oBAAoB;gCAC1C,kBAAkB,OAAA,EAAS,MAAM;gCACjC;4BACF;4BAEA,MAAM,mBAAmB,qBAAqB,cAAc;4BAC5D,MAAM,mBAAmB,4BAA4B;gCAAE;4BAAiB,CAAC;4BACzE,MAAM,QAAQ,iBAAiB,SAAA;+EAAU,CAAC,YAAc,cAAc,cAAc;;4BACpF,IAAI,WAAW,iBAAiB,KAAA,CAAM,QAAQ,CAAC,CAAC,GAAG;gCACjD,MAAM,cAAA,CAAe;4BACvB,OAAO;gCAIL,qBACI,kBAAkB,OAAA,EAAS,MAAM,IACjC,kBAAkB,OAAA,EAAS,MAAM;4BACvC;wBACF;oBACF;;gBAGA,SAAS,gBAAA,CAAiB,WAAW,aAAa;gBAClD;+CAAO,IAAM,SAAS,mBAAA,CAAoB,WAAW,aAAa;;YACpE;QACF;kCAAG;QAAC;QAAU,2BAA2B;KAAC;IAE1C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,sLAAkB,SAAA,EAAjB;QACC,KAAK;QACL,MAAK;QACL,cAAY,MAAM,OAAA,CAAQ,YAAY,WAAW;QAEjD,UAAU,CAAA;QAGV,OAAO;YAAE,eAAe,YAAY,KAAA,IAAY;QAAO;QAEtD,UAAA;YAAA,aACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,YAAA;gBACC,KAAK;gBACL,4BAA4B,MAAM;oBAChC,MAAM,qBAAqB,4BAA4B;wBACrD,kBAAkB;oBACpB,CAAC;oBACD,WAAW,kBAAkB;gBAC/B;YAAA;YAOJ,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO;gBACtB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,EAAA,EAAV;oBAAa,UAAU,CAAA;oBAAK,GAAG,aAAA;oBAAe,KAAK;gBAAA,CAAc;YAAA,CACpE;YACC,aACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,YAAA;gBACC,KAAK;gBACL,4BAA4B,MAAM;oBAChC,MAAM,qBAAqB,4BAA4B;wBACrD,kBAAkB;oBACpB,CAAC;oBACD,WAAW,kBAAkB;gBAC/B;YAAA;SACF;IAAA;AAIR;AAGF,cAAc,WAAA,GAAc;AAI5B,IAAM,mBAAmB;AAQzB,IAAM,aAAmB,+KAAA,EACvB,CAAC,OAAO,iBAAiB;IACvB,MAAM,EAAE,YAAA,EAAc,0BAAA,EAA4B,GAAG,WAAW,CAAA,GAAI;IACpE,MAAM,UAAU,wBAAwB,kBAAkB,YAAY;IAEtE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,oLAAC,iBAAA,EAAA;QACC,eAAW;QACX,UAAU;QACT,GAAG,UAAA;QACJ,KAAK;QAEL,OAAO;YAAE,UAAU;QAAQ;QAC3B,SAAS,CAAC,UAAU;YAClB,MAAM,qBAAqB,MAAM,aAAA;YACjC,MAAM,6BAA6B,CAAC,QAAQ,QAAA,EAAU,SAAS,kBAAkB;YACjF,IAAI,2BAA4B,CAAA,2BAA2B;QAC7D;IAAA;AAGN;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,aAAa;AACnB,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB;AAcxB,IAAM,0KAAc,aAAA,EAClB,CAAC,OAAgC,iBAAiB;IAChD,MAAM,EAAE,UAAA,EAAY,MAAM,QAAA,EAAU,WAAA,EAAa,YAAA,EAAc,GAAG,WAAW,CAAA,GAAI;IACjF,MAAM,CAAC,MAAM,OAAO,CAAA,mMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IACD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc;QAC/B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YACC;YACC,GAAG,UAAA;YACJ,KAAK;YACL,SAAS,IAAM,QAAQ,KAAK;YAC5B,mMAAS,iBAAA,EAAe,MAAM,OAAO;YACrC,oMAAU,iBAAA,EAAe,MAAM,QAAQ;YACvC,kLAAc,uBAAA,EAAqB,MAAM,YAAA,EAAc,CAAC,UAAU;gBAChE,MAAM,aAAA,CAAc,YAAA,CAAa,cAAc,OAAO;YACxD,CAAC;YACD,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAC9D,MAAM,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,GAAI,MAAM,MAAA,CAAO,KAAA;gBAC9B,MAAM,aAAA,CAAc,YAAA,CAAa,cAAc,MAAM;gBACrD,MAAM,aAAA,CAAc,KAAA,CAAM,WAAA,CAAY,8BAA8B,GAAG,CAAC,CAAA,EAAA,CAAI;gBAC5E,MAAM,aAAA,CAAc,KAAA,CAAM,WAAA,CAAY,8BAA8B,GAAG,CAAC,CAAA,EAAA,CAAI;YAC9E,CAAC;YACD,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gBAClE,MAAM,aAAA,CAAc,YAAA,CAAa,cAAc,QAAQ;gBACvD,MAAM,aAAA,CAAc,KAAA,CAAM,cAAA,CAAe,4BAA4B;gBACrE,MAAM,aAAA,CAAc,KAAA,CAAM,cAAA,CAAe,4BAA4B;gBACrE,MAAM,aAAA,CAAc,KAAA,CAAM,cAAA,CAAe,2BAA2B;gBACpE,MAAM,aAAA,CAAc,KAAA,CAAM,cAAA,CAAe,2BAA2B;YACtE,CAAC;YACD,gLAAY,uBAAA,EAAqB,MAAM,UAAA,EAAY,CAAC,UAAU;gBAC5D,MAAM,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,GAAI,MAAM,MAAA,CAAO,KAAA;gBAC9B,MAAM,aAAA,CAAc,YAAA,CAAa,cAAc,KAAK;gBACpD,MAAM,aAAA,CAAc,KAAA,CAAM,cAAA,CAAe,4BAA4B;gBACrE,MAAM,aAAA,CAAc,KAAA,CAAM,cAAA,CAAe,4BAA4B;gBACrE,MAAM,aAAA,CAAc,KAAA,CAAM,WAAA,CAAY,6BAA6B,GAAG,CAAC,CAAA,EAAA,CAAI;gBAC3E,MAAM,aAAA,CAAc,KAAA,CAAM,WAAA,CAAY,6BAA6B,GAAG,CAAC,CAAA,EAAA,CAAI;gBAC3E,QAAQ,KAAK;YACf,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,MAAM,WAAA,GAAc;AASpB,IAAM,CAAC,0BAA0B,0BAA0B,CAAA,GAAI,mBAAmB,YAAY;IAC5F,UAAU,EAAC;AACb,CAAC;AAsBD,IAAM,8KAAkB,aAAA,EACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EACJ,YAAA,EACA,OAAO,YAAA,EACP,UAAU,YAAA,EACV,IAAA,EACA,OAAA,EACA,eAAA,EACA,OAAA,EACA,QAAA,EACA,YAAA,EACA,WAAA,EACA,aAAA,EACA,UAAA,EACA,GAAG,YACL,GAAI;IACJ,MAAM,UAAU,wBAAwB,YAAY,YAAY;IAChE,MAAM,CAAC,MAAM,OAAO,CAAA,qKAAU,WAAA,EAAkC,IAAI;IACpE,MAAM,kMAAe,kBAAA,EAAgB;mDAAc,CAACA,QAAS,QAAQA,KAAI,CAAC;;IAC1E,MAAM,mBAAwB,0KAAA,EAAwC,IAAI;IAC1E,MAAM,kLAAsB,SAAA,EAAwC,IAAI;IACxE,MAAM,WAAW,gBAAgB,QAAQ,QAAA;IACzC,MAAM,2LAA+B,SAAA,EAAO,CAAC;IAC7C,MAAM,+LAAmC,SAAA,EAAO,QAAQ;IACxD,MAAM,kLAAsB,SAAA,EAAO,CAAC;IACpC,MAAM,EAAE,UAAA,EAAY,aAAA,CAAc,CAAA,GAAI;IACtC,MAAM,wMAAc,iBAAA;iDAAe,MAAM;YAGvC,MAAM,iBAAiB,MAAM,SAAS,SAAS,aAAa;YAC5D,IAAI,eAAgB,CAAA,QAAQ,QAAA,EAAU,MAAM;YAC5C,QAAQ;QACV,CAAC;;IAED,MAAM,+KAAmB,cAAA;6CACvB,CAACC,cAAqB;YACpB,IAAI,CAACA,aAAYA,cAAa,SAAU,CAAA;YACxC,OAAO,YAAA,CAAa,cAAc,OAAO;YACzC,uBAAuB,OAAA,GAAA,AAAU,aAAA,GAAA,IAAI,KAAK,EAAE,OAAA,CAAQ;YACpD,cAAc,OAAA,GAAU,OAAO,UAAA,CAAW,aAAaA,SAAQ;QACjE;4CACA;QAAC,WAAW;KAAA;sKAGR,YAAA;+BAAU,MAAM;YACpB,MAAM,WAAW,QAAQ,QAAA;YACzB,IAAI,UAAU;gBACZ,MAAM;wDAAe,MAAM;wBACzB,WAAW,2BAA2B,OAAO;wBAC7C,WAAW;oBACb;;gBACA,MAAM;uDAAc,MAAM;wBACxB,MAAM,cAAA,AAAc,aAAA,GAAA,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAI,uBAAuB,OAAA;wBAClE,2BAA2B,OAAA,GAAU,2BAA2B,OAAA,GAAU;wBAC1E,OAAO,YAAA,CAAa,cAAc,OAAO;wBACzC,UAAU;oBACZ;;gBACA,SAAS,gBAAA,CAAiB,gBAAgB,WAAW;gBACrD,SAAS,gBAAA,CAAiB,iBAAiB,YAAY;gBACvD;2CAAO,MAAM;wBACX,SAAS,mBAAA,CAAoB,gBAAgB,WAAW;wBACxD,SAAS,mBAAA,CAAoB,iBAAiB,YAAY;oBAC5D;;YACF;QACF;8BAAG;QAAC,QAAQ,QAAA;QAAU;QAAU;QAAS;QAAU,UAAU;KAAC;sKAKxD,YAAA;+BAAU,MAAM;YACpB,IAAI,QAAQ,CAAC,QAAQ,gBAAA,CAAiB,OAAA,CAAS,CAAA,WAAW,QAAQ;QACpE;8BAAG;QAAC;QAAM;QAAU,QAAQ,gBAAA;QAAkB,UAAU;KAAC;QAEnD,0KAAA;+BAAU,MAAM;YACpB,WAAW;YACX;uCAAO,IAAM,cAAc;;QAC7B;8BAAG;QAAC;QAAY,aAAa;KAAC;IAE9B,MAAM,sBAA4B,4KAAA;kDAAQ,MAAM;YAC9C,OAAO,OAAO,uBAAuB,IAAI,IAAI;QAC/C;iDAAG;QAAC,IAAI;KAAC;IAET,IAAI,CAAC,QAAQ,QAAA,CAAU,CAAA,OAAO;IAE9B,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;QACG,UAAA;YAAA,uBACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,eAAA;gBACC;gBAEA,MAAK;gBACL,aAAW,SAAS,eAAe,cAAc;gBACjD,eAAW;gBAEV,UAAA;YAAA;YAIL,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,0BAAA;gBAAyB,OAAO;gBAAc,SAAS;gBACrD,mLAAS,eAAA,EACR,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;oBAAoB,OAAO;oBAC1B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,sLAAkB,OAAA,EAAjB;wBACC,SAAO;wBACP,qLAAiB,uBAAA,EAAqB,iBAAiB,MAAM;4BAC3D,IAAI,CAAC,QAAQ,8BAAA,CAA+B,OAAA,CAAS,CAAA,YAAY;4BACjE,QAAQ,8BAAA,CAA+B,OAAA,GAAU;wBACnD,CAAC;wBAED,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,EAAA,EAAV;4BAEC,MAAK;4BACL,aAAU;4BACV,eAAW;4BACX,UAAU;4BACV,cAAY,OAAO,SAAS;4BAC5B,wBAAsB,QAAQ,cAAA;4BAC7B,GAAG,UAAA;4BACJ,KAAK;4BACL,OAAO;gCAAE,YAAY;gCAAQ,aAAa;gCAAQ,GAAG,MAAM,KAAA;4BAAM;4BACjE,8KAAW,wBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gCAC1D,IAAI,MAAM,GAAA,KAAQ,SAAU,CAAA;gCAC5B,kBAAkB,MAAM,WAAW;gCACnC,IAAI,CAAC,MAAM,WAAA,CAAY,gBAAA,EAAkB;oCACvC,QAAQ,8BAAA,CAA+B,OAAA,GAAU;oCACjD,YAAY;gCACd;4BACF,CAAC;4BACD,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gCAClE,IAAI,MAAM,MAAA,KAAW,EAAG,CAAA;gCACxB,gBAAgB,OAAA,GAAU;oCAAE,GAAG,MAAM,OAAA;oCAAS,GAAG,MAAM,OAAA;gCAAQ;4BACjE,CAAC;4BACD,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gCAClE,IAAI,CAAC,gBAAgB,OAAA,CAAS,CAAA;gCAC9B,MAAM,IAAI,MAAM,OAAA,GAAU,gBAAgB,OAAA,CAAQ,CAAA;gCAClD,MAAM,IAAI,MAAM,OAAA,GAAU,gBAAgB,OAAA,CAAQ,CAAA;gCAClD,MAAM,sBAAsB,QAAQ,cAAc,OAAO;gCACzD,MAAM,oBAAoB;oCAAC;oCAAQ,OAAO;iCAAA,CAAE,QAAA,CAAS,QAAQ,cAAc;gCAC3E,MAAM,QAAQ;oCAAC;oCAAQ,IAAI;iCAAA,CAAE,QAAA,CAAS,QAAQ,cAAc,IACxD,KAAK,GAAA,GACL,KAAK,GAAA;gCACT,MAAM,WAAW,oBAAoB,MAAM,GAAG,CAAC,IAAI;gCACnD,MAAM,WAAW,CAAC,oBAAoB,MAAM,GAAG,CAAC,IAAI;gCACpD,MAAM,kBAAkB,MAAM,WAAA,KAAgB,UAAU,KAAK;gCAC7D,MAAM,QAAQ;oCAAE,GAAG;oCAAU,GAAG;gCAAS;gCACzC,MAAM,cAAc;oCAAE,eAAe;oCAAO;gCAAM;gCAClD,IAAI,qBAAqB;oCACvB,cAAc,OAAA,GAAU;oCACxB,6BAA6B,kBAAkB,aAAa,aAAa;wCACvE,UAAU;oCACZ,CAAC;gCACH,OAAA,IAAW,mBAAmB,OAAO,QAAQ,cAAA,EAAgB,eAAe,GAAG;oCAC7E,cAAc,OAAA,GAAU;oCACxB,6BAA6B,mBAAmB,cAAc,aAAa;wCACzE,UAAU;oCACZ,CAAC;oCACA,MAAM,MAAA,CAAuB,iBAAA,CAAkB,MAAM,SAAS;gCACjE,OAAA,IAAW,KAAK,GAAA,CAAI,CAAC,IAAI,mBAAmB,KAAK,GAAA,CAAI,CAAC,IAAI,iBAAiB;oCAGzE,gBAAgB,OAAA,GAAU;gCAC5B;4BACF,CAAC;4BACD,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gCAC9D,MAAM,QAAQ,cAAc,OAAA;gCAC5B,MAAM,SAAS,MAAM,MAAA;gCACrB,IAAI,OAAO,iBAAA,CAAkB,MAAM,SAAS,GAAG;oCAC7C,OAAO,qBAAA,CAAsB,MAAM,SAAS;gCAC9C;gCACA,cAAc,OAAA,GAAU;gCACxB,gBAAgB,OAAA,GAAU;gCAC1B,IAAI,OAAO;oCACT,MAAM,QAAQ,MAAM,aAAA;oCACpB,MAAM,cAAc;wCAAE,eAAe;wCAAO;oCAAM;oCAClD,IACE,mBAAmB,OAAO,QAAQ,cAAA,EAAgB,QAAQ,cAAc,GACxE;wCACA,6BAA6B,iBAAiB,YAAY,aAAa;4CACrE,UAAU;wCACZ,CAAC;oCACH,OAAO;wCACL,6BACE,oBACA,eACA,aACA;4CACE,UAAU;wCACZ;oCAEJ;oCAGA,MAAM,gBAAA,CAAiB,SAAS,CAACC,SAAUA,OAAM,cAAA,CAAe,GAAG;wCACjE,MAAM;oCACR,CAAC;gCACH;4BACF,CAAC;wBAAA;oBACH;gBACF,CACF,GACA,QAAQ,QAAA;YACV,CACF;SAAA;IAAA,CACF;AAEJ;AASF,IAAM,gBAA8C,CAAC,UAA2C;IAC9F,MAAM,EAAE,YAAA,EAAc,QAAA,EAAU,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,wBAAwB,YAAY,YAAY;IAChE,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,GAAU,6KAAA,EAAS,KAAK;IACxE,MAAM,CAAC,aAAa,cAAc,CAAA,qKAAU,WAAA,EAAS,KAAK;IAG1D;sCAAa,IAAM,sBAAsB,IAAI,CAAC;;sKAGxC,YAAA;mCAAU,MAAM;YACpB,MAAM,QAAQ,OAAO,UAAA;iDAAW,IAAM,eAAe,IAAI;gDAAG,GAAI;YAChE;2CAAO,IAAM,OAAO,YAAA,CAAa,KAAK;;QACxC;kCAAG,CAAC,CAAC;IAEL,OAAO,cAAc,OACnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,wKAAC,SAAA,EAAA;QAAO,SAAO;QACb,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,oLAAC,iBAAA,EAAA;YAAgB,GAAG,aAAA;YACjB,UAAA,sBACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;gBACG,UAAA;oBAAA,QAAQ,KAAA;oBAAM;oBAAE;iBAAA;YAAA,CACnB;QAAA,CAEJ;IAAA,CACF;AAEJ;AAMA,IAAM,aAAa;AAMnB,IAAM,+KAAmB,aAAA,EACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,YAAA,EAAc,GAAG,WAAW,CAAA,GAAI;IACxC,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC3D;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,mBAAmB;AAKzB,IAAM,qLAAyB,aAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,YAAA,EAAc,GAAG,iBAAiB,CAAA,GAAI;IAC9C,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,gBAAA;QAAkB,KAAK;IAAA,CAAc;AACjE;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,cAAc;AAapB,IAAM,eAAoB,8KAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,OAAA,EAAS,GAAG,YAAY,CAAA,GAAI;IAEpC,IAAI,CAAC,QAAQ,IAAA,CAAK,GAAG;QACnB,QAAQ,KAAA,CACN,CAAA,uCAAA,EAA0C,WAAW,CAAA,kCAAA,CAAA;QAEvD,OAAO;IACT;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;QAAqB;QAAkB,SAAO;QAC7C,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,YAAA;YAAY,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA,CAClD;AAEJ;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAMnB,IAAM,8KAAmB,cAAA,EACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,YAAA,EAAc,GAAG,WAAW,CAAA,GAAI;IACxC,MAAM,qBAAqB,2BAA2B,YAAY,YAAY;IAE9E,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;QAAqB,SAAO;QAC3B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACJ,GAAG,UAAA;YACJ,KAAK;YACL,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,mBAAmB,OAAO;QAAA;IACzE,CACF;AAEJ;AAGF,WAAW,WAAA,GAAc;AASzB,IAAM,yLAA6B,aAAA,EAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EAAE,YAAA,EAAc,OAAA,EAAS,GAAG,qBAAqB,CAAA,GAAI;IAE3D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qLAAA,CAAU,GAAA,EAAV;QACC,qCAAkC;QAClC,iCAA+B,WAAW,KAAA;QACzC,GAAG,oBAAA;QACJ,KAAK;IAAA;AAGX,CAAC;AAED,SAAS,uBAAuB,SAAA,EAAwB;IACtD,MAAM,cAAwB,CAAC,CAAA;IAC/B,MAAM,aAAa,MAAM,IAAA,CAAK,UAAU,UAAU;IAElD,WAAW,OAAA,CAAQ,CAAC,SAAS;QAC3B,IAAI,KAAK,QAAA,KAAa,KAAK,SAAA,IAAa,KAAK,WAAA,CAAa,CAAA,YAAY,IAAA,CAAK,KAAK,WAAW;QAC3F,IAAI,cAAc,IAAI,GAAG;YACvB,MAAM,WAAW,KAAK,UAAA,IAAc,KAAK,MAAA,IAAU,KAAK,KAAA,CAAM,OAAA,KAAY;YAC1E,MAAM,aAAa,KAAK,OAAA,CAAQ,yBAAA,KAA8B;YAE9D,IAAI,CAAC,UAAU;gBACb,IAAI,YAAY;oBACd,MAAM,UAAU,KAAK,OAAA,CAAQ,qBAAA;oBAC7B,IAAI,QAAS,CAAA,YAAY,IAAA,CAAK,OAAO;gBACvC,OAAO;oBACL,YAAY,IAAA,CAAK,GAAG,uBAAuB,IAAI,CAAC;gBAClD;YACF;QACF;IACF,CAAC;IAID,OAAO;AACT;AAIA,SAAS,6BAIP,IAAA,EACA,OAAA,EACA,MAAA,EACA,EAAE,QAAA,CAAS,CAAA,EACX;IACA,MAAM,gBAAgB,OAAO,aAAA,CAAc,aAAA;IAC3C,MAAM,QAAQ,IAAI,YAAY,MAAM;QAAE,SAAS;QAAM,YAAY;QAAM;IAAO,CAAC;IAC/E,IAAI,QAAS,CAAA,cAAc,gBAAA,CAAiB,MAAM,SAA0B;QAAE,MAAM;IAAK,CAAC;IAE1F,IAAI,UAAU;QACZ,CAAA,GAAA,wKAAA,CAAA,8BAAA,EAA4B,eAAe,KAAK;IAClD,OAAO;QACL,cAAc,aAAA,CAAc,KAAK;IACnC;AACF;AAEA,IAAM,qBAAqB,CACzB,OACA,WACA,YAAY,CAAA,KACT;IACH,MAAM,SAAS,KAAK,GAAA,CAAI,MAAM,CAAC;IAC/B,MAAM,SAAS,KAAK,GAAA,CAAI,MAAM,CAAC;IAC/B,MAAM,WAAW,SAAS;IAC1B,IAAI,cAAc,UAAU,cAAc,SAAS;QACjD,OAAO,YAAY,SAAS;IAC9B,OAAO;QACL,OAAO,CAAC,YAAY,SAAS;IAC/B;AACF;AAEA,SAAS,aAAa,WAAW,KAAO,CAAD,AAAC,EAAG;IACzC,MAAM,+LAAK,iBAAA,EAAe,QAAQ;IAClC,CAAA,GAAA,sLAAA,CAAA,kBAAA;wCAAgB,MAAM;YACpB,IAAI,OAAO;YACX,IAAI,OAAO;YACX,OAAO,OAAO,qBAAA;gDAAsB,IAAO,OAAO,OAAO,qBAAA,CAAsB,EAAE,CAAE;;YACnF;gDAAO,MAAM;oBACX,OAAO,oBAAA,CAAqB,IAAI;oBAChC,OAAO,oBAAA,CAAqB,IAAI;gBAClC;;QACF;uCAAG;QAAC,EAAE;KAAC;AACT;AAEA,SAAS,cAAc,IAAA,EAAgC;IACrD,OAAO,KAAK,QAAA,KAAa,KAAK,YAAA;AAChC;AAYA,SAAS,sBAAsB,SAAA,EAAwB;IACrD,MAAM,QAAuB,CAAC,CAAA;IAC9B,MAAM,SAAS,SAAS,gBAAA,CAAiB,WAAW,WAAW,YAAA,EAAc;QAC3E,YAAY,CAAC,SAAc;YACzB,MAAM,gBAAgB,KAAK,OAAA,KAAY,WAAW,KAAK,IAAA,KAAS;YAChE,IAAI,KAAK,QAAA,IAAY,KAAK,MAAA,IAAU,cAAe,CAAA,OAAO,WAAW,WAAA;YAIrE,OAAO,KAAK,QAAA,IAAY,IAAI,WAAW,aAAA,GAAgB,WAAW,WAAA;QACpE;IACF,CAAC;IACD,MAAO,OAAO,QAAA,CAAS,EAAG,MAAM,IAAA,CAAK,OAAO,WAA0B;IAGtE,OAAO;AACT;AAEA,SAAS,WAAW,UAAA,EAA2B;IAC7C,MAAM,2BAA2B,SAAS,aAAA;IAC1C,OAAO,WAAW,IAAA,CAAK,CAAC,cAAc;QAEpC,IAAI,cAAc,yBAA0B,CAAA,OAAO;QACnD,UAAU,KAAA,CAAM;QAChB,OAAO,SAAS,aAAA,KAAkB;IACpC,CAAC;AACH;AAEA,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAMC,QAAO;AACb,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,SAAS;AACf,IAAM,QAAQ", "ignoreList": [0], "debugId": null}}]}