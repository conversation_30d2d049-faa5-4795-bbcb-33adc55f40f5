(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5034],{13157:(e,s,a)=>{Promise.resolve().then(a.bind(a,72961))},29797:(e,s,a)=>{"use strict";a.d(s,{$o:()=>f,Eb:()=>p,Iu:()=>m,WA:()=>h,cU:()=>u,dK:()=>c,n$:()=>x});var l=a(95155),t=a(965),r=a(73158),i=a(3561),n=a(12115),d=a(30285),o=a(54036);let c=n.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{className:(0,o.cn)("flex justify-center",a),ref:s,...t})});c.displayName="Pagination";let m=n.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("ul",{className:(0,o.cn)("flex flex-row items-center gap-1",a),ref:s,...t})});m.displayName="PaginationContent";let u=n.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("li",{className:(0,o.cn)("",a),ref:s,...t})});u.displayName="PaginationItem";let x=n.forwardRef((e,s)=>{let{className:a,isActive:t,...r}=e;return(0,l.jsx)(d.$,{"aria-current":t?"page":void 0,className:(0,o.cn)("h-9 w-9",a),ref:s,size:"icon",variant:t?"outline":"ghost",...r})});x.displayName="PaginationLink";let p=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,l.jsxs)(d.$,{className:(0,o.cn)("h-9 w-9 gap-1",a),ref:s,size:"icon",variant:"ghost",...r,children:[(0,l.jsx)(t.A,{className:"size-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Previous page"})]})});p.displayName="PaginationPrevious";let h=n.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsxs)(d.$,{className:(0,o.cn)("h-9 w-9 gap-1",a),ref:s,size:"icon",variant:"ghost",...t,children:[(0,l.jsx)(r.A,{className:"size-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Next page"})]})});h.displayName="PaginationNext";let g=n.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",a),ref:s,...t,children:[(0,l.jsx)(i.A,{className:"size-4"}),(0,l.jsx)("span",{className:"sr-only",children:"More pages"})]})});function f(e){let{className:s,currentPage:a,onPageChange:t,totalPages:r}=e,i=(()=>{let e=[];e.push(1);let s=Math.max(2,a-1),l=Math.min(r-1,a+1);s>2&&e.push("ellipsis1");for(let a=s;a<=l;a++)e.push(a);return l<r-1&&e.push("ellipsis2"),r>1&&e.push(r),e})();return r<=1?null:(0,l.jsx)(c,{className:s,children:(0,l.jsxs)(m,{children:[(0,l.jsx)(u,{children:(0,l.jsx)(p,{"aria-disabled":1===a?"true":void 0,"aria-label":"Go to previous page",disabled:1===a,onClick:()=>t(a-1)})}),i.map((e,s)=>"ellipsis1"===e||"ellipsis2"===e?(0,l.jsx)(u,{children:(0,l.jsx)(g,{})},"ellipsis-".concat(s)):(0,l.jsx)(u,{children:(0,l.jsx)(x,{"aria-label":"Go to page ".concat(e),isActive:a===e,onClick:()=>t(e),children:e})},"page-".concat(e))),(0,l.jsx)(u,{children:(0,l.jsx)(h,{"aria-disabled":a===r?"true":void 0,"aria-label":"Go to next page",disabled:a===r,onClick:()=>t(a+1)})})]})})}g.displayName="PaginationEllipsis"},33271:(e,s,a)=>{"use strict";a.d(s,{k:()=>h});var l=a(95155),t=a(18018),r=a(50172),i=a(68718),n=a(15300),d=a(60679),o=a(12115),c=a(6560),m=a(44838),u=a(53712),x=a(54036),p=a(16146);function h(e){let{className:s,csvData:a,enableCsv:h=!1,entityId:g,fileName:f,reportContentId:b,reportType:j,tableId:N}=e,[y,v]=(0,o.useState)(!1),[w,k]=(0,o.useState)(!1),{showFormSuccess:C,showFormError:A}=(0,u.t6)(),S=async()=>{v(!0);try{let e="/api/reports/".concat(j).concat(g?"/".concat(g):""),s=document.createElement("a");s.href=e,s.download="".concat(f,".pdf"),s.target="_blank",document.body.append(s),s.click(),s.remove(),C({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),A("PDF download failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{v(!1)}},P=async()=>{if(h){k(!0);try{if((null==a?void 0:a.data)&&a.headers)(0,p.og)(a.data,a.headers,"".concat(f,".csv"));else if(N){let e=(0,p.tL)(N);(0,p.og)(e.data,e.headers,"".concat(f,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");C({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),A("CSV generation failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{k(!1)}}},E=y||w;return(0,l.jsxs)("div",{className:(0,x.cn)("flex items-center gap-2 no-print",s),children:[(0,l.jsx)(c.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,l.jsx)(t.A,{className:"size-4"})}),(0,l.jsxs)(m.rI,{children:[(0,l.jsx)(m.ty,{asChild:!0,children:(0,l.jsx)(c.r,{actionType:"secondary","aria-label":"Download report",disabled:E,size:"icon",title:"Download Report",children:E?(0,l.jsx)(r.A,{className:"size-4 animate-spin"}):(0,l.jsx)(i.A,{className:"size-4"})})}),(0,l.jsxs)(m.SQ,{align:"end",children:[(0,l.jsxs)(m._2,{disabled:y,onClick:S,children:[y?(0,l.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}):(0,l.jsx)(n.A,{className:"mr-2 size-4"}),(0,l.jsx)("span",{children:"Download PDF"})]}),h&&(0,l.jsxs)(m._2,{disabled:w,onClick:P,children:[w?(0,l.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}):(0,l.jsx)(d.A,{className:"mr-2 size-4"}),(0,l.jsx)("span",{children:"Download CSV"})]})]})]})]})}},43326:(e,s,a)=>{"use strict";a.d(s,{zc:()=>m});var l=a(95155),t=a(24371),r=a(31949),i=a(50594),n=a(12115),d=a(55365),o=a(53712),c=a(50546);function m(e){var s;let{className:a="",context:m,error:u,showToast:x=!1}=e,{showFormError:p}=(0,o.t6)();if(n.useEffect(()=>{if(u&&x){(0,c.u1)(u);let e=(0,c.iG)(u,m);p(e.message,{errorTitle:e.title})}},[u,x,p,m]),!u)return null;let h=(0,c.iG)(u,m),g=(s=h.code||"UNKNOWN_ERROR",["VALIDATION_ERROR","ASSIGNMENT_ERROR","ROLE_ERROR"].some(e=>s.includes(e))?"error":["BUSINESS_RULE_WARNING","DEPRECATION_WARNING"].some(e=>s.includes(e))?"warning":"info");return(0,l.jsxs)(d.Fc,{className:"".concat(a," ").concat(function(e){switch(e){case"error":return"border-destructive/50 text-destructive dark:border-destructive";case"warning":return"border-yellow-500/50 text-yellow-600 dark:border-yellow-500 dark:text-yellow-400";default:return"border-blue-500/50 text-blue-600 dark:border-blue-500 dark:text-blue-400"}}(g)),children:[function(e){switch(e){case"error":return(0,l.jsx)(t.A,{className:"size-4"});case"warning":return(0,l.jsx)(r.A,{className:"size-4"});default:return(0,l.jsx)(i.A,{className:"size-4"})}}(g),(0,l.jsx)(d.XL,{children:h.title}),(0,l.jsxs)(d.TN,{children:[h.message,h.code&&(0,l.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Error Code: ",h.code]}),h.field&&(0,l.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Field: ",h.field]})]})]})}},56819:(e,s,a)=>{"use strict";a.d(s,{z:()=>m});var l=a(95155),t=a(41784),r=a(3567),i=a(31949),n=a(44956),d=a(65064);a(12115);var o=a(1350),c=a(99673);function m(e){let{className:s="",tasks:a,onDelete:m,onBulkDelete:u,onBulkArchive:x}=e,p=e=>{try{return(0,t.GP)(new Date(e),"MMM d, yyyy h:mm a")}catch(e){return"Invalid date"}},h=e=>e.deadline&&(0,r.R)(new Date(e.deadline))&&"Completed"!==e.status&&"Cancelled"!==e.status,g=e=>e.staffEmployeeId?e.staffEmployee?(0,c.DV)(e.staffEmployee):"Staff ID: ".concat(e.staffEmployeeId):"Unassigned",f=[(0,o.BZ)(),(0,o.K)("description","Description",{maxLength:50,className:"max-w-xs"}),(0,o.ZI)("status","Status",{Assigned:{variant:"default",label:"Assigned"},Cancelled:{variant:"secondary",label:"Cancelled"},Completed:{variant:"success",label:"Completed"},In_Progress:{variant:"default",label:"In Progress"},Pending:{variant:"warning",label:"Pending"}}),(0,o.ZI)("priority","Priority",{High:{variant:"destructive",label:"High"},Medium:{variant:"warning",label:"Medium"},Low:{variant:"secondary",label:"Low"}}),{accessorKey:"staffEmployeeId",header:"Assignee",cell:e=>{let{row:s}=e,a=s.original;return(0,l.jsx)("span",{className:a.staffEmployeeId?"":"text-muted-foreground",children:g(a)})}},{accessorKey:"dateTime",header:"Start Time",cell:e=>{let{row:s}=e;return p(s.getValue("dateTime"))}},{accessorKey:"deadline",header:"Deadline",cell:e=>{let{row:s}=e,a=s.original,t=a.deadline;if(!t)return(0,l.jsx)("span",{className:"text-muted-foreground",children:"No deadline"});let r=h(a);return(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("span",{className:r?"text-red-600 font-medium":"",children:p(t)}),r&&(0,l.jsx)(i.A,{"aria-label":"Overdue",className:"size-4 text-red-600"})]})}},(0,o.K)("location","Location",{maxLength:30,className:"max-w-xs"}),(0,o.Wy)({viewHref:e=>"/tasks/".concat(e.id),editHref:e=>"/tasks/".concat(e.id,"/edit"),...m&&{onDelete:e=>{m(e)}},showCopyId:!0})],b=[...u?[{label:"Delete Selected",icon:e=>{let{className:s}=e;return(0,l.jsx)(n.A,{className:s})},onClick:async e=>{await u(e)},variant:"destructive"}]:[],...x?[{label:"Archive Selected",icon:e=>{let{className:s}=e;return(0,l.jsx)(d.A,{className:s})},onClick:async e=>{await x(e)}}]:[]];return(0,l.jsx)(o.bQ,{data:a,columns:f,className:s,searchPlaceholder:"Search tasks by description or location...",searchColumn:"description",emptyMessage:"No tasks found. Create your first task to get started.",pageSize:20,enableRowSelection:!0,enableBulkActions:b.length>0,bulkActions:b,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",rowClassName:"hover:bg-blue-50/50 dark:hover:bg-blue-900/10"})}},72961:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>z});var l=a(95155),t=a(11518),r=a.n(t),i=a(35079),n=a(31949),d=a(67554),o=a(75074),c=a(25318),m=a(35695),u=a(12115),x=a(50594),p=a(3567),h=a(66695),g=a(54036);function f(e){let{className:s,tasks:a}=e,t=a.length,r=a.reduce((e,s)=>(e[s.status]=(e[s.status]||0)+1,e),{}),i=a.reduce((e,s)=>(e[s.priority]=(e[s.priority]||0)+1,e),{}),n=a.filter(e=>e.deadline&&(0,p.R)(new Date(e.deadline))&&"Completed"!==e.status&&"Cancelled"!==e.status).length,d=a.filter(e=>!e.staffEmployeeId&&"Completed"!==e.status&&"Cancelled"!==e.status).length,o={Assigned:"bg-blue-100 text-blue-800",Cancelled:"bg-gray-100 text-gray-800",Completed:"bg-green-100 text-green-800",In_Progress:"bg-purple-100 text-purple-800",Pending:"bg-yellow-100 text-yellow-800"},c={High:"bg-red-100 text-red-800",Low:"bg-blue-100 text-blue-800",Medium:"bg-orange-100 text-orange-800"};return(0,l.jsxs)("div",{className:(0,g.cn)("mt-4 space-y-4",s),children:[(0,l.jsxs)("div",{className:"summary-grid grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-5",children:[(0,l.jsx)(b,{className:"bg-gray-50",label:"Total Tasks",textColor:"text-gray-500",value:t}),(0,l.jsx)(b,{className:(0,g.cn)("bg-red-50",n>0?"border-red-200":""),label:"Overdue Tasks",textColor:"text-red-500",value:n}),(0,l.jsx)(b,{className:(0,g.cn)("bg-amber-50",d>0?"border-amber-200":""),label:"Unassigned Tasks",textColor:"text-amber-500",value:d}),(0,l.jsx)(b,{className:"bg-purple-50",label:"In Progress",textColor:"text-purple-500",value:r.In_Progress||0}),(0,l.jsx)(b,{className:"bg-green-50",label:"Completed",textColor:"text-green-500",value:r.Completed||0})]}),(0,l.jsx)(h.Zp,{className:"overflow-hidden",children:(0,l.jsxs)(h.Wu,{className:"p-4",children:[(0,l.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"Status Distribution"}),(0,l.jsx)("div",{className:"space-y-2",children:["Pending","Assigned","In_Progress","Completed","Cancelled"].map(e=>(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,l.jsx)("span",{children:e}),(0,l.jsxs)("span",{children:[r[e]||0," tasks (",t?Math.round((r[e]||0)/t*100):0,"%)"]})]}),(0,l.jsx)("div",{className:"h-2 w-full rounded-full bg-gray-200",children:(0,l.jsx)("div",{"aria-label":"".concat(e,": ").concat(r[e]||0," tasks"),className:(0,g.cn)("h-2 rounded-full",o[e]),style:{width:"".concat(t?(r[e]||0)/t*100:0,"%")}})})]},e))})]})}),(0,l.jsx)(h.Zp,{className:"overflow-hidden",children:(0,l.jsxs)(h.Wu,{className:"p-4",children:[(0,l.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"Priority Distribution"}),(0,l.jsx)("div",{className:"mt-1 flex flex-wrap gap-2",children:["High","Medium","Low"].map(e=>(0,l.jsxs)("div",{className:(0,g.cn)("text-xs px-3 py-1 rounded-full",c[e]),children:[e,": ",i[e]||0]},e))})]})})]})}function b(e){let{className:s,colSpan:a,label:t,textColor:r="text-gray-500",value:i}=e;return(0,l.jsx)(h.Zp,{className:(0,g.cn)("overflow-hidden",s,a),children:(0,l.jsxs)(h.Wu,{className:"p-2 text-center",children:[(0,l.jsx)("p",{className:"text-2xl font-semibold",children:i}),(0,l.jsx)("p",{className:(0,g.cn)("text-xs",r),children:t})]})})}var j=a(55365),N=a(30285),y=a(43326),v=a(77023),w=a(29797),k=a(56819);function C(e){let{className:s,error:a,isLoading:t,onRetry:r,tasks:i}=e,[n,o]=(0,u.useState)("dateTime"),[c,m]=(0,u.useState)("desc"),[p,g]=(0,u.useState)(1),[b]=(0,u.useState)(10),C={Assigned:2,Cancelled:5,Completed:4,In_Progress:1,Pending:3},A={High:1,Low:3,Medium:2},S=(0,u.useMemo)(()=>[...i].sort((e,s)=>{let a,l;switch(n){case"dateTime":case"deadline":a=e[n]?new Date(e[n]).getTime():0,l=s[n]?new Date(s[n]).getTime():0;break;case"description":case"location":var t,r;a=(null==(t=e[n])?void 0:t.toLowerCase())||"",l=(null==(r=s[n])?void 0:r.toLowerCase())||"";break;case"priority":a=A[e.priority]||999,l=A[s.priority]||999;break;case"staffEmployeeId":a=e.staffEmployeeId||999,l=s.staffEmployeeId||999;break;case"status":a=C[e.status]||999,l=C[s.status]||999;break;default:a=e[n],l=s[n]}let i=null!=a?a:"",d=null!=l?l:"";return i<d?"asc"===c?-1:1:i>d?"asc"===c?1:-1:0}),[i,n,c,C,A]);(0,u.useCallback)(e=>{n===e?m("asc"===c?"desc":"asc"):(o(e),m("asc"))},[n,c]);let P=p*b,E=P-b,T=(0,u.useMemo)(()=>S.slice(E,P),[S,E,P]),I=(0,u.useMemo)(()=>Math.ceil(S.length/b),[S.length,b]),R=(0,u.useCallback)(e=>{g(e)},[]);return((0,u.useEffect)(()=>{g(1)},[i]),t)?(0,l.jsx)("div",{className:"space-y-4","data-testid":"loading-skeleton",children:(0,l.jsx)(v.jt,{count:5,variant:"table"})}):a?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(y.zc,{context:"Loading Tasks",error:a}),(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsxs)(N.$,{"aria-label":"Try loading tasks again",onClick:r,size:"sm",variant:"outline",children:[(0,l.jsx)(d.A,{className:"mr-2 size-4"}),"Try Again"]})})]}):0===i.length?(0,l.jsxs)(j.Fc,{className:"mb-6",variant:"default",children:[(0,l.jsx)(x.A,{className:"size-4"}),(0,l.jsx)(j.XL,{children:"No Tasks"}),(0,l.jsx)(j.TN,{children:"No tasks match your current filters. Try adjusting your search or filter criteria."})]}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(f,{tasks:S}),(0,l.jsx)(h.Zp,{className:"card-print shadow-md",children:(0,l.jsx)(h.Wu,{className:"p-0",children:(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsx)(k.z,{tasks:T})})})}),S.length>b&&(0,l.jsx)("div",{className:"no-print mt-4 flex justify-center",children:(0,l.jsx)(w.$o,{currentPage:p,onPageChange:R,totalPages:I})}),(0,l.jsxs)("div",{className:"no-print text-center text-sm text-muted-foreground",children:["Showing ",E+1," to"," ",Math.min(P,S.length)," of ",S.length," ","tasks"]})]})}var A=a(33271),S=a(62523),P=a(85057),E=a(95647),T=a(59409),I=a(83761),R=a(61051);function z(){return(0,l.jsx)(u.Suspense,{fallback:(0,l.jsx)("div",{className:"py-10 text-center",children:"Loading report..."}),children:(0,l.jsx)(D,{})})}function D(){var e,s,a,t;let x=(0,m.useSearchParams)(),{data:p,error:g,isLoading:f,refetch:b}=(0,R.si)(),{data:y,error:w,isLoading:k,refetch:z}=(0,I.nR)(),D=(0,u.useMemo)(()=>p||[],[p]),L=(0,u.useMemo)(()=>y||[],[y]),[_,M]=(0,u.useState)(""),[F,U]=(0,u.useState)(""),[V,O]=(0,u.useState)("all"),[W,H]=(0,u.useState)("all"),[$,G]=(0,u.useState)("all");(0,u.useEffect)(()=>{document.title="Task Report";let e=(null==x?void 0:x.get("searchTerm"))||"",s=(null==x?void 0:x.get("status"))||"all",a=(null==x?void 0:x.get("priority"))||"all",l=(null==x?void 0:x.get("employee"))||"all";M(e),U(e),O(s),H(a),G(l)},[x]),(0,u.useEffect)(()=>{let e=setTimeout(()=>{U(_)},300);return()=>clearTimeout(e)},[_]);let Z=(0,u.useMemo)(()=>{let e=[...D],s=F.toLowerCase();return"all"!==V&&(e=e.filter(e=>e.status===V)),"all"!==W&&(e=e.filter(e=>e.priority===W)),"all"!==$&&("unassigned"===$||(e=e.filter(e=>{var s;return(null==(s=e.staffEmployeeId)?void 0:s.toString())===$}))),s&&(e=e.filter(e=>{let a=e.staffEmployeeId?L.find(s=>s.id===e.staffEmployeeId):e.driverEmployeeId?L.find(s=>s.id===e.driverEmployeeId):null,l=a?a.fullName||a.name||"Unknown Employee".trim().toLowerCase():"";return e.description&&e.description.toLowerCase().includes(s)||l.includes(s)||e.status.toLowerCase().includes(s)||e.priority.toLowerCase().includes(s)})),e},[D,F,V,W,$,L]),K=(0,u.useCallback)(()=>{b(),z()},[b,z]);return f||k?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(E.z,{description:"Loading task data...",icon:i.A,title:"Tasks Report"}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(v.jt,{count:1,variant:"card"}),(0,l.jsx)(v.jt,{className:"mt-6",count:5,variant:"table"})]})]}):g||w?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(E.z,{description:"Error loading task data.",icon:i.A,title:"Tasks Report"}),(0,l.jsxs)(j.Fc,{className:"mb-6",variant:"destructive",children:[(0,l.jsx)(n.A,{className:"size-4"}),(0,l.jsx)(j.XL,{children:"Error Loading Data"}),(0,l.jsx)(j.TN,{children:(null==g?void 0:g.message)||(null==w?void 0:w.message)||"An unknown error occurred."}),(0,l.jsxs)(N.$,{className:"mt-2",onClick:K,size:"sm",variant:"outline",children:[(0,l.jsx)(d.A,{className:"mr-2 size-4"}),"Try Again"]})]})]}):(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 space-y-6",children:[(0,l.jsx)(E.z,{description:"View and manage all tasks and assignments.",icon:i.A,title:"Tasks Report",children:(0,l.jsx)("div",{className:"jsx-d0a4b0b7e2af8440 no-print flex items-center gap-2",children:(0,l.jsx)(A.k,{enableCsv:Z.length>0,fileName:"tasks-report-".concat(new Date().toISOString().split("T")[0]),reportContentId:"#task-report-content",reportType:"task",tableId:"#tasks-table"})})}),(0,l.jsx)(h.Zp,{className:"no-print shadow-md",children:(0,l.jsxs)(h.Wu,{className:"pt-6",children:[(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 filter-grid mb-6 grid grid-cols-1 items-end gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440",children:[(0,l.jsx)(P.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"status-filter",children:"Filter by Status"}),(0,l.jsxs)(T.l6,{"aria-label":"Filter by status",onValueChange:O,value:V,children:[(0,l.jsx)(T.bq,{className:"w-full",id:"status-filter",children:(0,l.jsx)(T.yv,{placeholder:"All Statuses"})}),(0,l.jsxs)(T.gC,{children:[(0,l.jsx)(T.eb,{value:"all",children:"All Statuses"}),(0,l.jsx)(T.eb,{value:"Pending",children:"Pending"}),(0,l.jsx)(T.eb,{value:"Assigned",children:"Assigned"}),(0,l.jsx)(T.eb,{value:"In_Progress",children:"In Progress"}),(0,l.jsx)(T.eb,{value:"Completed",children:"Completed"}),(0,l.jsx)(T.eb,{value:"Cancelled",children:"Cancelled"})]})]})]}),(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440",children:[(0,l.jsx)(P.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"priority-filter",children:"Filter by Priority"}),(0,l.jsxs)(T.l6,{"aria-label":"Filter by priority",onValueChange:H,value:W,children:[(0,l.jsx)(T.bq,{className:"w-full",id:"priority-filter",children:(0,l.jsx)(T.yv,{placeholder:"All Priorities"})}),(0,l.jsxs)(T.gC,{children:[(0,l.jsx)(T.eb,{value:"all",children:"All Priorities"}),(0,l.jsx)(T.eb,{value:"High",children:"High"}),(0,l.jsx)(T.eb,{value:"Medium",children:"Medium"}),(0,l.jsx)(T.eb,{value:"Low",children:"Low"})]})]})]}),(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440",children:[(0,l.jsx)(P.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"employee-filter",children:"Filter by Assignee"}),(0,l.jsxs)(T.l6,{"aria-label":"Filter by assignee",onValueChange:G,value:$,children:[(0,l.jsx)(T.bq,{className:"w-full",id:"employee-filter",children:(0,l.jsx)(T.yv,{placeholder:"All Assignees"})}),(0,l.jsxs)(T.gC,{children:[(0,l.jsx)(T.eb,{value:"all",children:"All Assignees"}),(0,l.jsx)(T.eb,{value:"unassigned",children:"Unassigned"}),Array.isArray(L)&&L.map(e=>(0,l.jsxs)(T.eb,{value:e.id.toString(),children:[" ",e.fullName||e.name||"Employee ".concat(e.id)]},e.id))]})]})]}),(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 relative",children:[(0,l.jsx)(P.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"search-tasks",children:"Search Tasks"}),(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 relative",children:[(0,l.jsx)(S.p,{"aria-label":"Search tasks",className:"px-10",id:"search-tasks",onChange:e=>M(e.target.value),placeholder:"Search by description, location, notes...",type:"text",value:_}),(0,l.jsx)(o.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground"}),_&&(0,l.jsxs)(N.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>M(""),size:"icon",variant:"ghost",children:[(0,l.jsx)(c.A,{className:"size-4"}),(0,l.jsx)("span",{className:"jsx-d0a4b0b7e2af8440 sr-only",children:"Clear search"})]})]})]})]}),("all"!==V||"all"!==W||"all"!==$||_)&&(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 mt-4 flex items-center justify-between rounded bg-gray-50 p-2",children:[(0,l.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 text-sm",children:[(0,l.jsx)("span",{className:"jsx-d0a4b0b7e2af8440 font-medium",children:"Active Filters:"}),"all"!==V&&(0,l.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:["Status: ",V.replace("_"," ")]}),"all"!==W&&(0,l.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:["Priority: ",W]}),"all"!==$&&(0,l.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:["Assignee:"," ","unassigned"===$?"Unassigned":(L.find(e=>e.id.toString()===$)?(null==(e=L.find(e=>e.id.toString()===$))?void 0:e.fullName)||(null==(s=L.find(e=>e.id.toString()===$))?void 0:s.name)||"Unknown Employee":"")||"Unknown"]}),_&&(0,l.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:['Search: "',_,'"']})]}),(0,l.jsx)(N.$,{"aria-label":"Reset all filters",onClick:()=>{O("all"),H("all"),G("all"),M("")},size:"sm",variant:"ghost",children:"Reset Filters"})]})]})}),(0,l.jsxs)("div",{id:"task-report-content",className:"jsx-d0a4b0b7e2af8440 report-content",children:[(0,l.jsxs)("header",{className:"jsx-d0a4b0b7e2af8440 print-only mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,l.jsx)("h1",{className:"jsx-d0a4b0b7e2af8440 text-3xl font-bold text-gray-800",children:"Tasks Report"}),(0,l.jsxs)("p",{className:"jsx-d0a4b0b7e2af8440 text-md text-gray-600",children:["all"!==V&&"Status: ".concat(V.replace("_"," ")),"all"!==W&&("all"===V?"":" | ")+"Priority: ".concat(W),"all"!==$&&("all"!==V||"all"!==W?" | ":"")+"Assignee: ".concat("unassigned"===$?"Unassigned":(Array.isArray(L)&&L.find(e=>e.id.toString()===$)?(null==(a=L.find(e=>e.id.toString()===$))?void 0:a.fullName)||(null==(t=L.find(e=>e.id.toString()===$))?void 0:t.name)||"Unknown Employee":"")||"Employee "+$)]})]}),(0,l.jsx)(C,{error:g,isLoading:f,onRetry:b,tasks:Z}),(0,l.jsxs)("footer",{className:"jsx-d0a4b0b7e2af8440 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,l.jsxs)("p",{className:"jsx-d0a4b0b7e2af8440",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,l.jsx)("p",{className:"jsx-d0a4b0b7e2af8440",children:"WorkHub - Task Management"})]})]}),(0,l.jsx)(r(),{id:"d0a4b0b7e2af8440",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-description,.print-location,.print-service-col,.print-notes-col{max-width:200px;white-space:normal!important;word-break:break-word}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.overflow-x-auto{overflow-x:auto}.filter-grid{grid-template-columns:1fr!important}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})}},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>o});var l=a(95155),t=a(12115),r=a(40968),i=a(74466),n=a(54036);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.b,{ref:s,className:(0,n.cn)(d(),a),...t})});o.displayName=r.b.displayName},99673:(e,s,a)=>{"use strict";function l(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function t(e){var s,a;if(null==(s=e.fullName)?void 0:s.trim())return e.fullName.trim();if(null==(a=e.name)?void 0:a.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(s," (Role)")}return"Unknown Employee"}function r(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function i(e){return e.replaceAll("_"," ")}a.d(s,{DV:()=>t,fZ:()=>l,s:()=>r,vq:()=>i})}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,3860,9664,1263,5495,1859,6874,5247,6463,7994,4036,4767,8950,3712,7515,3615,5320,6554,1051,8441,1684,7358],()=>s(13157)),_N_E=e.O()}]);