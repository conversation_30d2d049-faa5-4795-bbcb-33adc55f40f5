(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4662],{3561:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(40157).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},10518:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(40157).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19018:(e,r,s)=>{"use strict";s.d(r,{Breadcrumb:()=>c,BreadcrumbItem:()=>o,BreadcrumbLink:()=>m,BreadcrumbList:()=>d,BreadcrumbPage:()=>u,BreadcrumbSeparator:()=>x});var t=s(95155),a=s(99708),n=s(73158),i=(s(3561),s(12115)),l=s(54036);let c=i.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("nav",{"aria-label":"breadcrumb",className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",s),ref:r,...a})});c.displayName="Breadcrumb";let d=i.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("ol",{className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",s),ref:r,...a})});d.displayName="BreadcrumbList";let o=i.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("li",{className:(0,l.cn)("inline-flex items-center gap-1.5",s),ref:r,...a})});o.displayName="BreadcrumbItem";let m=i.forwardRef((e,r)=>{let{asChild:s,className:n,...i}=e,c=s?a.DX:"a";return(0,t.jsx)(c,{className:(0,l.cn)("transition-colors hover:text-foreground",n),ref:r,...i})});m.displayName="BreadcrumbLink";let u=i.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,l.cn)("font-normal text-foreground",s),ref:r,role:"link",...a})});u.displayName="BreadcrumbPage";let x=e=>{let{children:r,className:s,...a}=e;return(0,t.jsx)("span",{"aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",s),role:"presentation",...a,children:null!=r?r:(0,t.jsx)(n.A,{className:"size-4"})})};x.displayName="BreadcrumbSeparator"},26126:(e,r,s)=>{"use strict";s.d(r,{E:()=>l});var t=s(95155),a=s(74466);s(12115);var n=s(54036);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function l(e){let{className:r,variant:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)(i({variant:s}),r),...a})}},30285:(e,r,s)=>{"use strict";s.d(r,{$:()=>d,r:()=>c});var t=s(95155),a=s(12115),n=s(99708),i=s(74466),l=s(54036);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:s,variant:a,size:i,asChild:d=!1,...o}=e,m=d?n.DX:"button";return(0,t.jsx)(m,{className:(0,l.cn)(c({variant:a,size:i,className:s})),ref:r,...o})});d.displayName="Button"},66695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>l,wL:()=>m});var t=s(95155),a=s(12115),n=s(54036);let i=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),ref:r,...a})});i.displayName="Card";let l=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),ref:r,...a})});l.displayName="CardHeader";let c=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),ref:r,...a})});c.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("text-sm text-muted-foreground",s),ref:r,...a})});d.displayName="CardDescription";let o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("p-6 pt-0",s),ref:r,...a})});o.displayName="CardContent";let m=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex items-center p-6 pt-0",s),ref:r,...a})});m.displayName="CardFooter"},72608:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(95155),a=s(18271),n=s(82733),i=s(51362);s(12115);var l=s(27945),c=s(66866),d=s(89440),o=s(30285),m=s(66695),u=s(36846);function x(){let{dashboardLayout:e,fontSize:r,notificationsEnabled:s,resetPreferences:x,tableDensity:f}=(0,u.useUiPreferences)(),{theme:p}=(0,i.D)();return(0,t.jsxs)("div",{className:"container mx-auto space-y-6 py-8",children:[(0,t.jsx)(d.AppBreadcrumb,{}),(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"flex items-center gap-2 text-3xl font-bold",children:[(0,t.jsx)(a.A,{className:"size-8"}),"Settings"]}),(0,t.jsx)("p",{className:"mt-1 text-muted-foreground",children:"Customize your WorkHub experience and preferences"})]})}),(0,t.jsxs)("div",{className:"grid gap-8",children:[(0,t.jsxs)("section",{children:[(0,t.jsxs)("h2",{className:"mb-4 flex items-center gap-2 text-2xl font-semibold",children:[(0,t.jsx)(n.A,{className:"size-6"}),"Theme Preferences"]}),(0,t.jsx)(c.LG,{})]}),(0,t.jsxs)("section",{children:[(0,t.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Display Preferences"}),(0,t.jsx)(l.uq,{})]}),(0,t.jsxs)("section",{children:[(0,t.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Current Configuration"}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"Settings Summary"}),(0,t.jsx)(m.BT,{children:"Overview of your current preferences and settings"})]}),(0,t.jsxs)(m.Wu,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Appearance"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Theme:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:p||"system"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Font Size:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:r})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Notifications"}),(0,t.jsx)("div",{className:"space-y-1",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Enabled:"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:s?"Yes":"No"})]})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Layout"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Table Density:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:f})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-sm",children:"Dashboard:"}),(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:e})]})]})]})]}),(0,t.jsx)("div",{className:"mt-6 border-t pt-6",children:(0,t.jsxs)(o.$,{className:"flex items-center gap-2",onClick:x,variant:"outline",children:[(0,t.jsx)(a.A,{className:"size-4"}),"Reset All to Defaults"]})})]})]})]}),(0,t.jsxs)("section",{children:[(0,t.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Help & Information"}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"About Settings"}),(0,t.jsx)(m.BT,{children:"Information about how settings work in WorkHub"})]}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-2 font-medium",children:"Font Size"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Font size settings apply globally across the entire application. Changes are saved automatically and will persist across browser sessions."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-2 font-medium",children:"Accessibility"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use larger font sizes for better readability. All font size changes maintain proper contrast ratios and accessibility standards."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-2 font-medium",children:"Performance"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Settings are stored locally in your browser and synchronized across tabs. No server requests are made when changing preferences."})]})]})]})]})]})]})}},73158:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(40157).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},73350:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(40157).A)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]])},74466:(e,r,s)=>{"use strict";s.d(r,{F:()=>i});var t=s(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=t.$,i=(e,r)=>s=>{var t;if((null==r?void 0:r.variants)==null)return n(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:i,defaultVariants:l}=r,c=Object.keys(i).map(e=>{let r=null==s?void 0:s[e],t=null==l?void 0:l[e];if(null===r)return null;let n=a(r)||a(t);return i[e][n]}),d=s&&Object.entries(s).reduce((e,r)=>{let[s,t]=r;return void 0===t||(e[s]=t),e},{});return n(e,c,null==r||null==(t=r.compoundVariants)?void 0:t.reduce((e,r)=>{let{class:s,className:t,...a}=r;return Object.entries(a).every(e=>{let[r,s]=e;return Array.isArray(s)?s.includes({...l,...d}[r]):({...l,...d})[r]===s})?[...e,s,t]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},82733:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(40157).A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},89440:(e,r,s)=>{"use strict";s.d(r,{AppBreadcrumb:()=>o});var t=s(95155),a=s(6874),n=s.n(a),i=s(35695),l=s(12115),c=s(19018),d=s(54036);function o(e){let{className:r,homeHref:s="/",homeLabel:a="Dashboard",showContainer:o=!0}=e,m=(0,i.usePathname)(),u=m?m.split("/").filter(Boolean):[],x=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},f=u.map((e,r)=>{let s="/"+u.slice(0,r+1).join("/"),a=r===u.length-1,i=x(e);return(0,t.jsxs)(l.Fragment,{children:[(0,t.jsx)(c.BreadcrumbItem,{children:a?(0,t.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:i}):(0,t.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:i})})}),!a&&(0,t.jsx)(c.BreadcrumbSeparator,{})]},s)}),p=(0,t.jsx)(c.Breadcrumb,{className:(0,d.cn)("text-sm",r),children:(0,t.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,t.jsx)(c.BreadcrumbItem,{children:(0,t.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:a})})}),u.length>0&&(0,t.jsx)(c.BreadcrumbSeparator,{}),f]})});return o?(0,t.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"flex items-center",children:p})}):p}},95206:(e,r,s)=>{Promise.resolve().then(s.bind(s,72608))}},e=>{var r=r=>e(e.s=r);e.O(0,[6476,6874,87,4036,1978,8441,1684,7358],()=>r(95206)),_N_E=e.O()}]);