/**
 * @file Centralized exports for the API service layer.
 * @module api
 */

// Core infrastructure
export * from './core/apiClient';
export * from './core/baseApiService';
export * from './core/errors';
export * from './core/types';
export * from './core/interfaces';

// Create and export a configured API client instance
import { ApiClient } from './core/apiClient';
import { getEnvironmentConfig } from '../config/environment';

/**
 * Secure Authentication Token Provider
 * Single source of truth for authentication tokens across the entire application
 * Consistent naming convention: "Secure" prefix for all authentication functions
 */
let secureAuthTokenProvider: (() => string | null) | null = null;

/**
 * Set the secure authentication token provider
 * This should ONLY be called by the AuthContext
 * Replaces legacy setGlobalAuthTokenProvider and setFactoryAuthTokenProvider
 */
export function setSecureAuthTokenProvider(
  provider: () => string | null
): void {
  secureAuthTokenProvider = provider;

  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Secure Auth Token Provider initialized');
  }
}

/**
 * Get the current authentication token from the secure provider
 * This is used by ALL API clients throughout the application
 */
function getSecureAuthToken(): string | null {
  if (!secureAuthTokenProvider) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Secure Auth Token Provider not initialized');
    }
    return null;
  }

  try {
    return secureAuthTokenProvider();
  } catch (error) {
    console.error('❌ Error getting auth token from secure provider:', error);
    return null;
  }
}

/**
 * Get the secure token provider function (for debugging/testing)
 */
export function getSecureAuthTokenProvider(): (() => string | null) | null {
  return secureAuthTokenProvider;
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use setSecureAuthTokenProvider instead
 */
export function setGlobalAuthTokenProvider(
  provider: () => string | null
): void {
  console.warn(
    '⚠️ setGlobalAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider instead.'
  );
  setSecureAuthTokenProvider(provider);
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use setSecureAuthTokenProvider instead
 */
export function setUnifiedAuthTokenProvider(
  provider: () => string | null
): void {
  console.warn(
    '⚠️ setUnifiedAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider instead.'
  );
  setSecureAuthTokenProvider(provider);
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use getSecureAuthTokenProvider instead
 */
export function getGlobalAuthTokenProvider(): (() => string | null) | null {
  console.warn(
    '⚠️ getGlobalAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead.'
  );
  return getSecureAuthTokenProvider();
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use getSecureAuthTokenProvider instead
 */
export function getUnifiedAuthTokenProvider(): (() => string | null) | null {
  console.warn(
    '⚠️ getUnifiedAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead.'
  );
  return getSecureAuthTokenProvider();
}

// Get environment-aware configuration
const envConfig = getEnvironmentConfig();

export const apiClient = new ApiClient({
  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration
  getAuthToken: getSecureAuthToken, // Use consistent secure naming
  headers: {
    'Content-Type': 'application/json',
  },
  retryAttempts: 3,
  timeout: 10_000,
});

// Security architecture (selective exports to avoid conflicts)
export {
  // Security hooks
  useSecureApiClient,
  useSecureApiReplacement,
  useSecureHttpClient,
  useCSRFProtection,
  useInputValidation,
  useSecurityMonitoring,
  useSessionSecurity,
  useTokenManagement,
  // Security providers
  SecurityConfigProvider,
  useSecurityConfig,
  useSecurityConfigValue,
  // Security composer
  SecurityComposer,
  createSecurityComposer,
  // Secure API client
  SecureApiClient,
  createSecureApiClient,
} from './security';

export type {
  // Security types (avoid RequestConfig conflict)
  UseSecureApiClientReturn,
  SecureApiRequestConfig,
  UseSecureHttpClientReturn,
  SecurityFeatures,
  SecureApiClientConfig,
} from './security';

// Domain-specific API services
export * from './services/domain/delegationApi';
export * from './services/domain/employeeApi';
export * from './services/domain/taskApi';
export * from './services/domain/vehicleApi';

// External API services
export * from './services/external/flightApi';
export * from './services/external/flightDetailsApi';
