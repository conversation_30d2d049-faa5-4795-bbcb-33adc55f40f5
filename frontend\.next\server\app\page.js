(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={2775:(e,t,s)=>{"use strict";s.d(t,{Ln:()=>y,WV:()=>h,fs:()=>p,kI:()=>v,xH:()=>m,xT:()=>f});var i=s(8693),r=s(54050),a=s(77312),n=s(46349),l=s(8342);let o={fromApi(e){let t={cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId};return e.vehicleMake||e.vehicleModel||e.vehicleYear?{...t,vehicleMake:e.vehicleMake||"Unknown",vehicleModel:e.vehicleModel||"Unknown",vehicleYear:e.vehicleYear||new Date().getFullYear(),licensePlate:e.licensePlate||null,employeeName:e.employeeName||null}:t},toApi:e=>e};class d extends a.v{constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/servicerecords",this.transformer=o}async getById(e){return this.executeWithInfrastructure(`${this.endpoint}:${e}`,async()=>{let t=await this.apiClient.get(`${this.endpoint}/${e}`);return this.transformer.fromApi?this.transformer.fromApi(t):t})}async updateRecord(e,t,s){return this.executeWithInfrastructure(null,async()=>{let{vehicleId:i,...r}=s,a=await this.apiClient.put(`/vehicles/${t}/servicerecords/${e}`,r);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:${e}`)),this.cache.invalidatePattern(RegExp(`^vehicles:${t}:`)),this.transformer.fromApi?this.transformer.fromApi(a):a})}async deleteRecord(e,t){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete(`/vehicles/${t}/servicerecords/${e}`),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:${e}`)),this.cache.invalidatePattern(RegExp(`^vehicles:${t}:`))})}async getVehicleServiceRecords(e){return this.executeWithInfrastructure(`vehicles:${e}:servicerecords`,async()=>await this.apiClient.get(`/vehicles/${e}/servicerecords`))}async createVehicleServiceRecord(e,t){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post(`/vehicles/${e}/servicerecords`,t);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidatePattern(RegExp(`^vehicles:${e}:`)),this.transformer.fromApi?this.transformer.fromApi(s):s})}async getAllEnriched(){return this.executeWithInfrastructure(`${this.endpoint}:enriched`,async()=>(await this.apiClient.get(`${this.endpoint}/enriched`)).map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e))}}let c=new d(l.uE),u="serviceRecords",h=(e,t)=>(0,n.GK)([u,e],()=>c.getById(e),"serviceRecord",{enabled:t?.enabled??!!e,staleTime:3e5}),p=e=>(0,n.GK)([u,"allEnriched"],()=>c.getAllEnriched(),"serviceRecord",{enabled:e?.enabled??!0,staleTime:3e5}),m=(e,t)=>(0,n.GK)([u,"forVehicle",e],()=>c.getVehicleServiceRecords(e),"serviceRecord",{enabled:t?.enabled??!0,staleTime:3e5}),v=()=>{let e=(0,i.jE)();return(0,r.n)({mutationFn:async e=>{let{vehicleId:t}=e;return c.createVehicleServiceRecord(t,e)},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,"forVehicle",s.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",s.vehicleId]})}})},y=()=>{let e=(0,i.jE)();return(0,r.n)({mutationFn:async({id:e,vehicleId:t,data:s})=>c.updateRecord(e,t,s),onSuccess:(t,s)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,s.id]}),e.invalidateQueries({queryKey:[u,"forVehicle",s.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",s.vehicleId]})}})},f=()=>{let e=(0,i.jE)();return(0,r.n)({mutationFn:async({id:e,vehicleId:t})=>c.deleteRecord(e,t),onSuccess:(t,s)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,s.id]}),e.invalidateQueries({queryKey:[u,"forVehicle"]}),e.invalidateQueries({queryKey:["vehicle"]})}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},23562:(e,t,s)=>{"use strict";s.d(t,{k:()=>l});var i=s(60687),r=s(25177),a=s(43210),n=s(22482);let l=a.forwardRef(({className:e,value:t,...s},a)=>(0,i.jsx)(r.bL,{className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),ref:a,...s,children:(0,i.jsx)(r.C1,{className:"size-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));l.displayName=r.bL.displayName},25177:(e,t,s)=>{"use strict";s.d(t,{C1:()=>j,bL:()=>b});var i=s(43210),r=s(11273),a=s(14163),n=s(60687),l="Progress",[o,d]=(0,r.A)(l),[c,u]=o(l),h=i.forwardRef((e,t)=>{var s,i;let{__scopeProgress:r,value:l=null,max:o,getValueLabel:d=v,...u}=e;(o||0===o)&&!x(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let h=x(o)?o:100;null===l||g(l,h)||console.error((i=`${l}`,`Invalid prop \`value\` of value \`${i}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=g(l,h)?l:null,m=f(p)?d(p,h):void 0;return(0,n.jsx)(c,{scope:r,value:p,max:h,children:(0,n.jsx)(a.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":f(p)?p:void 0,"aria-valuetext":m,role:"progressbar","data-state":y(p,h),"data-value":p??void 0,"data-max":h,...u,ref:t})})});h.displayName=l;var p="ProgressIndicator",m=i.forwardRef((e,t)=>{let{__scopeProgress:s,...i}=e,r=u(p,s);return(0,n.jsx)(a.sG.div,{"data-state":y(r.value,r.max),"data-value":r.value??void 0,"data-max":r.max,...i,ref:t})});function v(e,t){return`${Math.round(e/t*100)}%`}function y(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function f(e){return"number"==typeof e}function x(e){return f(e)&&!isNaN(e)&&e>0}function g(e,t){return f(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=p;var b=h,j=m},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},54050:(e,t,s)=>{"use strict";s.d(t,{n:()=>c});var i=s(43210),r=s(65406),a=s(33465),n=s(35536),l=s(31212),o=class extends n.Q{#e;#t=void 0;#s;#i;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#r()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#r(),this.#a(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#r(),this.#a()}mutate(e,t){return this.#i=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#r(){let e=this.#s?.state??(0,r.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){a.jG.batch(()=>{if(this.#i&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#i.onSuccess?.(e.data,t,s),this.#i.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#i.onError?.(e.error,t,s),this.#i.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},d=s(8693);function c(e,t){let s=(0,d.jE)(t),[r]=i.useState(()=>new o(s,e));i.useEffect(()=>{r.setOptions(e)},[r,e]);let n=i.useSyncExternalStore(i.useCallback(e=>r.subscribe(a.jG.batchCalls(e)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),c=i.useCallback((e,t)=>{r.mutate(e,t).catch(l.lQ)},[r]);if(n.error&&(0,l.GU)(r.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:c,mutateAsync:n.mutate}}},54052:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(82614).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57047:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Q});var i=s(60687),r=s(8751),a=s(81950),n=s(54052),l=s(26622),o=s(27629),d=s(3662),c=s(24920),u=s(27805),h=s(15036),p=s(14975),m=s(76311),v=s(29333),y=s(48206),f=s(85814),x=s.n(f),g=s(43210),b=s(96834),j=s(29523),k=s(44493),E=s(23562),w=s(63213),N=s(63502),A=s(19599),q=s(2775),R=s(73227),I=s(72273),S=s(22482);let P=({title:e,value:t,description:s,icon:n,trend:l,href:o})=>{let d=o?x():"div";return(0,i.jsx)(d,{href:o||"#",className:o?"block":"",children:(0,i.jsx)(k.Zp,{className:(0,S.cn)("transition-all duration-200",o&&"hover:shadow-md cursor-pointer"),children:(0,i.jsx)(k.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e}),(0,i.jsx)("p",{className:"text-3xl font-bold",children:t}),s&&(0,i.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:s})]}),(0,i.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,i.jsx)("div",{className:"rounded-md bg-primary/10 p-2",children:(0,i.jsx)(n,{className:"size-4 text-primary"})}),l&&(0,i.jsxs)("div",{className:(0,S.cn)("flex items-center text-xs",l.isPositive?"text-green-600":"text-red-600"),children:[l.isPositive?(0,i.jsx)(r.A,{className:"size-3 mr-1"}):(0,i.jsx)(a.A,{className:"size-3 mr-1"}),Math.abs(l.value),"%"]})]})]})})})})},M=({title:e,description:t,icon:s,href:r,variant:a="outline"})=>(0,i.jsx)(k.Zp,{className:"hover:shadow-md transition-all duration-200",children:(0,i.jsx)(k.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-start gap-3",children:[(0,i.jsx)("div",{className:"rounded-md bg-primary/10 p-2",children:(0,i.jsx)(s,{className:"size-4 text-primary"})}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsx)("h4",{className:"text-sm font-medium",children:e}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:t}),(0,i.jsx)(j.$,{asChild:!0,variant:a,size:"sm",className:"mt-3 w-full",children:(0,i.jsx)(x(),{href:r,children:"Get Started"})})]})]})})}),$=()=>{let{user:e,isInitialized:t,loading:s}=(0,w.useAuthContext)(),r=(0,g.useMemo)(()=>t&&!s&&!!e,[t,s,e?.id]),{data:a=[]}=(0,I.T$)({enabled:r}),{data:f=[]}=(0,N.BD)({enabled:r}),{data:S=[]}=(0,R.si)({enabled:r}),{data:$=[]}=(0,A.nR)({enabled:r}),{data:Q=[]}=(0,q.fs)({enabled:r});if(!t||s)return(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,i.jsx)("p",{className:"mt-4 text-sm text-muted-foreground",children:"Initializing..."})]})});if(!e)return(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsxs)(k.Zp,{className:"w-full max-w-md",children:[(0,i.jsxs)(k.aR,{className:"text-center",children:[(0,i.jsx)(k.ZB,{children:"Welcome to WorkHub"}),(0,i.jsx)(k.BT,{children:"Please sign in to access your dashboard"})]}),(0,i.jsx)(k.Wu,{children:(0,i.jsx)(j.$,{asChild:!0,className:"w-full",children:(0,i.jsx)(x(),{href:"/login",children:"Sign In"})})})]})});let C={totalVehicles:a.length,activeDelegations:f.filter(e=>"In_Progress"===e.status).length,pendingTasks:S.filter(e=>"Assigned"===e.status||"In_Progress"===e.status).length,maintenancesDue:Q.filter(e=>Math.random()>.8).length,teamMembers:$.length},T=[{title:"Add New Vehicle",description:"Register a new asset to your fleet",icon:n.A,href:"/add-vehicle",variant:"default"},{title:"Schedule Maintenance",description:"Plan upcoming service appointments",icon:l.A,href:"/service-records"},{title:"View Analytics",description:"See detailed reports and insights",icon:o.A,href:"/reports"},{title:"Assign Task",description:"Create and delegate new tasks",icon:d.A,href:"/tasks"}];return(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Welcome back",e?.user_metadata?.full_name?`, ${e.user_metadata.full_name}`:"","!"]}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Here's what's happening with your operations today."})]}),(0,i.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,i.jsx)(P,{title:"Total Vehicles",value:C.totalVehicles,description:"Active fleet assets",icon:c.A,href:"/vehicles",trend:{value:5.2,isPositive:!0}}),(0,i.jsx)(P,{title:"Active Projects",value:C.activeDelegations,description:"In progress delegations",icon:u.A,href:"/delegations"}),(0,i.jsx)(P,{title:"Pending Tasks",value:C.pendingTasks,description:"Awaiting completion",icon:h.A,href:"/tasks",trend:{value:2.1,isPositive:!1}}),(0,i.jsx)(P,{title:"Maintenance Due",value:C.maintenancesDue,description:"Requires attention",icon:p.A,href:"/service-history"})]}),(0,i.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,i.jsx)("div",{className:"lg:col-span-2",children:(0,i.jsxs)(k.Zp,{children:[(0,i.jsxs)(k.aR,{className:"flex flex-row items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(k.ZB,{children:"Recent Activity"}),(0,i.jsx)(k.BT,{children:"Latest updates from your operations"})]}),(0,i.jsx)(j.$,{variant:"outline",size:"sm",asChild:!0,children:(0,i.jsxs)(x(),{href:"/activity",children:[(0,i.jsx)(m.A,{className:"size-4 mr-2"}),"View All"]})})]}),(0,i.jsx)(k.Wu,{children:(0,i.jsx)("div",{className:"space-y-4",children:[{id:"1",title:"Vehicle VIN-123 maintenance completed",description:"Oil change and tire rotation finished",timestamp:"2 hours ago",type:"maintenance"},{id:"2",title:"New task assigned: Fleet inspection",description:"Quarterly safety inspection due next week",timestamp:"4 hours ago",type:"task",priority:"high"},{id:"3",title:"Project Alpha milestone completed",description:"Phase 2 deliverables submitted",timestamp:"1 day ago",type:"delegation"}].map(e=>(0,i.jsxs)("div",{className:"flex items-start gap-3 p-3 rounded-lg bg-muted/30",children:[(0,i.jsxs)("div",{className:"rounded-full bg-primary/10 p-1",children:["maintenance"===e.type&&(0,i.jsx)(v.A,{className:"size-3 text-primary"}),"task"===e.type&&(0,i.jsx)(d.A,{className:"size-3 text-primary"}),"delegation"===e.type&&(0,i.jsx)(y.A,{className:"size-3 text-primary"}),"vehicle"===e.type&&(0,i.jsx)(c.A,{className:"size-3 text-primary"})]}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("p",{className:"text-sm font-medium",children:e.title}),e.priority&&(0,i.jsx)(b.E,{variant:"high"===e.priority?"destructive":"secondary",className:"text-xs",children:e.priority})]}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.timestamp})]})]},e.id))})})]})}),(0,i.jsx)("div",{children:(0,i.jsxs)(k.Zp,{children:[(0,i.jsxs)(k.aR,{children:[(0,i.jsx)(k.ZB,{children:"Quick Actions"}),(0,i.jsx)(k.BT,{children:"Common tasks and shortcuts"})]}),(0,i.jsx)(k.Wu,{children:(0,i.jsx)("div",{className:"space-y-3",children:T.map((e,t)=>(0,i.jsx)(M,{...e},t))})})]})})]}),(0,i.jsxs)(k.Zp,{children:[(0,i.jsxs)(k.aR,{children:[(0,i.jsx)(k.ZB,{children:"Fleet Performance"}),(0,i.jsx)(k.BT,{children:"Overview of fleet efficiency and maintenance status"})]}),(0,i.jsx)(k.Wu,{children:(0,i.jsxs)("div",{className:"grid gap-6 md:grid-cols-3",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Fleet Utilization"}),(0,i.jsx)("span",{className:"font-medium",children:"87%"})]}),(0,i.jsx)(E.k,{value:87,className:"h-2"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Maintenance Up-to-date"}),(0,i.jsx)("span",{className:"font-medium",children:"92%"})]}),(0,i.jsx)(E.k,{value:92,className:"h-2"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Task Completion Rate"}),(0,i.jsx)("span",{className:"font-medium",children:"78%"})]}),(0,i.jsx)(E.k,{value:78,className:"h-2"})]})]})})]})]})};function Q(){return(0,i.jsx)($,{})}"undefined"!=typeof document&&(document.title="Dashboard - WorkHub")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72273:(e,t,s)=>{"use strict";s.d(t,{NS:()=>m,T$:()=>c,W_:()=>u,Y1:()=>h,lR:()=>p});var i=s(8693),r=s(54050),a=s(46349),n=s(87676),l=s(48839),o=s(49603);let d={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,a.GK)([...d.all],async()=>(await o.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>(0,a.GK)([...d.detail(e)],()=>o.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(t?.enabled??!0),staleTime:3e5,...t}),h=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,n.useNotifications)();return(0,r.n)({mutationFn:e=>{let t=l.M.toCreateRequest(e);return o.vehicleApiService.create(t)},onError:e=>{t(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),s(`Vehicle "${t.licensePlate}" has been created successfully!`)}})},p=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,n.useNotifications)();return(0,r.n)({mutationFn:({data:e,id:t})=>{let s=l.M.toUpdateRequest(e);return o.vehicleApiService.update(t,s)},onError:e=>{t(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:d.all}),e.invalidateQueries({queryKey:d.detail(t.id)}),s(`Vehicle "${t.licensePlate}" has been updated successfully!`)}})},m=()=>{let e=(0,i.jE)(),{showError:t,showSuccess:s}=(0,n.useNotifications)();return(0,r.n)({mutationFn:e=>o.vehicleApiService.delete(e),onError:e=>{t(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(t,i)=>{e.invalidateQueries({queryKey:d.all}),e.removeQueries({queryKey:d.detail(i)}),s("Vehicle has been deleted successfully!")}})}},73227:(e,t,s)=>{"use strict";s.d(t,{ZY:()=>b,AK:()=>k,b7:()=>x,xo:()=>g,si:()=>f,K:()=>j});var i=s(93425),r=s(8693),a=s(54050),n=s(43210),l=s(46349),o=s(49603),d=s(83144),c=s(57930);let u={all:["tasks"],detail:e=>["tasks",e]},h=e=>({enabled:!!e,queryFn:()=>o.taskApiService.getById(e),queryKey:u.detail(e),staleTime:3e5}),p=()=>({queryFn:()=>o.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),m=()=>({queryFn:()=>o.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),v=e=>[h(e),p(),m()];var y=s(38118);let f=e=>(0,l.GK)([...u.all],async()=>(await o.taskApiService.getAll()).data,"task",{staleTime:0,...e}),x=e=>(0,l.GK)([...u.detail(e)],async()=>await o.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),g=e=>{let[t,s,r]=(0,i.E)({queries:v(e)}),a=(0,n.useMemo)(()=>{if(t?.data&&s?.data&&r?.data)try{let e=c.J.fromApi(t.data),i=Array.isArray(s.data)?s.data:[],a=Array.isArray(r.data)?r.data:[];return(0,d.R)(e,i,a)}catch(e){throw console.error("Error enriching task data:",e),e}},[t?.data,s?.data,r?.data]),l=(0,n.useCallback)(()=>{t?.refetch(),s?.refetch(),r?.refetch()},[t?.refetch,s?.refetch,r?.refetch]);return{data:a,error:t?.error||s?.error||r?.error,isError:t?.isError||s?.isError||r?.isError,isLoading:t?.isLoading||s?.isLoading||r?.isLoading,isPending:t?.isPending||s?.isPending||r?.isPending,refetch:l}},b=()=>{let e=(0,r.jE)();return(0,a.n)({mutationFn:async e=>{let t=c.J.toCreateRequest(e);return await o.taskApiService.create(t)},onError:(t,s,i)=>{i?.previousTasks&&e.setQueryData(u.all,i.previousTasks),console.error("Failed to create task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:u.all});let s=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>{let s="optimistic-"+Date.now().toString(),i=new Date().toISOString();return[...e,{createdAt:i,dateTime:t.dateTime??null,deadline:t.deadline??null,description:t.description,driverEmployee:null,driverEmployeeId:t.driverEmployeeId??null,estimatedDuration:t.estimatedDuration??null,id:s,location:t.location??null,notes:t.notes??null,priority:t.priority,requiredSkills:t.requiredSkills??null,staffEmployee:null,staffEmployeeId:t.staffEmployeeId??null,status:t.status||"Pending",subtasks:t.subtasks?.map(e=>({completed:e.completed||!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:s,title:e.title}))||[],updatedAt:i,vehicle:null,vehicleId:t.vehicleId??null}]}),{previousTasks:s}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})},j=()=>{let e=(0,r.jE)();return(0,a.n)({mutationFn:async({data:e,id:t})=>{let s=c.J.toUpdateRequest(e);return await o.taskApiService.update(t,s)},onError:(t,s,i)=>{i?.previousTask&&e.setQueryData(u.detail(s.id),i.previousTask),i?.previousTasksList&&e.setQueryData(u.all,i.previousTasksList),console.error("Failed to update task:",t)},onMutate:async({data:t,id:s})=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(s)});let i=e.getQueryData(u.detail(s)),r=e.getQueryData(u.all);return e.setQueryData(u.detail(s),e=>{if(!e)return e;let i=new Date().toISOString();return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,y.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,y.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,y.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:s,title:e.title}))||e.subtasks||[],updatedAt:i,vehicleId:(0,y.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}),e.setQueryData(u.all,(e=[])=>e.map(e=>{if(e.id===s){let i=new Date().toISOString(),r=t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:s,title:e.title}))||e.subtasks||[];return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,y.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,y.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,y.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:r,updatedAt:i,vehicleId:(0,y.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}return e})),{previousTask:i,previousTasksList:r}},onSettled:(t,s,i)=>{e.invalidateQueries({queryKey:u.detail(i.id)}),e.invalidateQueries({queryKey:u.all})}})},k=()=>{let e=(0,r.jE)();return(0,a.n)({mutationFn:async e=>(await o.taskApiService.delete(e),e),onError:(t,s,i)=>{i?.previousTasksList&&e.setQueryData(u.all,i.previousTasksList),console.error("Failed to delete task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(t)});let s=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>e.filter(e=>e.id!==t)),e.removeQueries({queryKey:u.detail(t)}),{previousTasksList:s}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81432:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var i=s(65239),r=s(48088),a=s(88170),n=s.n(a),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21204)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\WorkHub\\frontend\\src\\app\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81630:e=>{"use strict";e.exports=require("http")},81950:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(82614).A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},83144:(e,t,s)=>{"use strict";s.d(t,{R:()=>r});class i{static enrich(e,t,s){let{employeeMap:i,vehicleMap:r}=this.createLookupMaps(t,s),a=this.enrichStaffEmployee(e,i);return a=this.enrichDriverEmployee(a,i),a=this.enrichVehicle(a,r)}static createLookupMaps(e,t){let s=Array.isArray(e)?e:[],i=Array.isArray(t)?t:[];return{employeeMap:new Map(s.map(e=>[e.id,e])),vehicleMap:new Map(i.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,t){if(!e.driverEmployeeId)return e;let s=e.driverEmployee??t.get(e.driverEmployeeId)??null;return{...e,driverEmployee:s}}static enrichStaffEmployee(e,t){if(!e.staffEmployeeId)return e;let s=e.staffEmployee??t.get(e.staffEmployeeId)??null;return{...e,staffEmployee:s}}static enrichVehicle(e,t){if(!e.vehicleId)return e;let s=e.vehicle??t.get(e.vehicleId)??null;return{...e,vehicle:s}}}let r=(e,t,s)=>i.enrich(e,t,s)},83997:e=>{"use strict";e.exports=require("tty")},89967:(e,t,s)=>{Promise.resolve().then(s.bind(s,21204))},91645:e=>{"use strict";e.exports=require("net")},93425:(e,t,s)=>{"use strict";s.d(t,{E:()=>v});var i=s(43210),r=s(33465),a=s(5563),n=s(35536),l=s(31212);function o(e,t){let s=new Set(t);return e.filter(e=>!s.has(e))}var d=class extends n.Q{#e;#n;#l;#o;#d;#c;#u;#h;#p=[];constructor(e,t,s){super(),this.#e=e,this.#o=s,this.#l=[],this.#d=[],this.#n=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#d.forEach(e=>{e.subscribe(t=>{this.#m(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#d.forEach(e=>{e.destroy()})}setQueries(e,t){this.#l=e,this.#o=t,r.jG.batch(()=>{let e=this.#d,t=this.#v(this.#l);this.#p=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let s=t.map(e=>e.observer),i=s.map(e=>e.getCurrentResult()),r=s.some((t,s)=>t!==e[s]);(e.length!==s.length||r)&&(this.#d=s,this.#n=i,this.hasListeners()&&(o(e,s).forEach(e=>{e.destroy()}),o(s,e).forEach(e=>{e.subscribe(t=>{this.#m(e,t)})}),this.#a()))})}getCurrentResult(){return this.#n}getQueries(){return this.#d.map(e=>e.getCurrentQuery())}getObservers(){return this.#d}getOptimisticResult(e,t){let s=this.#v(e),i=s.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[i,e=>this.#y(e??i,t),()=>this.#f(i,s)]}#f(e,t){return t.map((s,i)=>{let r=e[i];return s.defaultedQueryOptions.notifyOnChangeProps?r:s.observer.trackResult(r,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#y(e,t){return t?(this.#c&&this.#n===this.#h&&t===this.#u||(this.#u=t,this.#h=this.#n,this.#c=(0,l.BH)(this.#c,t(e))),this.#c):e}#v(e){let t=new Map(this.#d.map(e=>[e.options.queryHash,e])),s=[];return e.forEach(e=>{let i=this.#e.defaultQueryOptions(e),r=t.get(i.queryHash);r?s.push({defaultedQueryOptions:i,observer:r}):s.push({defaultedQueryOptions:i,observer:new a.$(this.#e,i)})}),s}#m(e,t){let s=this.#d.indexOf(e);-1!==s&&(this.#n=function(e,t,s){let i=e.slice(0);return i[t]=s,i}(this.#n,s,t),this.#a())}#a(){if(this.hasListeners()){let e=this.#c,t=this.#f(this.#n,this.#p);e!==this.#y(t,this.#o?.combine)&&r.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#n)})})}}},c=s(8693),u=s(24903),h=s(18228),p=s(16142),m=s(76935);function v({queries:e,...t},s){let n=(0,c.jE)(s),o=(0,u.w)(),v=(0,h.h)(),y=i.useMemo(()=>e.map(e=>{let t=n.defaultQueryOptions(e);return t._optimisticResults=o?"isRestoring":"optimistic",t}),[e,n,o]);y.forEach(e=>{(0,m.jv)(e),(0,p.LJ)(e,v)}),(0,p.wZ)(v);let[f]=i.useState(()=>new d(n,y,t)),[x,g,b]=f.getOptimisticResult(y,t.combine),j=!o&&!1!==t.subscribed;i.useSyncExternalStore(i.useCallback(e=>j?f.subscribe(r.jG.batchCalls(e)):l.lQ,[f,j]),()=>f.getCurrentResult(),()=>f.getCurrentResult()),i.useEffect(()=>{f.setQueries(y,t)},[y,t,f]);let k=x.some((e,t)=>(0,m.EU)(y[t],e))?x.flatMap((e,t)=>{let s=y[t];if(s){let t=new a.$(n,s);if((0,m.EU)(s,e))return(0,m.iL)(s,t,v);(0,m.nE)(e,o)&&(0,m.iL)(s,t,v)}return[]}):[];if(k.length>0)throw Promise.all(k);let E=x.find((e,t)=>{let s=y[t];return s&&(0,p.$1)({result:e,errorResetBoundary:v,throwOnError:s.throwOnError,query:n.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(E?.error)throw E.error;return g(b())}},94735:e=>{"use strict";e.exports=require("events")},98215:(e,t,s)=>{Promise.resolve().then(s.bind(s,57047))}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[4447,211,1658,8390,101,5825,9599,3502],()=>s(81432));module.exports=i})();