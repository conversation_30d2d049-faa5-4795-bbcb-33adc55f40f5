(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3015],{6560:(e,a,t)=>{"use strict";t.d(a,{r:()=>c});var s=t(95155),r=t(50172),i=t(12115),l=t(30285),n=t(54036);let c=i.forwardRef((e,a)=>{let{actionType:t="primary",asChild:i=!1,children:c,className:d,disabled:o,icon:m,isLoading:x=!1,loadingText:u,...h}=e,{className:p,variant:f}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[t];return(0,s.jsx)(l.$,{asChild:i,className:(0,n.cn)(p,d),disabled:x||o,ref:a,variant:f,...h,children:x?(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,s.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}),u||c]}):(0,s.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,s.jsx)("span",{className:"mr-2",children:m}),c]})})});c.displayName="ActionButton"},20957:(e,a,t)=>{Promise.resolve().then(t.bind(t,97261))},33271:(e,a,t)=>{"use strict";t.d(a,{k:()=>p});var s=t(95155),r=t(18018),i=t(50172),l=t(68718),n=t(15300),c=t(60679),d=t(12115),o=t(6560),m=t(44838),x=t(53712),u=t(54036),h=t(16146);function p(e){let{className:a,csvData:t,enableCsv:p=!1,entityId:f,fileName:g,reportContentId:v,reportType:j,tableId:N}=e,[y,b]=(0,d.useState)(!1),[w,C]=(0,d.useState)(!1),{showFormSuccess:k,showFormError:A}=(0,x.t6)(),S=async()=>{b(!0);try{let e="/api/reports/".concat(j).concat(f?"/".concat(f):""),a=document.createElement("a");a.href=e,a.download="".concat(g,".pdf"),a.target="_blank",document.body.append(a),a.click(),a.remove(),k({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),A("PDF download failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{b(!1)}},E=async()=>{if(p){C(!0);try{if((null==t?void 0:t.data)&&t.headers)(0,h.og)(t.data,t.headers,"".concat(g,".csv"));else if(N){let e=(0,h.tL)(N);(0,h.og)(e.data,e.headers,"".concat(g,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");k({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),A("CSV generation failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{C(!1)}}},z=y||w;return(0,s.jsxs)("div",{className:(0,u.cn)("flex items-center gap-2 no-print",a),children:[(0,s.jsx)(o.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,s.jsx)(r.A,{className:"size-4"})}),(0,s.jsxs)(m.rI,{children:[(0,s.jsx)(m.ty,{asChild:!0,children:(0,s.jsx)(o.r,{actionType:"secondary","aria-label":"Download report",disabled:z,size:"icon",title:"Download Report",children:z?(0,s.jsx)(i.A,{className:"size-4 animate-spin"}):(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsxs)(m.SQ,{align:"end",children:[(0,s.jsxs)(m._2,{disabled:y,onClick:S,children:[y?(0,s.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(n.A,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download PDF"})]}),p&&(0,s.jsxs)(m._2,{disabled:w,onClick:E,children:[w?(0,s.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(c.A,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download CSV"})]})]})]})]})}},42366:(e,a,t)=>{"use strict";t.r(a),t.d(a,{useNotifications:()=>i,useWorkHubNotifications:()=>l});var s=t(12115),r=t(96016);let i=()=>{let e=(0,r.C)(e=>e.addNotification),a=(0,r.C)(e=>e.removeNotification),t=(0,r.C)(e=>e.clearAllNotifications),i=(0,r.C)(e=>e.unreadNotificationCount),l=(0,s.useCallback)(a=>{e({message:a,type:"success"})},[e]),n=(0,s.useCallback)(a=>{e({message:a,type:"error"})},[e]),c=(0,s.useCallback)(a=>{e({message:a,type:"warning"})},[e]),d=(0,s.useCallback)(a=>{e({message:a,type:"info"})},[e]),o=(0,s.useCallback)((e,a,t)=>{e?l(a):n(t)},[l,n]),m=(0,s.useCallback)(function(t,s){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:s,type:t}),setTimeout(()=>{let e=r.C.getState().notifications.at(-1);e&&e.message===s&&a(e.id)},i)},[e,a]),x=(0,s.useCallback)(function(){var a;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:t,type:"info"}),null==(a=r.C.getState().notifications.at(-1))?void 0:a.id},[e]),u=(0,s.useCallback)((e,t,s)=>{a(e),t?l(s):n(s)},[a,l,n]);return{clearAllNotifications:t,removeNotification:a,showApiResult:o,showError:n,showInfo:d,showLoading:x,showSuccess:l,showTemporary:m,showWarning:c,unreadCount:i,updateLoadingNotification:u}},l=()=>{let{clearAllNotifications:e,removeNotification:a,showError:t,showInfo:l,showSuccess:n,showWarning:c,unreadCount:d}=i(),o=(0,s.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"delegation",message:e,type:"delegation-update"})},[]),m=(0,s.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),x=(0,s.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:a,showDelegationUpdate:o,showEmployeeUpdate:(0,s.useCallback)((e,a)=>{(0,r.C.getState().addNotification)({...a&&{actionUrl:a},category:"employee",message:e,type:"employee-update"})},[]),showError:t,showInfo:l,showSuccess:n,showTaskAssigned:x,showVehicleMaintenance:m,showWarning:c,unreadCount:d}}},44838:(e,a,t)=>{"use strict";t.d(a,{SQ:()=>x,_2:()=>u,hO:()=>h,lp:()=>p,mB:()=>f,rI:()=>o,ty:()=>m});var s=t(95155),r=t(12115),i=t(48698),l=t(73158),n=t(10518),c=t(70154),d=t(54036);let o=i.bL,m=i.l9;i.YJ,i.ZL,i.Pb,i.z6,r.forwardRef((e,a)=>{let{className:t,inset:r,children:n,...c}=e;return(0,s.jsxs)(i.ZP,{ref:a,className:(0,d.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...c,children:[n,(0,s.jsx)(l.A,{className:"ml-auto"})]})}).displayName=i.ZP.displayName,r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(i.G5,{ref:a,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...r})}).displayName=i.G5.displayName;let x=r.forwardRef((e,a)=>{let{className:t,sideOffset:r=4,...l}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsx)(i.UC,{ref:a,sideOffset:r,className:(0,d.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...l})})});x.displayName=i.UC.displayName;let u=r.forwardRef((e,a)=>{let{className:t,inset:r,...l}=e;return(0,s.jsx)(i.q7,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...l})});u.displayName=i.q7.displayName;let h=r.forwardRef((e,a)=>{let{className:t,children:r,checked:l,...c}=e;return(0,s.jsxs)(i.H_,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...void 0!==l&&{checked:l},...c,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})}),r]})});h.displayName=i.H_.displayName,r.forwardRef((e,a)=>{let{className:t,children:r,...l}=e;return(0,s.jsxs)(i.hN,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(c.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=i.hN.displayName;let p=r.forwardRef((e,a)=>{let{className:t,inset:r,...l}=e;return(0,s.jsx)(i.JU,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",t),...l})});p.displayName=i.JU.displayName;let f=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(i.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...r})});f.displayName=i.wv.displayName},55365:(e,a,t)=>{"use strict";t.d(a,{Fc:()=>c,TN:()=>o,XL:()=>d});var s=t(95155),r=t(74466),i=t(12115),l=t(54036);let n=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),c=i.forwardRef((e,a)=>{let{className:t,variant:r,...i}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:r}),t),ref:a,role:"alert",...i})});c.displayName="Alert";let d=i.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h5",{className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",t),ref:a,...r})});d.displayName="AlertTitle";let o=i.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)("text-sm [&_p]:leading-relaxed",t),ref:a,...r})});o.displayName="AlertDescription"},68856:(e,a,t)=>{"use strict";t.d(a,{E:()=>i});var s=t(95155),r=t(54036);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",a),...t})}},77023:(e,a,t)=>{"use strict";t.d(a,{gO:()=>x,jt:()=>f,pp:()=>u});var s=t(95155),r=t(11133),i=t(50172);t(12115);var l=t(6560),n=t(55365),c=t(68856),d=t(54036);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:a,className:t,data:r,emptyComponent:i,error:l,errorComponent:n,isLoading:c,loadingComponent:o,onRetry:m}=e;return c?o||(0,s.jsx)(p,{...t&&{className:t},text:"Loading..."}):l?n||(0,s.jsx)(h,{...t&&{className:t},message:l,...m&&{onRetry:m}}):!r||Array.isArray(r)&&0===r.length?i||(0,s.jsx)("div",{className:(0,d.cn)("text-center py-8",t),children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,s.jsx)("div",{className:t,children:a(r)})}function u(e){let{className:a,description:t,icon:r,primaryAction:i,secondaryAction:n,title:c}=e;return(0,s.jsxs)("div",{className:(0,d.cn)("space-y-6 text-center py-12",a),children:[r&&(0,s.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,s.jsx)(r,{className:"h-10 w-10 text-muted-foreground"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:c}),t&&(0,s.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:t})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[i&&(0,s.jsx)(l.r,{actionType:"primary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,s.jsx)("a",{href:i.href,children:i.label}):i.label}),n&&(0,s.jsx)(l.r,{actionType:"tertiary",asChild:!!n.href,icon:n.icon,onClick:n.onClick,children:n.href?(0,s.jsx)("a",{href:n.href,children:n.label}):n.label})]})]})}function h(e){let{className:a,message:t,onRetry:c}=e;return(0,s.jsxs)(n.Fc,{className:(0,d.cn)("my-4",a),variant:"destructive",children:[(0,s.jsx)(r.A,{className:"size-4"}),(0,s.jsx)(n.XL,{children:"Error"}),(0,s.jsx)(n.TN,{children:(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:t}),c&&(0,s.jsx)(l.r,{actionType:"tertiary",icon:(0,s.jsx)(i.A,{className:"size-4"}),onClick:c,size:"sm",children:"Try Again"})]})})]})}function p(e){let{className:a,fullPage:t=!1,size:r="md",text:l}=e;return(0,s.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",t&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",a),children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(i.A,{className:(0,d.cn)("animate-spin text-primary",o[r])}),l&&(0,s.jsx)("span",{className:(0,d.cn)("mt-2 text-muted-foreground",m[r]),children:l})]})})}function f(e){let{className:a,count:t=1,testId:r="loading-skeleton",variant:i="default"}=e;return"card"===i?(0,s.jsx)("div",{className:(0,d.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",a),"data-testid":r,children:Array(t).fill(0).map((e,a)=>(0,s.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,s.jsx)(c.E,{className:"aspect-[16/10] w-full"}),(0,s.jsxs)("div",{className:"p-5",children:[(0,s.jsx)(c.E,{className:"mb-1 h-7 w-3/4"}),(0,s.jsx)(c.E,{className:"mb-3 h-4 w-1/2"}),(0,s.jsx)(c.E,{className:"my-3 h-px w-full"}),(0,s.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(c.E,{className:"mr-2.5 size-5 rounded-full"}),(0,s.jsx)(c.E,{className:"h-5 w-2/3"})]},a))})]})]},a))}):"table"===i?(0,s.jsxs)("div",{className:(0,d.cn)("space-y-3",a),"data-testid":r,children:[(0,s.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,a)=>(0,s.jsx)(c.E,{className:"h-8 flex-1"},a))}),Array(t).fill(0).map((e,a)=>(0,s.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,a)=>(0,s.jsx)(c.E,{className:"h-6 flex-1"},a))},a))]}):"list"===i?(0,s.jsx)("div",{className:(0,d.cn)("space-y-3",a),"data-testid":r,children:Array(t).fill(0).map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(c.E,{className:"size-12 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(c.E,{className:"h-4 w-1/3"}),(0,s.jsx)(c.E,{className:"h-4 w-full"})]})]},a))}):"stats"===i?(0,s.jsx)("div",{className:(0,d.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",a),"data-testid":r,children:Array(t).fill(0).map((e,a)=>(0,s.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(c.E,{className:"h-5 w-1/3"}),(0,s.jsx)(c.E,{className:"size-5 rounded-full"})]}),(0,s.jsx)(c.E,{className:"mt-3 h-8 w-1/2"}),(0,s.jsx)(c.E,{className:"mt-2 h-4 w-2/3"})]},a))}):(0,s.jsx)("div",{className:(0,d.cn)("space-y-2",a),"data-testid":r,children:Array(t).fill(0).map((e,a)=>(0,s.jsx)(c.E,{className:"h-5 w-full"},a))})}},80937:(e,a,t)=>{"use strict";t.d(a,{NS:()=>h,T$:()=>o,W_:()=>m,Y1:()=>x,lR:()=>u});var s=t(26715),r=t(5041),i=t(90111),l=t(42366),n=t(99605),c=t(75908);let d={all:["vehicles"],detail:e=>["vehicles",e]},o=e=>(0,i.GK)([...d.all],async()=>(await c.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,a)=>{var t;return(0,i.GK)([...d.detail(e)],()=>c.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(t=null==a?void 0:a.enabled)||t),staleTime:3e5,...a})},x=()=>{let e=(0,s.jE)(),{showError:a,showSuccess:t}=(0,l.useNotifications)();return(0,r.n)({mutationFn:e=>{let a=n.M.toCreateRequest(e);return c.vehicleApiService.create(a)},onError:e=>{a("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:a=>{e.invalidateQueries({queryKey:d.all}),t('Vehicle "'.concat(a.licensePlate,'" has been created successfully!'))}})},u=()=>{let e=(0,s.jE)(),{showError:a,showSuccess:t}=(0,l.useNotifications)();return(0,r.n)({mutationFn:e=>{let{data:a,id:t}=e,s=n.M.toUpdateRequest(a);return c.vehicleApiService.update(t,s)},onError:e=>{a("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:a=>{e.invalidateQueries({queryKey:d.all}),e.invalidateQueries({queryKey:d.detail(a.id)}),t('Vehicle "'.concat(a.licensePlate,'" has been updated successfully!'))}})},h=()=>{let e=(0,s.jE)(),{showError:a,showSuccess:t}=(0,l.useNotifications)();return(0,r.n)({mutationFn:e=>c.vehicleApiService.delete(e),onError:e=>{a("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(a,s)=>{e.invalidateQueries({queryKey:d.all}),e.removeQueries({queryKey:d.detail(s)}),t("Vehicle has been deleted successfully!")}})}},96016:(e,a,t)=>{"use strict";t.d(a,{C:()=>i});var s=t(65453),r=t(46786);let i=(0,s.v)()((0,r.lt)((0,r.Zr)((e,a)=>({addNotification:a=>e(e=>({notifications:[...e.notifications,{...a,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:a=>e(e=>({notifications:e.notifications.map(e=>e.id===a?{...e,read:!0}:e)})),notifications:[],removeNotification:a=>e(e=>({notifications:e.notifications.filter(e=>e.id!==a)})),setTheme:a=>{e({currentTheme:a})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=a();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))},97261:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var s=t(95155),r=t(3638),i=t(66766),l=t(6874),n=t.n(l),c=t(35695),d=t(12115),o=t(33271),m=t(6560),x=t(77023),u=t(80937),h=t(50546);function p(){let e=(0,c.useParams)(),a=null==e?void 0:e.id,{data:t,error:l,isLoading:p,refetch:f}=(0,u.W_)(Number(a),{enabled:!!a});return(0,d.useEffect)(()=>{if(t){var e;null==(e=t.serviceHistory)||e.sort((e,a)=>new Date(e.date).getTime()-new Date(a.date).getTime()||e.odometer-a.odometer),document.title="".concat(t.make," ").concat(t.model," - Maintenance Report")}},[t]),(0,s.jsx)("div",{className:"mx-auto max-w-4xl bg-white p-2 text-gray-800 sm:p-4",children:(0,s.jsx)(x.gO,{data:t,emptyComponent:(0,s.jsx)("div",{className:"py-10 text-center",children:"Vehicle not found or could not be loaded."}),error:(0,h.u1)(l),isLoading:p,loadingComponent:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h1",{className:"text-center text-3xl font-bold text-gray-800",children:"Loading Report..."}),(0,s.jsx)(x.jt,{count:1,variant:"card"}),(0,s.jsx)(x.jt,{className:"mt-6",count:3,variant:"table"})]}),onRetry:()=>{f()},children:e=>{var t,l,c;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"no-print mb-4 flex items-center justify-between",children:[(0,s.jsx)(m.r,{actionType:"tertiary",asChild:!0,icon:(0,s.jsx)(r.A,{className:"size-4"}),children:(0,s.jsx)(n(),{href:"/vehicles/".concat(a,"/report/service-history"),children:"Detailed Service History"})}),(0,s.jsx)(o.k,{enableCsv:((null==(t=e.serviceHistory)?void 0:t.length)||0)>0,entityId:a,fileName:"vehicle-report-".concat(e.make,"-").concat(e.model),reportContentId:"#vehicle-report-content",reportType:"vehicle",tableId:"#service-history-table"})]}),(0,s.jsxs)("div",{className:"report-content",id:"vehicle-report-content",children:[(0,s.jsxs)("header",{className:"report-header mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Maintenance Report"}),(0,s.jsxs)("p",{className:"text-xl text-gray-600",children:[e.make," ",e.model," (",e.year,")"]}),e.licensePlate&&(0,s.jsxs)("p",{className:"text-md text-gray-500",children:["Plate: ",e.licensePlate]})]}),(0,s.jsxs)("section",{className:"card-print mb-8 rounded border border-gray-200 p-4",children:[(0,s.jsx)("h2",{className:"mb-4 border-b border-gray-200 pb-2 text-2xl font-semibold text-gray-700",children:"Vehicle Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm md:grid-cols-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Make:"})," ",e.make]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Model:"})," ",e.model]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Year:"})," ",e.year]}),e.licensePlate&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Plate Number:"})," ",e.licensePlate]}),e.color&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Color:"})," ",e.color]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Initial Odometer:"})," ",null!==e.initialOdometer&&void 0!==e.initialOdometer?"".concat(e.initialOdometer.toLocaleString()," miles"):"Not recorded"]})]}),e.imageUrl&&(0,s.jsx)("div",{className:"no-print relative mx-auto mt-4 aspect-[16/9] w-full max-w-md overflow-hidden rounded",children:(0,s.jsx)(i.default,{alt:"".concat(e.make," ").concat(e.model),"data-ai-hint":"car side",layout:"fill",objectFit:"contain",src:e.imageUrl})})]}),(0,s.jsxs)("section",{className:"card-print rounded border border-gray-200 p-4",children:[(0,s.jsxs)("div",{className:"mb-4 flex items-center justify-between border-b border-gray-200 pb-2",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-700",children:"Service History"}),(0,s.jsx)("p",{className:"no-print text-sm text-gray-500",children:(0,s.jsx)(n(),{className:"text-blue-600 hover:underline",href:"/vehicles/".concat(a,"/report/service-history"),children:"View detailed service history"})})]}),(null==(l=e.serviceHistory)?void 0:l.length)===0?(0,s.jsx)("p",{className:"text-gray-500",children:"No service records available for this vehicle."}):(0,s.jsxs)("table",{className:"w-full text-left text-sm text-gray-600",id:"service-history-table",children:[(0,s.jsx)("thead",{className:"bg-gray-50 text-xs uppercase text-gray-700",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Date"}),(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Odometer"}),(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Service Performed"}),(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Notes"}),(0,s.jsx)("th",{className:"px-3 py-2 text-right",scope:"col",children:"Cost"})]})}),(0,s.jsx)("tbody",{children:null==(c=e.serviceHistory)?void 0:c.map(e=>(0,s.jsxs)("tr",{className:"border-b bg-white hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-3 py-2",children:new Date(e.date).toLocaleDateString()}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.odometer.toLocaleString()}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.servicePerformed.join(", ")}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"}),(0,s.jsx)("td",{className:"px-3 py-2 text-right",children:e.cost?"$".concat(Number(e.cost).toFixed(2)):"-"})]},e.id))})]})]}),(0,s.jsxs)("footer",{className:"report-footer mt-12 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,s.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,s.jsx)("p",{children:"WorkHub"})]})]})]})}})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6476,7047,3860,9664,1263,5495,6874,5247,6766,2174,4036,4767,8950,3712,8441,1684,7358],()=>a(20957)),_N_E=e.O()}]);