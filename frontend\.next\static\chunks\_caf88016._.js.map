{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Breadcrumb = React.forwardRef<\r\n  HTMLElement,\r\n  React.HTMLAttributes<HTMLElement>\r\n>(({ className, ...props }, ref) => (\r\n  <nav\r\n    aria-label=\"breadcrumb\"\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumb.displayName = 'Breadcrumb';\r\n\r\nconst BreadcrumbList = React.forwardRef<\r\n  HTMLOListElement,\r\n  React.OlHTMLAttributes<HTMLOListElement>\r\n>(({ className, ...props }, ref) => (\r\n  <ol\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbList.displayName = 'BreadcrumbList';\r\n\r\nconst BreadcrumbItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.LiHTMLAttributes<HTMLLIElement>\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    className={cn('inline-flex items-center gap-1.5', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbItem.displayName = 'BreadcrumbItem';\r\n\r\nconst BreadcrumbLink = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.AnchorHTMLAttributes<HTMLAnchorElement> & {\r\n    asChild?: boolean;\r\n  }\r\n>(({ asChild, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      className={cn('transition-colors hover:text-foreground', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nBreadcrumbLink.displayName = 'BreadcrumbLink';\r\n\r\nconst BreadcrumbPage = React.forwardRef<\r\n  HTMLSpanElement,\r\n  React.HTMLAttributes<HTMLSpanElement>\r\n>(({ className, ...props }, ref) => (\r\n  <span\r\n    aria-current=\"page\"\r\n    aria-disabled=\"true\"\r\n    className={cn('font-normal text-foreground', className)}\r\n    ref={ref}\r\n    role=\"link\"\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbPage.displayName = 'BreadcrumbPage';\r\n\r\nconst BreadcrumbSeparator = ({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('[&>svg]:size-3.5', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    {children ?? <ChevronRight className=\"size-4\" />}\r\n  </span>\r\n);\r\nBreadcrumbSeparator.displayName = 'BreadcrumbSeparator';\r\n\r\nconst BreadcrumbEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"size-4\" />\r\n    <span className=\"sr-only\">More</span>\r\n  </span>\r\n);\r\nBreadcrumbEllipsis.displayName = 'BreadcrumbElipssis';\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbEllipsis,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAClD,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACzD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,gBAAa;QACb,iBAAc;QACd,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC7C,KAAK;QACL,MAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACmC,iBACtC,6LAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QAClC,MAAK;QACJ,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;;;;;;OAXnC;AAcN,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACmC,iBACtC,6LAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,MAAK;QACJ,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;OAXxB;AAcN,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "file": "ellipsis.js", "sources": ["file:///C:/Projects/WorkHub/frontend/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('Ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}