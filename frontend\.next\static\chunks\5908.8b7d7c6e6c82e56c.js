"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5908],{75908:(e,t,i)=>{i.d(t,{cl:()=>v,delegationApiService:()=>S,employeeApiService:()=>w,reliabilityApiService:()=>C,taskApiService:()=>m,vehicleApiService:()=>d});var r=i(55411),a=i(976),s=i(12430),n=i(25982);let c={fromApi:e=>e,toApi:e=>e};class l extends n.v{async getSystemHealth(){return this.executeWithInfrastructure("health:system",async()=>await this.apiClient.get("/health"))}async getDetailedHealth(){return this.executeWithInfrastructure("health:detailed",async()=>await this.apiClient.get("/health/detailed"))}async getDependencyHealth(){return this.executeWithInfrastructure("health:dependencies",async()=>await this.apiClient.get("/health/dependencies"))}async getCircuitBreakerStatus(){return this.executeWithInfrastructure("monitoring:circuit-breakers",async()=>{try{let e=await this.apiClient.get("/monitoring/circuit-breakers"),t=(null==e?void 0:e.circuitBreakers)||[];return{circuitBreakers:t||[],summary:{total:(null==t?void 0:t.length)||0,closed:(null==t?void 0:t.filter(e=>"CLOSED"===e.state).length)||0,open:(null==t?void 0:t.filter(e=>"OPEN"===e.state).length)||0,halfOpen:(null==t?void 0:t.filter(e=>"HALF_OPEN"===e.state).length)||0}}}catch(e){return console.error("Failed to get circuit breaker status:",e),{circuitBreakers:[],summary:{total:0,closed:0,open:0,halfOpen:0}}}})}async getDeduplicationMetrics(){return this.executeWithInfrastructure("monitoring:deduplication",async()=>await this.apiClient.get("/monitoring/deduplication"))}async getMetrics(){return this.executeWithInfrastructure("metrics:system",async()=>await this.apiClient.get("/metrics",{headers:{Accept:"application/json"}}))}async getActiveAlerts(){return this.executeWithInfrastructure("alerts:active",async()=>{try{let e=await this.apiClient.get("/alerts");return(null==e?void 0:e.alerts)||[]}catch(e){return console.error("Failed to get active alerts:",e),[]}})}async getAlertHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return this.executeWithInfrastructure("alerts:history:".concat(e,":").concat(t),async()=>{let i=new URLSearchParams({page:e.toString(),limit:t.toString()});return await this.apiClient.get("/alerts/history?".concat(i.toString()))})}async getAlertStatistics(){return this.executeWithInfrastructure("alerts:statistics",async()=>{try{return await this.apiClient.get("/alerts/statistics")}catch(e){return console.error("Failed to get alert statistics:",e),{total:0,active:0,acknowledged:0,resolved:0,bySeverity:{low:0,medium:0,high:0,critical:0},averageResolutionTime:0,recentTrends:{last24Hours:0,last7Days:0,last30Days:0}}}})}async resolveAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let r=await this.apiClient.post("/alerts/".concat(e,"/resolve"),{reason:t,resolvedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),r})}async acknowledgeAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let r=await this.apiClient.post("/alerts/".concat(e,"/acknowledge"),{note:t,acknowledgedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),r})}async testAlerts(){return this.executeWithInfrastructure(null,async()=>{var e;let t=await this.apiClient.post("/alerts/test");return{success:(null==t?void 0:t.status)==="success",message:(null==t?void 0:t.message)||"Test alert triggered",testAlertId:null==t||null==(e=t.data)?void 0:e.id}})}async getReliabilityDashboardData(){let[e,t,i,r,a,s]=await Promise.all([this.getSystemHealth(),this.getDetailedHealth(),this.getCircuitBreakerStatus(),this.getMetrics(),this.getActiveAlerts(),this.getAlertStatistics()]);return{systemHealth:e,detailedHealth:t,circuitBreakers:i,metrics:r,activeAlerts:a,alertStatistics:s}}async isSystemHealthy(){try{let e=await this.getSystemHealth();return"healthy"===e.status}catch(e){return!1}}async getCriticalAlertCount(){try{return(await this.getAlertStatistics()).bySeverity.critical}catch(e){return 0}}async getHealthTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h";return this.executeWithInfrastructure("health:trends:".concat(e),async()=>await this.apiClient.get("/health/trends?timeframe=".concat(e)))}async getCircuitBreakerHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h",t=arguments.length>1?arguments[1]:void 0;return this.executeWithInfrastructure("circuit-breakers:history:".concat(e,":").concat(t||"all"),async()=>{let i=new URLSearchParams({timeframe:e});return t&&i.append("breakerName",t),await this.apiClient.get("/monitoring/circuit-breakers/history?".concat(i.toString()))})}async getHttpRequestMetrics(){return this.executeWithInfrastructure("http:metrics",async()=>await this.apiClient.get("/monitoring/http-request-metrics"))}constructor(e,t){super(e,{cacheDuration:6e4,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/reliability",this.transformer=c}}var h=i(90137),u=i(97966),o=i(38549),g=i(72248);function y(){let e=(0,g.Sk)();if(!e)return null;try{return e()}catch(e){return console.error("❌ Factory: Error getting auth token from secure provider:",e),null}}class p{getApiClient(){return this.apiClient}getDelegationService(){return this.delegationService||(this.delegationService=new a.y(this.apiClient)),this.delegationService}getEmployeeService(){return this.employeeService||(this.employeeService=new s.Q(this.apiClient)),this.employeeService}getReliabilityService(){return this.reliabilityService||(this.reliabilityService=new l(this.apiClient)),this.reliabilityService}getTaskService(){return this.taskService||(this.taskService=new h.D(this.apiClient)),this.taskService}getVehicleService(){return this.vehicleService||(this.vehicleService=new u.C(this.apiClient)),this.vehicleService}constructor(e){this.apiClient=new r.O({...e,getAuthToken:y})}}let v=new p({baseURL:(0,o.Qq)().apiBaseUrl,headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4}),d=v.getVehicleService(),S=v.getDelegationService(),m=v.getTaskService(),w=v.getEmployeeService(),C=v.getReliabilityService()}}]);