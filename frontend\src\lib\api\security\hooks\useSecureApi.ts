/**
 * @file Secure API Hook - UPDATED to use new architecture
 * @module hooks/useSecureApi
 *
 * Phase 4 Migration: This hook now uses the new secure API architecture
 * instead of duplicating HTTP logic. It provides the same interface but
 * leverages the enhanced SecureApiClient under the hood.
 *
 * MIGRATION NOTE: This hook maintains backward compatibility while using
 * the new architecture. HTTP duplication has been eliminated.
 */

'use client';

import { useCallback } from 'react';

// Import from new architecture to eliminate HTTP duplication
import { useSecureApiReplacement } from './useSecureApiClient';

// Re-export types for backward compatibility
export interface ApiResponse<T = any> {
  data: T;
  headers: Record<string, string>;
  status: number;
  statusText: string;
}

export interface RequestConfig {
  data?: any;
  headers?: Record<string, string>;
  method?: 'DELETE' | 'GET' | 'PATCH' | 'POST' | 'PUT';
  timeout?: number;
  url: string;
}

export interface UseSecureApiReturn {
  hasValidToken: boolean;
  isAuthenticated: boolean;
  refreshToken: () => Promise<boolean>;
  sanitizeInput: (input: any) => any;
  secureRequest: <T = any>(config: RequestConfig) => Promise<ApiResponse<T>>;
}

/**
 * Custom ApiError class for consistent error handling
 * Maintained for backward compatibility
 */
export class ApiError extends Error {
  public code?: string;
  public details?: any;
  public status?: number;

  constructor({
    code,
    details,
    message,
    status,
  }: {
    code?: string;
    details?: any;
    message: string;
    status?: number;
  }) {
    super(message);
    this.name = 'ApiError';
    this.status = status || 0;
    this.code = code || '';
    this.details = details;
  }
}

/**
 * Secure API Hook - UPDATED to use new architecture
 *
 * Phase 4 Migration: Now uses the enhanced SecureApiClient architecture
 * instead of duplicating HTTP logic. Maintains the same interface for
 * backward compatibility while providing enhanced security features.
 */
export function useSecureApi(): UseSecureApiReturn {
  // Use the new architecture under the hood (eliminates HTTP duplication)
  const newApiClient = useSecureApiReplacement({
    enableLogging: true,
  });

  // Convert secureRequest to match the old interface
  const secureRequest = useCallback(
    async <T = any>(config: RequestConfig): Promise<ApiResponse<T>> => {
      try {
        // Use the new architecture's secureRequest method
        return await newApiClient.secureRequest<T>({
          url: config.url,
          method: config.method || 'GET',
          data: config.data,
          headers: config.headers || {},
          timeout: config.timeout || 10000,
        });
      } catch (error) {
        // Convert errors to ApiError for backward compatibility
        if (error instanceof Error) {
          throw new ApiError({
            code: 'REQUEST_FAILED',
            message: error.message,
            details: error,
          });
        }
        throw error;
      }
    },
    [newApiClient]
  );

  return {
    hasValidToken: newApiClient.hasValidToken,
    isAuthenticated: newApiClient.isAuthenticated,
    refreshToken: newApiClient.refreshToken,
    sanitizeInput: newApiClient.sanitizeInput,
    secureRequest,
  };
}
