"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5908,8950],{55012:(e,t,n)=>{n.d(t,{gv:()=>h,j9:()=>l});var i=n(14298),r=n(38549),c=n(14163),s=n(28113),o=n(87358);class a{static getInstance(e){var t;return null!=a.instance||(a.instance=new a(e)),a.instance}async connect(){var e;if(null==(e=this.socket)?void 0:e.connected)return void console.debug("WebSocket already connected");this.setConnectionState("connecting");try{let{data:{session:e},error:t}=await c.N.auth.getSession();t&&console.warn("Failed to get session for WebSocket connection:",t);let n={forceNew:!0,timeout:this.config.timeout,transports:["websocket","polling"],withCredentials:!0};if(null==e?void 0:e.access_token){n.auth={token:e.access_token},console.debug("\uD83D\uDD10 WebSocket connecting with authentication token");let t=e.expires_at?1e3*e.expires_at:0,i=Date.now();t-i<=6e4&&console.warn("⚠️ WebSocket token expires soon, may need refresh")}else console.warn("⚠️ WebSocket connecting without authentication token - connection may fail");this.socket=(0,i.io)(this.config.url,n),this.setupEventHandlers()}catch(e){console.error("Failed to connect WebSocket:",e),this.setConnectionState("error"),this.scheduleReconnect()}}destroy(){this.disconnect(),this.subscriptions.clear(),this.stateListeners.clear(),a.instance=null}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionState("disconnected"),this.reconnectAttempts=0}emit(e,t,n){var i;if(!(null==(i=this.socket)?void 0:i.connected))return void console.warn("Cannot emit ".concat(e,":").concat(t," - WebSocket not connected"));this.socket.emit(t,n)}getConnectionState(){return this.connectionState}isConnected(){var e;return"connected"===this.connectionState&&(null==(e=this.socket)?void 0:e.connected)===!0}joinRoom(e){var t;if(!(null==(t=this.socket)?void 0:t.connected))return void console.warn("Cannot join room ".concat(e," - WebSocket not connected"));this.socket.emit("join-room",e)}leaveRoom(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.socket.emit("leave-room",e)}onStateChange(e){return this.stateListeners.add(e),()=>{this.stateListeners.delete(e)}}subscribe(e,t,n){var i;let r="".concat(e,":").concat(t);return this.subscriptions.has(r)||this.subscriptions.set(r,new Set),this.subscriptions.get(r).add(n),(null==(i=this.socket)?void 0:i.connected)&&t&&this.socket.on(t,n),()=>{let e=this.subscriptions.get(r);e&&(e.delete(n),0===e.size&&this.subscriptions.delete(r)),this.socket&&t&&this.socket.off(t,n)}}handleAuthenticationError(){let e=(0,s.Q)();console.log("\uD83D\uDD10 Handling WebSocket authentication error..."),this.socket&&(this.socket.disconnect(),this.socket=null),e.refreshNow().then(e=>{e?console.log("\uD83D\uDD04 Token refresh successful, retrying WebSocket connection"):(console.error("\uD83D\uDD04 Token refresh failed, scheduling normal reconnect"),this.scheduleReconnect())}).catch(e=>{console.error("\uD83D\uDD04 Token refresh error:",e),this.scheduleReconnect()})}resubscribeToEvents(){if(this.socket)for(let[e,t]of this.subscriptions){let[,n]=e.split(":");for(let e of t)n&&this.socket.on(n,e)}}scheduleReconnect(){if(this.reconnectAttempts>=this.config.reconnectAttempts){console.error("Max reconnection attempts reached"),this.setConnectionState("error");return}this.setConnectionState("reconnecting"),this.reconnectAttempts++,setTimeout(()=>{console.info("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.config.reconnectAttempts,")")),this.connect()},this.config.reconnectDelay*Math.pow(2,this.reconnectAttempts-1))}setConnectionState(e){if(this.connectionState!==e)for(let t of(this.connectionState=e,this.stateListeners))t(e)}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.info("WebSocket connected"),this.setConnectionState("connected"),this.reconnectAttempts=0,this.resubscribeToEvents()}),this.socket.on("disconnect",e=>{console.warn("WebSocket disconnected:",e),this.setConnectionState("disconnected"),"io server disconnect"!==e&&this.scheduleReconnect()}),this.socket.on("connect_error",e=>{var t,n,i,r;console.error("WebSocket connection error:",e),this.setConnectionState("error"),(null==(t=e.message)?void 0:t.includes("Authentication"))||(null==(n=e.message)?void 0:n.includes("token"))||(null==(i=e.message)?void 0:i.includes("No token provided"))||(null==(r=e.message)?void 0:r.includes("Unauthorized"))?(console.warn("\uD83D\uDD10 Authentication error detected, attempting token refresh"),this.handleAuthenticationError()):this.scheduleReconnect()}),this.socket.on("auth_error",e=>{console.error("\uD83D\uDD10 Server authentication error:",e),this.handleAuthenticationError()}),this.socket.on("token_refresh_required",()=>{console.warn("\uD83D\uDD04 Server requested token refresh"),this.handleAuthenticationError()}))}setupTokenRefreshHandling(){(0,s.Q)().subscribe((e,t)=>{switch(e){case"critical_refresh_failed":console.error("\uD83D\uDD04 Critical token refresh failure, disconnecting WebSocket"),this.disconnect(),this.setConnectionState("error");break;case"refresh_failed":console.error("\uD83D\uDD04 Token refresh failed, WebSocket may lose connection");break;case"refresh_success":console.log("\uD83D\uDD04 Token refreshed, reconnecting WebSocket with new token"),this.socket&&(this.socket.disconnect(),this.socket=null),setTimeout(()=>this.connect(),500)}})}constructor(e={}){var t,n,i,c,s,a;this.connectionState="disconnected",this.reconnectAttempts=0,this.socket=null,this.stateListeners=new Set,this.subscriptions=new Map,this.config={autoConnect:null==(t=e.autoConnect)||t,reconnectAttempts:null!=(n=e.reconnectAttempts)?n:5,reconnectDelay:null!=(i=e.reconnectDelay)?i:1e3,timeout:null!=(c=e.timeout)?c:1e4,url:null!=(a=null!=(s=e.url)?s:o.env.NEXT_PUBLIC_WEBSOCKET_URL)?a:(0,r.Qq)().wsUrl.replace("ws://","http://").replace("wss://","https://")},this.config.autoConnect&&this.connect(),this.setupTokenRefreshHandling()}}a.instance=null;let l=e=>a.getInstance(e),h=()=>{let e=l();return{connectionState:e.getConnectionState(),isConnected:e.isConnected()}}},75908:(e,t,n)=>{n.d(t,{cl:()=>v,delegationApiService:()=>m,employeeApiService:()=>k,reliabilityApiService:()=>S,taskApiService:()=>f,vehicleApiService:()=>y});var i=n(55411),r=n(976),c=n(12430),s=n(25982);let o={fromApi:e=>e,toApi:e=>e};class a extends s.v{async getSystemHealth(){return this.executeWithInfrastructure("health:system",async()=>await this.apiClient.get("/health"))}async getDetailedHealth(){return this.executeWithInfrastructure("health:detailed",async()=>await this.apiClient.get("/health/detailed"))}async getDependencyHealth(){return this.executeWithInfrastructure("health:dependencies",async()=>await this.apiClient.get("/health/dependencies"))}async getCircuitBreakerStatus(){return this.executeWithInfrastructure("monitoring:circuit-breakers",async()=>{try{let e=await this.apiClient.get("/monitoring/circuit-breakers"),t=(null==e?void 0:e.circuitBreakers)||[];return{circuitBreakers:t||[],summary:{total:(null==t?void 0:t.length)||0,closed:(null==t?void 0:t.filter(e=>"CLOSED"===e.state).length)||0,open:(null==t?void 0:t.filter(e=>"OPEN"===e.state).length)||0,halfOpen:(null==t?void 0:t.filter(e=>"HALF_OPEN"===e.state).length)||0}}}catch(e){return console.error("Failed to get circuit breaker status:",e),{circuitBreakers:[],summary:{total:0,closed:0,open:0,halfOpen:0}}}})}async getDeduplicationMetrics(){return this.executeWithInfrastructure("monitoring:deduplication",async()=>await this.apiClient.get("/monitoring/deduplication"))}async getMetrics(){return this.executeWithInfrastructure("metrics:system",async()=>await this.apiClient.get("/metrics",{headers:{Accept:"application/json"}}))}async getActiveAlerts(){return this.executeWithInfrastructure("alerts:active",async()=>{try{let e=await this.apiClient.get("/alerts");return(null==e?void 0:e.alerts)||[]}catch(e){return console.error("Failed to get active alerts:",e),[]}})}async getAlertHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return this.executeWithInfrastructure("alerts:history:".concat(e,":").concat(t),async()=>{let n=new URLSearchParams({page:e.toString(),limit:t.toString()});return await this.apiClient.get("/alerts/history?".concat(n.toString()))})}async getAlertStatistics(){return this.executeWithInfrastructure("alerts:statistics",async()=>{try{return await this.apiClient.get("/alerts/statistics")}catch(e){return console.error("Failed to get alert statistics:",e),{total:0,active:0,acknowledged:0,resolved:0,bySeverity:{low:0,medium:0,high:0,critical:0},averageResolutionTime:0,recentTrends:{last24Hours:0,last7Days:0,last30Days:0}}}})}async resolveAlert(e,t,n){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.post("/alerts/".concat(e,"/resolve"),{reason:t,resolvedBy:n});return this.cache.invalidatePattern(RegExp("^alerts:")),i})}async acknowledgeAlert(e,t,n){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.post("/alerts/".concat(e,"/acknowledge"),{note:t,acknowledgedBy:n});return this.cache.invalidatePattern(RegExp("^alerts:")),i})}async testAlerts(){return this.executeWithInfrastructure(null,async()=>{var e;let t=await this.apiClient.post("/alerts/test");return{success:(null==t?void 0:t.status)==="success",message:(null==t?void 0:t.message)||"Test alert triggered",testAlertId:null==t||null==(e=t.data)?void 0:e.id}})}async getReliabilityDashboardData(){let[e,t,n,i,r,c]=await Promise.all([this.getSystemHealth(),this.getDetailedHealth(),this.getCircuitBreakerStatus(),this.getMetrics(),this.getActiveAlerts(),this.getAlertStatistics()]);return{systemHealth:e,detailedHealth:t,circuitBreakers:n,metrics:i,activeAlerts:r,alertStatistics:c}}async isSystemHealthy(){try{let e=await this.getSystemHealth();return"healthy"===e.status}catch(e){return!1}}async getCriticalAlertCount(){try{return(await this.getAlertStatistics()).bySeverity.critical}catch(e){return 0}}async getHealthTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h";return this.executeWithInfrastructure("health:trends:".concat(e),async()=>await this.apiClient.get("/health/trends?timeframe=".concat(e)))}async getCircuitBreakerHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h",t=arguments.length>1?arguments[1]:void 0;return this.executeWithInfrastructure("circuit-breakers:history:".concat(e,":").concat(t||"all"),async()=>{let n=new URLSearchParams({timeframe:e});return t&&n.append("breakerName",t),await this.apiClient.get("/monitoring/circuit-breakers/history?".concat(n.toString()))})}async getHttpRequestMetrics(){return this.executeWithInfrastructure("http:metrics",async()=>await this.apiClient.get("/monitoring/http-request-metrics"))}constructor(e,t){super(e,{cacheDuration:6e4,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/reliability",this.transformer=o}}var l=n(90137),h=n(97966),u=n(38549),d=n(72248);function g(){let e=(0,d.Sk)();if(!e)return null;try{return e()}catch(e){return console.error("❌ Factory: Error getting auth token from secure provider:",e),null}}class p{getApiClient(){return this.apiClient}getDelegationService(){return this.delegationService||(this.delegationService=new r.y(this.apiClient)),this.delegationService}getEmployeeService(){return this.employeeService||(this.employeeService=new c.Q(this.apiClient)),this.employeeService}getReliabilityService(){return this.reliabilityService||(this.reliabilityService=new a(this.apiClient)),this.reliabilityService}getTaskService(){return this.taskService||(this.taskService=new l.D(this.apiClient)),this.taskService}getVehicleService(){return this.vehicleService||(this.vehicleService=new h.C(this.apiClient)),this.vehicleService}constructor(e){this.apiClient=new i.O({...e,getAuthToken:g})}}let v=new p({baseURL:(0,u.Qq)().apiBaseUrl,headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4}),y=v.getVehicleService(),m=v.getDelegationService(),f=v.getTaskService(),k=v.getEmployeeService(),S=v.getReliabilityService()},90111:(e,t,n)=>{n.d(t,{GK:()=>a,ol:()=>l});var i=n(26715),r=n(28755),c=n(12115),s=n(55012);let o={crud:"entity-updates",notifications:"notifications-monitoring",reliability:"reliability-monitoring",system:"system-monitoring"};function a(e,t,n,i){return h(e,t,{channel:"crud",events:["".concat(n,":created"),"".concat(n,":updated"),"".concat(n,":deleted"),"refresh:".concat(n)],fallbackInterval:3e4},i)}function l(e,t,n,i){let r=(0,s.j9)();return(0,c.useEffect)(()=>{r.isConnected()&&(console.debug("[ReliabilityQuery] Joining reliability-monitoring room for ".concat(n)),r.joinRoom("reliability-monitoring"));let e=r.onStateChange(e=>{"connected"===e&&(console.debug("[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ".concat(n)),r.joinRoom("reliability-monitoring"))});return()=>{e(),r.isConnected()&&r.leaveRoom("reliability-monitoring")}},[r,n]),h(e,t,{channel:"reliability",events:["".concat(n,"-update"),"".concat(n,"-created"),"".concat(n,"-resolved")],fallbackInterval:{alerts:3e4,"circuit-breakers":6e4,health:45e3,metrics:6e4}[n]},i)}function h(e,t,n,a){let{channel:l,enableFallback:h=!0,enableWebSocket:u=!0,events:d,fallbackInterval:g=3e4}=n,[p,v]=(0,c.useState)(!1),y=(0,s.j9)();(0,c.useEffect)(()=>{let e=()=>{v(y.isConnected())};return e(),y.onStateChange(e)},[y]);let m=h&&(!u||!p),f={gcTime:6e5,queryFn:t,queryKey:e,refetchInterval:!!m&&g,refetchOnReconnect:!0,refetchOnWindowFocus:m,staleTime:3e4*!p,...a},k=(0,i.jE)(),S=(0,r.I)(f);return(0,c.useEffect)(()=>{if(!u||!p)return;let e=o[l];if(!e)return void console.warn("[SmartQuery] No room mapping found for channel: ".concat(l));try{y.joinRoom(e),console.log("[SmartQuery] Joined room: ".concat(e," for channel: ").concat(l))}catch(t){console.error("[SmartQuery] Failed to join room ".concat(e,":"),t)}return()=>{try{y.leaveRoom(e),console.log("[SmartQuery] Left room: ".concat(e," for channel: ").concat(l))}catch(t){console.error("[SmartQuery] Failed to leave room ".concat(e,":"),t)}}},[u,p,l,y]),(0,c.useEffect)(()=>{if(!u||!p||0===d.length)return;let t=[];for(let n of d){let i=y.subscribe(l,n,t=>{console.log("[SmartQuery] WebSocket event received: ".concat(l,":").concat(n),t),k.invalidateQueries({queryKey:e})});t.push(i)}return()=>{for(let e of t)e()}},[u,p,d,l,y,k,e]),{...S,isUsingFallback:m,isWebSocketConnected:p}}}}]);