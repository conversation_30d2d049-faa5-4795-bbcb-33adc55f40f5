(()=>{var e={};e.id=9989,e.ids=[9989],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11384:(e,s,t)=>{Promise.resolve().then(t.bind(t,89009))},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21112:(e,s,t)=>{Promise.resolve().then(t.bind(t,59075))},21820:e=>{"use strict";e.exports=require("os")},24501:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35137:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},36558:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},45208:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>h,tree:()=>l});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),c=t(30893),d={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);t.d(s,d);let l={children:["",{children:["service-records",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,89009)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\service-records\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Projects\\WorkHub\\frontend\\src\\app\\service-records\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/service-records/[id]/page",pathname:"/service-records/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},54050:(e,s,t)=>{"use strict";t.d(s,{n:()=>o});var r=t(43210),i=t(65406),a=t(33465),n=t(35536),c=t(31212),d=class extends n.Q{#e;#s=void 0;#t;#r;constructor(e,s){super(),this.#e=e,this.setOptions(s),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let s=this.options;this.options=this.#e.defaultMutationOptions(e),(0,c.f8)(this.options,s)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#t,observer:this}),s?.mutationKey&&this.options.mutationKey&&(0,c.EN)(s.mutationKey)!==(0,c.EN)(this.options.mutationKey)?this.reset():this.#t?.state.status==="pending"&&this.#t.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#t?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#a(e)}getCurrentResult(){return this.#s}reset(){this.#t?.removeObserver(this),this.#t=void 0,this.#i(),this.#a()}mutate(e,s){return this.#r=s,this.#t?.removeObserver(this),this.#t=this.#e.getMutationCache().build(this.#e,this.options),this.#t.addObserver(this),this.#t.execute(e)}#i(){let e=this.#t?.state??(0,i.$)();this.#s={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){a.jG.batch(()=>{if(this.#r&&this.hasListeners()){let s=this.#s.variables,t=this.#s.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,s,t),this.#r.onSettled?.(e.data,null,s,t)):e?.type==="error"&&(this.#r.onError?.(e.error,s,t),this.#r.onSettled?.(void 0,e.error,s,t))}this.listeners.forEach(e=>{e(this.#s)})})}},l=t(8693);function o(e,s){let t=(0,l.jE)(s),[i]=r.useState(()=>new d(t,e));r.useEffect(()=>{i.setOptions(e)},[i,e]);let n=r.useSyncExternalStore(r.useCallback(e=>i.subscribe(a.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),o=r.useCallback((e,s)=>{i.mutate(e,s).catch(c.lQ)},[i]);if(n.error&&(0,c.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:o,mutateAsync:n.mutate}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59075:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var r=t(60687),i=t(16189),a=t(44493),n=t(85726),c=t(35950),d=t(29523),l=t(96834),o=t(91821),u=t(55925),h=t(85814),m=t.n(h),x=t(55817),p=t(24920),j=t(35137),f=t(57207),v=t(14975),g=t(38765),N=t(36644),b=t(26622),y=t(36558),w=t(24501),A=t(3389),k=t(2775);t(43210);var M=t(60368),P=t(68012);function q({vehicleInfo:e,showViewButton:s=!0}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"Vehicle Information"]})}),(0,r.jsxs)(a.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Make & Model"}),(0,r.jsxs)("p",{className:"font-semibold",children:[e.make," ",e.model]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Year"}),(0,r.jsx)("p",{className:"font-semibold",children:e.year})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"License Plate"}),(0,r.jsx)("p",{className:"font-semibold",children:e.licensePlate})]}),e.color&&(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Color"}),(0,r.jsx)("p",{className:"font-semibold",children:e.color})]})]}),s&&(0,r.jsx)(d.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,r.jsxs)(m(),{href:`/vehicles/${e.id}`,children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"View Vehicle Details",(0,r.jsx)(M.A,{className:"ml-2 h-4 w-4"})]})})]})]})}function C(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"Vehicle Information"]})}),(0,r.jsxs)(a.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Make & Model"}),(0,r.jsx)(n.E,{className:"h-6 w-32"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Year"}),(0,r.jsx)(n.E,{className:"h-6 w-16"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"License Plate"}),(0,r.jsx)(n.E,{className:"h-6 w-24"})]})]}),(0,r.jsx)(n.E,{className:"h-10 w-full"})]})]})}function E(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5"}),"Vehicle Information"]})}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Unable to load vehicle information"})})]})}function R({vehicleId:e,showViewButton:s=!0,className:t}){let{vehicleInfo:i,isLoading:n,error:c}=(0,P.g)(e);return(0,r.jsxs)(a.Zp,{className:t,children:[n&&(0,r.jsx)(C,{}),c&&(0,r.jsx)(E,{}),i&&(0,r.jsx)(q,{vehicleInfo:i,showViewButton:s})]})}function S(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),{toast:t}=(0,A.dj)();if(!e||"string"!=typeof e.id)return(0,r.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,r.jsx)("p",{children:"Error: Invalid or missing Service Record ID."}),(0,r.jsx)(d.$,{asChild:!0,className:"mt-4",children:(0,r.jsxs)(m(),{href:"/service-history",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})})]});let h=e.id,{data:M,isLoading:P,error:q}=(0,k.WV)(h,{enabled:!!h}),C=(0,k.xT)(),E=async()=>{if(M)try{await C.mutateAsync({id:h,vehicleId:M.vehicleId}),t({title:"Deleted!",description:"Service record deleted successfully.",variant:"default"}),s.push("/service-history")}catch(e){t({title:"Error",description:"Failed to delete service record. Please try again.",variant:"destructive"})}};return P?(0,r.jsx)("div",{className:"container mx-auto py-8",children:(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsx)(n.E,{className:"h-8 w-3/4"})}),(0,r.jsxs)(a.Wu,{className:"space-y-4",children:[(0,r.jsx)(n.E,{className:"h-4 w-full"}),(0,r.jsx)(n.E,{className:"h-4 w-full"}),(0,r.jsx)(n.E,{className:"h-4 w-2/3"})]})]})}):q?(0,r.jsxs)("div",{className:"container mx-auto py-8 text-center text-red-500",children:[(0,r.jsxs)("p",{children:["Error loading service record: ",q.message]}),(0,r.jsx)(d.$,{asChild:!0,className:"mt-4",children:(0,r.jsxs)(m(),{href:"/service-history",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})})]}):M||P?M?(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(d.$,{asChild:!0,variant:"outline",size:"sm",children:(0,r.jsxs)(m(),{href:"/service-history",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,r.jsx)(p.A,{className:"h-3 w-3"}),M.vehicleMake," ",M.vehicleModel]}),(0,r.jsx)(d.$,{asChild:!0,size:"sm",children:(0,r.jsxs)(m(),{href:`/service-records/${M.id}/edit`,children:[(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,r.jsxs)(u.Lt,{children:[(0,r.jsx)(u.tv,{asChild:!0,children:(0,r.jsxs)(d.$,{variant:"destructive",size:"sm",disabled:C.isPending,children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Delete"]})}),(0,r.jsxs)(u.EO,{children:[(0,r.jsxs)(u.wd,{children:[(0,r.jsxs)(u.r7,{className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 text-destructive"}),"Delete Service Record"]}),(0,r.jsxs)(u.$v,{children:["Are you sure you want to delete this service record? This action cannot be undone.",(0,r.jsxs)("div",{className:"mt-2 p-3 bg-muted rounded-md",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Record Details:"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[M.servicePerformed?.join(", ")," -"," ",new Date(M.date).toLocaleDateString()]})]})]})]}),(0,r.jsxs)(u.ck,{children:[(0,r.jsx)(u.Zr,{children:"Cancel"}),(0,r.jsx)(u.Rx,{onClick:E,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete Record"})]})]})]})]})]}),(0,r.jsxs)(o.Fc,{className:"border-green-200 bg-green-50",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)(o.TN,{className:"text-green-800",children:"Service record loaded successfully. All information is up to date."})]}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-5 w-5"}),"Service Details"]})}),(0,r.jsxs)(a.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Service Date"}),(0,r.jsx)("p",{className:"font-semibold",children:new Date(M.date).toLocaleDateString()})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Cost"}),(0,r.jsxs)("p",{className:"font-semibold",children:["$",(M.cost??0).toFixed(2)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,r.jsx)(w.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Odometer"}),(0,r.jsxs)("p",{className:"font-semibold",children:[M.odometer.toLocaleString()," miles"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg",children:[(0,r.jsx)(N.A,{className:"h-5 w-5 text-muted-foreground"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Record ID"}),(0,r.jsx)("p",{className:"font-semibold font-mono text-sm",children:M.id})]})]})]}),(0,r.jsx)(c.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Services Performed"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:M.servicePerformed.map((e,s)=>(0,r.jsx)(l.E,{variant:"outline",children:e},s))})]}),M.notes&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Notes"}),(0,r.jsx)("p",{className:"text-sm bg-muted/50 p-3 rounded-lg",children:M.notes})]})]})]})]})}),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(R,{vehicleId:M.vehicleId})})]})]}):null:(0,r.jsxs)("div",{className:"container mx-auto py-8 text-center text-gray-500",children:[(0,r.jsx)("p",{children:"No service record data available."}),(0,r.jsx)(d.$,{asChild:!0,className:"mt-4",children:(0,r.jsxs)(m(),{href:"/service-history",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"})," Back to Service History"]})})]})}},60368:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89009:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\service-records\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\service-records\\[id]\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,211,1658,8390,101,5825,6813],()=>t(45208));module.exports=r})();