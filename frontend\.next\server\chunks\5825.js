exports.id=5825,exports.ids=[5825,9603],exports.modules={658:(e,t,i)=>{"use strict";i.d(t,{gv:()=>l,j9:()=>a});var n=i(40463),s=i(23133),r=i(65931),c=i(89513);class o{static{this.instance=null}constructor(e={}){this.connectionState="disconnected",this.reconnectAttempts=0,this.socket=null,this.stateListeners=new Set,this.subscriptions=new Map,this.config={autoConnect:e.autoConnect??!0,reconnectAttempts:e.reconnectAttempts??5,reconnectDelay:e.reconnectDelay??1e3,timeout:e.timeout??1e4,url:e.url??process.env.NEXT_PUBLIC_WEBSOCKET_URL??(0,s.Qq)().wsUrl.replace("ws://","http://").replace("wss://","https://")},this.config.autoConnect&&this.connect(),this.setupTokenRefreshHandling()}static getInstance(e){return o.instance??=new o(e),o.instance}async connect(){if(this.socket?.connected)return void console.debug("WebSocket already connected");this.setConnectionState("connecting");try{let{data:{session:e},error:t}=await r.N.auth.getSession();t&&console.warn("Failed to get session for WebSocket connection:",t);let i={forceNew:!0,timeout:this.config.timeout,transports:["websocket","polling"],withCredentials:!0};if(e?.access_token){i.auth={token:e.access_token},console.debug("\uD83D\uDD10 WebSocket connecting with authentication token");let t=e.expires_at?1e3*e.expires_at:0,n=Date.now();t-n<=6e4&&console.warn("⚠️ WebSocket token expires soon, may need refresh")}else console.warn("⚠️ WebSocket connecting without authentication token - connection may fail");this.socket=(0,n.io)(this.config.url,i),this.setupEventHandlers()}catch(e){console.error("Failed to connect WebSocket:",e),this.setConnectionState("error"),this.scheduleReconnect()}}destroy(){this.disconnect(),this.subscriptions.clear(),this.stateListeners.clear(),o.instance=null}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionState("disconnected"),this.reconnectAttempts=0}emit(e,t,i){if(!this.socket?.connected)return void console.warn(`Cannot emit ${e}:${t} - WebSocket not connected`);this.socket.emit(t,i)}getConnectionState(){return this.connectionState}isConnected(){return"connected"===this.connectionState&&this.socket?.connected===!0}joinRoom(e){if(!this.socket?.connected)return void console.warn(`Cannot join room ${e} - WebSocket not connected`);this.socket.emit("join-room",e)}leaveRoom(e){this.socket?.connected&&this.socket.emit("leave-room",e)}onStateChange(e){return this.stateListeners.add(e),()=>{this.stateListeners.delete(e)}}subscribe(e,t,i){let n=`${e}:${t}`;return this.subscriptions.has(n)||this.subscriptions.set(n,new Set),this.subscriptions.get(n).add(i),this.socket?.connected&&t&&this.socket.on(t,i),()=>{let e=this.subscriptions.get(n);e&&(e.delete(i),0===e.size&&this.subscriptions.delete(n)),this.socket&&t&&this.socket.off(t,i)}}handleAuthenticationError(){let e=(0,c.Q)();console.log("\uD83D\uDD10 Handling WebSocket authentication error..."),this.socket&&(this.socket.disconnect(),this.socket=null),e.refreshNow().then(e=>{e?console.log("\uD83D\uDD04 Token refresh successful, retrying WebSocket connection"):(console.error("\uD83D\uDD04 Token refresh failed, scheduling normal reconnect"),this.scheduleReconnect())}).catch(e=>{console.error("\uD83D\uDD04 Token refresh error:",e),this.scheduleReconnect()})}resubscribeToEvents(){if(this.socket)for(let[e,t]of this.subscriptions){let[,i]=e.split(":");for(let e of t)i&&this.socket.on(i,e)}}scheduleReconnect(){if(this.reconnectAttempts>=this.config.reconnectAttempts){console.error("Max reconnection attempts reached"),this.setConnectionState("error");return}this.setConnectionState("reconnecting"),this.reconnectAttempts++,setTimeout(()=>{console.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`),this.connect()},this.config.reconnectDelay*Math.pow(2,this.reconnectAttempts-1))}setConnectionState(e){if(this.connectionState!==e)for(let t of(this.connectionState=e,this.stateListeners))t(e)}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.info("WebSocket connected"),this.setConnectionState("connected"),this.reconnectAttempts=0,this.resubscribeToEvents()}),this.socket.on("disconnect",e=>{console.warn("WebSocket disconnected:",e),this.setConnectionState("disconnected"),"io server disconnect"!==e&&this.scheduleReconnect()}),this.socket.on("connect_error",e=>{console.error("WebSocket connection error:",e),this.setConnectionState("error"),e.message?.includes("Authentication")||e.message?.includes("token")||e.message?.includes("No token provided")||e.message?.includes("Unauthorized")?(console.warn("\uD83D\uDD10 Authentication error detected, attempting token refresh"),this.handleAuthenticationError()):this.scheduleReconnect()}),this.socket.on("auth_error",e=>{console.error("\uD83D\uDD10 Server authentication error:",e),this.handleAuthenticationError()}),this.socket.on("token_refresh_required",()=>{console.warn("\uD83D\uDD04 Server requested token refresh"),this.handleAuthenticationError()}))}setupTokenRefreshHandling(){(0,c.Q)().subscribe((e,t)=>{switch(e){case"critical_refresh_failed":console.error("\uD83D\uDD04 Critical token refresh failure, disconnecting WebSocket"),this.disconnect(),this.setConnectionState("error");break;case"refresh_failed":console.error("\uD83D\uDD04 Token refresh failed, WebSocket may lose connection");break;case"refresh_success":console.log("\uD83D\uDD04 Token refreshed, reconnecting WebSocket with new token"),this.socket&&(this.socket.disconnect(),this.socket=null),setTimeout(()=>this.connect(),500)}})}}let a=e=>o.getInstance(e),l=()=>{let e=a();return{connectionState:e.getConnectionState(),isConnected:e.isConnected()}}},5991:()=>{},46349:(e,t,i)=>{"use strict";i.d(t,{GK:()=>o,ol:()=>a});var n=i(8693),s=i(43612),r=i(43210),c=i(658);function o(e,t,i,n){return l(e,t,{channel:"crud",events:[`${i}:created`,`${i}:updated`,`${i}:deleted`,`refresh:${i}`],fallbackInterval:3e4},n)}function a(e,t,i,n){return(0,c.j9)(),l(e,t,{channel:"reliability",events:[`${i}-update`,`${i}-created`,`${i}-resolved`],fallbackInterval:{alerts:3e4,"circuit-breakers":6e4,health:45e3,metrics:6e4}[i]},n)}function l(e,t,i,o){let{channel:a,enableFallback:l=!0,enableWebSocket:h=!0,events:u,fallbackInterval:d=3e4}=i,[g,p]=(0,r.useState)(!1);(0,c.j9)();let k=l&&(!h||!g),y={gcTime:6e5,queryFn:t,queryKey:e,refetchInterval:!!k&&d,refetchOnReconnect:!0,refetchOnWindowFocus:k,staleTime:3e4*!g,...o};return(0,n.jE)(),{...(0,s.I)(y),isUsingFallback:k,isWebSocketConnected:g}}},49603:(e,t,i)=>{"use strict";i.d(t,{cl:()=>k,delegationApiService:()=>S,employeeApiService:()=>f,reliabilityApiService:()=>v,taskApiService:()=>m,vehicleApiService:()=>y});var n=i(79772),s=i(44194),r=i(10212),c=i(77312);let o={fromApi:e=>e,toApi:e=>e};class a extends c.v{constructor(e,t){super(e,{cacheDuration:6e4,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/reliability",this.transformer=o}async getSystemHealth(){return this.executeWithInfrastructure("health:system",async()=>await this.apiClient.get("/health"))}async getDetailedHealth(){return this.executeWithInfrastructure("health:detailed",async()=>await this.apiClient.get("/health/detailed"))}async getDependencyHealth(){return this.executeWithInfrastructure("health:dependencies",async()=>await this.apiClient.get("/health/dependencies"))}async getCircuitBreakerStatus(){return this.executeWithInfrastructure("monitoring:circuit-breakers",async()=>{try{let e=await this.apiClient.get("/monitoring/circuit-breakers"),t=e?.circuitBreakers||[];return{circuitBreakers:t||[],summary:{total:t?.length||0,closed:t?.filter(e=>"CLOSED"===e.state).length||0,open:t?.filter(e=>"OPEN"===e.state).length||0,halfOpen:t?.filter(e=>"HALF_OPEN"===e.state).length||0}}}catch(e){return console.error("Failed to get circuit breaker status:",e),{circuitBreakers:[],summary:{total:0,closed:0,open:0,halfOpen:0}}}})}async getDeduplicationMetrics(){return this.executeWithInfrastructure("monitoring:deduplication",async()=>await this.apiClient.get("/monitoring/deduplication"))}async getMetrics(){return this.executeWithInfrastructure("metrics:system",async()=>await this.apiClient.get("/metrics",{headers:{Accept:"application/json"}}))}async getActiveAlerts(){return this.executeWithInfrastructure("alerts:active",async()=>{try{let e=await this.apiClient.get("/alerts");return e?.alerts||[]}catch(e){return console.error("Failed to get active alerts:",e),[]}})}async getAlertHistory(e=1,t=50){return this.executeWithInfrastructure(`alerts:history:${e}:${t}`,async()=>{let i=new URLSearchParams({page:e.toString(),limit:t.toString()});return await this.apiClient.get(`/alerts/history?${i.toString()}`)})}async getAlertStatistics(){return this.executeWithInfrastructure("alerts:statistics",async()=>{try{return await this.apiClient.get("/alerts/statistics")}catch(e){return console.error("Failed to get alert statistics:",e),{total:0,active:0,acknowledged:0,resolved:0,bySeverity:{low:0,medium:0,high:0,critical:0},averageResolutionTime:0,recentTrends:{last24Hours:0,last7Days:0,last30Days:0}}}})}async resolveAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let n=await this.apiClient.post(`/alerts/${e}/resolve`,{reason:t,resolvedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),n})}async acknowledgeAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let n=await this.apiClient.post(`/alerts/${e}/acknowledge`,{note:t,acknowledgedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),n})}async testAlerts(){return this.executeWithInfrastructure(null,async()=>{let e=await this.apiClient.post("/alerts/test");return{success:e?.status==="success",message:e?.message||"Test alert triggered",testAlertId:e?.data?.id}})}async getReliabilityDashboardData(){let[e,t,i,n,s,r]=await Promise.all([this.getSystemHealth(),this.getDetailedHealth(),this.getCircuitBreakerStatus(),this.getMetrics(),this.getActiveAlerts(),this.getAlertStatistics()]);return{systemHealth:e,detailedHealth:t,circuitBreakers:i,metrics:n,activeAlerts:s,alertStatistics:r}}async isSystemHealthy(){try{let e=await this.getSystemHealth();return"healthy"===e.status}catch(e){return!1}}async getCriticalAlertCount(){try{return(await this.getAlertStatistics()).bySeverity.critical}catch(e){return 0}}async getHealthTrends(e="24h"){return this.executeWithInfrastructure(`health:trends:${e}`,async()=>await this.apiClient.get(`/health/trends?timeframe=${e}`))}async getCircuitBreakerHistory(e="24h",t){return this.executeWithInfrastructure(`circuit-breakers:history:${e}:${t||"all"}`,async()=>{let i=new URLSearchParams({timeframe:e});return t&&i.append("breakerName",t),await this.apiClient.get(`/monitoring/circuit-breakers/history?${i.toString()}`)})}async getHttpRequestMetrics(){return this.executeWithInfrastructure("http:metrics",async()=>await this.apiClient.get("/monitoring/http-request-metrics"))}}var l=i(76783),h=i(30930),u=i(23133),d=i(8342);function g(){let e=(0,d.Sk)();if(!e)return null;try{return e()}catch(e){return console.error("❌ Factory: Error getting auth token from secure provider:",e),null}}class p{constructor(e){this.apiClient=new n.O({...e,getAuthToken:g})}getApiClient(){return this.apiClient}getDelegationService(){return this.delegationService||(this.delegationService=new s.y(this.apiClient)),this.delegationService}getEmployeeService(){return this.employeeService||(this.employeeService=new r.Q(this.apiClient)),this.employeeService}getReliabilityService(){return this.reliabilityService||(this.reliabilityService=new a(this.apiClient)),this.reliabilityService}getTaskService(){return this.taskService||(this.taskService=new l.D(this.apiClient)),this.taskService}getVehicleService(){return this.vehicleService||(this.vehicleService=new h.C(this.apiClient)),this.vehicleService}}let k=new p({baseURL:(0,u.Qq)().apiBaseUrl,headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4}),y=k.getVehicleService(),S=k.getDelegationService(),m=k.getTaskService(),f=k.getEmployeeService(),v=k.getReliabilityService()},70440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});var n=i(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80702:()=>{}};