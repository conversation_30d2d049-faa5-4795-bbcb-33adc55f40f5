(()=>{var e={};e.id=6043,e.ids=[6043],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12662:(e,t,s)=>{"use strict";s.d(t,{AppBreadcrumb:()=>u});var r=s(60687),i=s(85814),a=s.n(i),l=s(16189),d=s(43210),n=s.n(d),o=s(70640),c=s(22482);function u({className:e,homeHref:t="/",homeLabel:s="Dashboard",showContainer:i=!0}){let d=(0,l.usePathname)(),u=d?d.split("/").filter(Boolean):[],m=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let t={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return t[e]?t[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=u.map((e,t)=>{let s="/"+u.slice(0,t+1).join("/"),i=t===u.length-1,l=m(e);return(0,r.jsxs)(n().Fragment,{children:[(0,r.jsx)(o.BreadcrumbItem,{children:i?(0,r.jsx)(o.BreadcrumbPage,{className:"font-medium text-foreground",children:l}):(0,r.jsx)(o.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(a(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:l})})}),!i&&(0,r.jsx)(o.BreadcrumbSeparator,{})]},s)}),h=(0,r.jsx)(o.Breadcrumb,{className:(0,c.cn)("text-sm",e),children:(0,r.jsxs)(o.BreadcrumbList,{className:"flex-wrap",children:[(0,r.jsx)(o.BreadcrumbItem,{children:(0,r.jsx)(o.BreadcrumbLink,{asChild:!0,children:(0,r.jsx)(a(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:s})})}),u.length>0&&(0,r.jsx)(o.BreadcrumbSeparator,{}),p]})});return i?(0,r.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"flex items-center",children:h})}):h}},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>f,gC:()=>x,l6:()=>c,yv:()=>u});var r=s(60687),i=s(22670),a=s(61662),l=s(89743),d=s(58450),n=s(43210),o=s(22482);let c=i.bL;i.YJ;let u=i.WT,m=n.forwardRef(({children:e,className:t,...s},l)=>(0,r.jsxs)(i.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:l,...s,children:[e,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(a.A,{className:"size-4 opacity-50"})})]}));m.displayName=i.l9.displayName;let p=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:s,...t,children:(0,r.jsx)(l.A,{className:"size-4"})}));p.displayName=i.PP.displayName;let h=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),ref:s,...t,children:(0,r.jsx)(a.A,{className:"size-4"})}));h.displayName=i.wn.displayName;let x=n.forwardRef(({children:e,className:t,position:s="popper",...a},l)=>(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,ref:l,...a,children:[(0,r.jsx)(p,{}),(0,r.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,r.jsx)(h,{})]})}));x.displayName=i.UC.displayName,n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:s,...t})).displayName=i.JU.displayName;let f=n.memo(n.forwardRef(({children:e,className:t,...s},a)=>{let l=n.useCallback(e=>{"function"==typeof a?a(e):a&&(a.current=e)},[a]);return(0,r.jsxs)(i.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:l,...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(d.A,{className:"size-4"})})}),(0,r.jsx)(i.p4,{children:e})]})}));f.displayName=i.q7.displayName,n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),ref:s,...t})).displayName=i.wv.displayName},15146:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>K});var r=s(60687),i=s(75699),a=s(58261),l=s(14975),d=s(80489),n=s(35137),o=s(57207),c=s(26398),u=s(92876),m=s(15036),p=s(48206),h=s(36644),x=s(24920),f=s(3662),y=s(58595),g=s(71032),v=s(97025);let b=(0,s(82614).A)("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var j=s(85814),N=s.n(j),k=s(16189),w=s(43210),E=s(55925),A=s(12662),C=s(32584),D=s(96834),I=s(29523),q=s(44493),S=s(80013),$=s(52027),T=s(48041),R=s(15079),P=s(35950),z=s(3940),M=s(19599),Q=s(73227),F=s(22482),L=s(15795);let U=e=>{switch(e){case"Assigned":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";case"Cancelled":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";case"Completed":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"In_Progress":return"bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30";case"Pending":return"bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},O=e=>{switch(e){case"High":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";case"Low":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"Medium":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},_=(e,t=!1)=>{if(!e)return"N/A";try{return(0,i.GP)((0,a.H)(e),t?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch{return"Invalid Date"}};function K(){let e=(0,k.useParams)(),t=(0,k.useRouter)(),{showEntityDeleted:s,showEntityDeletionError:i,showEntityUpdated:a,showEntityUpdateError:b,showFormError:j,showFormSuccess:C}=(0,z.O_)("task"),L=e?.id,{data:K,error:V,isLoading:G,refetch:Z}=(0,Q.xo)(L),{data:J}=(0,M.nR)(),W=J?.filter(e=>"Active"===e.status)||[],{mutateAsync:Y}=(0,Q.AK)(),{mutateAsync:X}=(0,Q.K)(),[ee,et]=(0,w.useState)(""),es=async()=>{if(K?.id)try{await Y(K.id);let e={name:K.description.slice(0,30)+(K.description.length>30?"...":""),title:K.description.slice(0,30)+(K.description.length>30?"...":"")};s(e),t.push("/tasks")}catch(e){console.error("Error deleting task:",e),i(e.message||"Failed to delete task. Please try again.")}},er=async()=>{if(K?.id&&ee)try{await X({data:{driverEmployeeId:Number.parseInt(ee)},id:K.id});let e={name:`Task assigned to employee ID ${ee}`,title:`Task assigned to employee ID ${ee}`};a(e),Z(),et("")}catch(e){console.error("Error assigning task:",e),b(e.message||"Failed to assign task. Please try again.")}},ei=async e=>{if(K?.id){let t={};if(K.driverEmployeeId===e)t.driverEmployeeId=void 0;else if(K.staffEmployeeId===e)return void j("Cannot unassign the primary staff member directly. Please reassign.",{errorTitle:"Action Not Allowed"});else return void j("Employee not found in task assignments.");if(0===Object.keys(t).length)return void C({successDescription:"No changes to apply for unassignment.",successTitle:"Info"});try{await X({data:t,id:K.id}),a({name:"Employee unassigned from task",title:"Employee unassigned from task"}),Z()}catch(e){console.error("Error updating task assignment:",e),b(e.message||"Failed to unassign task. Please try again.")}}},ea=K?.status==="Completed"||K?.status==="Cancelled";return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(A.AppBreadcrumb,{}),(0,r.jsx)($.gO,{data:K,emptyComponent:(0,r.jsxs)("div",{className:"py-10 text-center",children:[(0,r.jsx)(T.z,{icon:l.A,title:"Task Not Found"}),(0,r.jsx)("p",{className:"mb-4",children:"The requested task could not be found."})]}),error:V?V.message:null,isLoading:G??!1,loadingComponent:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(T.z,{icon:d.A,title:"Loading Task..."}),(0,r.jsx)($.jt,{count:1,variant:"card"}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsx)($.jt,{className:"lg:col-span-2",count:1,variant:"card"}),(0,r.jsx)($.jt,{count:1,variant:"card"})]})]}),onRetry:Z,children:e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(T.z,{description:"Manage details and assignment for this task.",icon:d.A,title:e.description||"Task Details",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,r.jsx)(I.$,{asChild:!0,className:"gap-2",variant:"default",children:(0,r.jsxs)(N(),{href:`/tasks/${e.id}/edit`,children:[(0,r.jsx)(n.A,{className:"size-4"}),"Edit"]})}),(0,r.jsxs)(E.Lt,{children:[(0,r.jsx)(E.tv,{asChild:!0,children:(0,r.jsxs)(I.$,{className:"gap-2",variant:"destructive",children:[(0,r.jsx)(o.A,{className:"size-4"}),"Delete Task"]})}),(0,r.jsxs)(E.EO,{children:[(0,r.jsxs)(E.wd,{children:[(0,r.jsx)(E.r7,{children:"Are you sure?"}),(0,r.jsx)(E.$v,{children:"This action cannot be undone. This will permanently delete the task."})]}),(0,r.jsxs)(E.ck,{children:[(0,r.jsx)(E.Zr,{children:"Cancel"}),(0,r.jsx)(E.Rx,{className:"bg-destructive hover:bg-destructive/90",onClick:es,children:"Delete"})]})]})]})]})}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsxs)(q.Zp,{className:"shadow-sm lg:col-span-2",children:[(0,r.jsx)(q.aR,{className:"border-b",children:(0,r.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(q.ZB,{className:"text-2xl font-bold leading-tight",children:[e.description," "]})," "]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)(D.E,{className:(0,F.cn)("justify-center",U(e.status)),children:e.status}),(0,r.jsxs)(D.E,{className:(0,F.cn)("justify-center",O(e.priority)),variant:"outline",children:[e.priority," Priority"]})]})]})}),(0,r.jsxs)(q.Wu,{className:"space-y-6 p-6",children:[(0,r.jsx)(H,{icon:c.A,label:"Location",value:e.location}),(0,r.jsx)(H,{icon:u.A,label:"Start Date & Time",value:_(e.dateTime,!0)}),(0,r.jsx)(H,{icon:m.A,label:"Estimated Duration",value:`${e.estimatedDuration} minutes`}),e.deadline&&(0,r.jsx)(H,{icon:m.A,label:"Deadline",value:_(e.deadline,!1)}),e.requiredSkills&&e.requiredSkills.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required Skills"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.requiredSkills.map((e,t)=>(0,r.jsx)(D.E,{className:"text-xs",variant:"secondary",children:e},t))})]}),e.notes&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Notes"})]}),(0,r.jsx)("p",{className:"rounded-lg bg-muted/30 p-3 text-sm text-muted-foreground",children:e.notes})]}),e.vehicleId&&(0,r.jsx)(H,{icon:x.A,label:"Assigned Vehicle",children:(0,r.jsxs)("div",{className:"rounded-lg border bg-muted/30 p-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.vehicle?`${e.vehicle.make} ${e.vehicle.model}`:`Vehicle ID: ${e.vehicleId}`}),e.vehicle?.licensePlate&&(0,r.jsxs)("p",{className:"mt-1 text-xs text-muted-foreground",children:["License Plate: ",e.vehicle.licensePlate]}),e.vehicle?.year&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Year: ",e.vehicle.year]})]})}),e.subtasks&&e.subtasks.length>0&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(P.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"mb-3 flex items-center gap-2 text-lg font-semibold",children:[(0,r.jsx)(d.A,{className:"size-4"}),"Sub-Tasks"]}),(0,r.jsx)("ul",{className:"space-y-2",children:e.subtasks.map(e=>(0,r.jsxs)("li",{className:(0,F.cn)("flex items-center gap-2 text-sm",e.completed&&"line-through text-muted-foreground"),children:[e.completed?(0,r.jsx)(f.A,{className:"size-4 text-green-500"}):(0,r.jsx)("div",{className:"size-4 rounded-sm border"}),e.title]},e.id))})]})]})]}),(0,r.jsx)(q.wL,{className:"flex items-center justify-center border-t bg-muted/20",children:(0,r.jsxs)("div",{className:"flex w-full flex-col gap-2 text-xs text-muted-foreground sm:flex-row sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"font-medium",children:"Created:"}),(0,r.jsx)("span",{children:_(e.createdAt,!0)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:"font-medium",children:"Updated:"}),(0,r.jsx)("span",{children:_(e.updatedAt,!0)})]})]})})]}),(0,r.jsxs)(q.Zp,{className:"h-fit shadow-sm",children:[(0,r.jsx)(q.aR,{children:(0,r.jsxs)(q.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"size-5 text-primary"}),"Task Assignment"]})}),(0,r.jsxs)(q.Wu,{className:"space-y-4",children:[e.staffEmployeeId&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(y.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Staff Assignment"})]}),(0,r.jsx)(B,{employee:e.staffEmployee,employeeId:e.staffEmployeeId,isTaskCompleted:ea,onUnassign:()=>{e.staffEmployeeId&&ei(e.staffEmployeeId)}})]}),e.driverEmployeeId&&(0,r.jsxs)(r.Fragment,{children:[e.staffEmployeeId&&(0,r.jsx)(P.w,{className:"my-4"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Driver Assignment"})]}),(0,r.jsx)(B,{employee:e.driverEmployee,employeeId:e.driverEmployeeId,isTaskCompleted:ea,onUnassign:()=>{e.driverEmployeeId&&ei(e.driverEmployeeId)}})]})]}),!e.staffEmployeeId&&!e.driverEmployeeId&&(0,r.jsx)("div",{className:"py-4 text-center",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."})}),!ea&&!(e.staffEmployeeId||e.driverEmployeeId)&&(0,r.jsxs)("div",{className:"space-y-4 border-t pt-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Assign task to an employee:"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(S.J,{className:"text-sm font-medium",htmlFor:"assignee-select",children:"Select Employee"}),(0,r.jsxs)(R.l6,{onValueChange:et,value:ee,children:[(0,r.jsx)(R.bq,{children:(0,r.jsx)(R.yv,{placeholder:"Select an employee"})}),(0,r.jsx)(R.gC,{children:W.length>0?W.map(e=>(0,r.jsxs)(R.eb,{value:String(e.id),children:[e.fullName||e.name," (",e.position||e.role,")"]},e.id)):(0,r.jsx)("div",{className:"p-2 text-sm text-muted-foreground",children:"No available employees."})})]}),(0,r.jsxs)(I.$,{className:"w-full gap-2",disabled:!ee,onClick:er,children:[(0,r.jsx)(g.A,{className:"size-4"}),"Assign to Selected Employee"]})]})]}),"Completed"===e.status&&(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20",children:[(0,r.jsx)(f.A,{className:"size-4 text-green-600"}),(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-400",children:"Task completed."})]}),"Cancelled"===e.status&&(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20",children:[(0,r.jsx)(v.A,{className:"size-4 text-red-600"}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:"Task cancelled."})]})]})]})]})]})})]})}function B({employee:e,employeeId:t,isTaskCompleted:s,onUnassign:i}){if(!t&&0!==t)return(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."});if(!e)return(0,r.jsxs)("p",{className:"text-sm text-destructive",children:["Could not load assignee (ID: ",t,")."]});let a=(0,L.DV)(e);return(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border bg-muted/30 p-2",children:[(0,r.jsx)(C.eu,{className:"size-8",children:(0,r.jsx)(C.q5,{className:"text-xs font-medium",children:a.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(N(),{className:"text-sm font-medium hover:text-primary",href:`/employees/${e.employeeId}`,children:a}),(0,r.jsx)("span",{className:"text-xs capitalize text-muted-foreground",children:e.position||e.role})]}),!s&&i&&t&&(0,r.jsx)(I.$,{className:"size-6 p-0 text-muted-foreground hover:text-destructive",onClick:()=>i(t),size:"sm",variant:"ghost",children:(0,r.jsx)(b,{className:"size-3"})})]})})}function H({children:e,className:t,icon:s,label:i,value:a}){return void 0!==a||e?(0,r.jsxs)("div",{className:(0,F.cn)("flex items-start gap-3",t),children:[(0,r.jsx)(s,{className:"mt-1 size-4 shrink-0 text-muted-foreground"}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:i}),null!=a&&(0,r.jsx)("p",{className:"text-base font-medium text-foreground",children:a}),e]})]}):null}},15795:(e,t,s)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function i(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let t=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(t.toLowerCase())||t.includes("_")){let e=t.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return t}if(e.role){let t=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${t} (Role)`}return"Unknown Employee"}function a(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function l(e){return e.replaceAll("_"," ")}s.d(t,{DV:()=>i,fZ:()=>r,s:()=>a,vq:()=>l})},18164:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(65239),i=s(48088),a=s(88170),l=s.n(a),d=s(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);s.d(t,n);let o={children:["",{children:["tasks",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94663)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/tasks/[id]/page",pathname:"/tasks/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26398:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35137:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},48041:(e,t,s)=>{"use strict";s.d(t,{z:()=>i});var r=s(60687);function i({children:e,description:t,icon:s,title:i}){return(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[s&&(0,r.jsx)(s,{className:"size-8 text-primary"}),(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:i})]}),t&&(0,r.jsx)("p",{className:"mt-1 text-muted-foreground",children:t})]}),e&&(0,r.jsx)("div",{className:"flex items-center gap-2",children:e})]})}s(43210)},49278:(e,t,s)=>{"use strict";s.d(t,{G7:()=>m,Gb:()=>n,JP:()=>o,Ok:()=>c,Qu:()=>u,iw:()=>d,oz:()=>h,z0:()=>p});var r=s(3389);class i{show(e){return(0,r.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends i{constructor(e){super(),this.config=e}entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}}class l extends i{serviceRecordCreated(e,t){return this.success("Service Record Added",`${t} service for "${e}" has been successfully logged.`)}serviceRecordUpdated(e,t){return this.success("Service Record Updated",`${t} service for "${e}" has been updated.`)}serviceRecordDeleted(e,t){return this.success("Service Record Deleted",`${t} service record for "${e}" has been permanently removed.`)}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function d(e){return new a(e)}function n(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:`${e} Created`,description:t=>`The ${e.toLowerCase()} "${t}" has been successfully created.`},updated:{title:`${e} Updated Successfully`,description:e=>`${e} has been updated.`},deleted:{title:`${e} Deleted Successfully`,description:e=>`${e} has been permanently removed.`},creationError:{title:`Failed to Create ${e}`,description:t=>t||`An unexpected error occurred while creating the ${e.toLowerCase()}.`},updateError:{title:"Update Failed",description:t=>t||`An unexpected error occurred while updating the ${e.toLowerCase()}.`},deletionError:{title:`Failed to Delete ${e}`,description:t=>t||`An unexpected error occurred while deleting the ${e.toLowerCase()}.`}}})}let o=new i,c=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>`The employee "${e}" has been successfully created.`},updated:{title:"Employee Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Employee Deleted Successfully",description:e=>`${e} has been permanently removed from the system.`},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>`The delegation "${e}" has been successfully created.`},updated:{title:"Delegation Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Delegation Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),m=new a({entityName:"Vehicle",getDisplayName:e=>`${e.make} ${e.model}`,messages:{created:{title:"Vehicle Added",description:e=>`The vehicle "${e}" has been successfully created.`},updated:{title:"Vehicle Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Vehicle Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),p=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>`The task "${e}" has been successfully created.`},updated:{title:"Task Updated Successfully",description:e=>`${e} has been updated.`},deleted:{title:"Task Deleted Successfully",description:e=>`${e} has been permanently removed.`},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new l},51134:(e,t,s)=>{Promise.resolve().then(s.bind(s,15146))},52027:(e,t,s)=>{"use strict";s.d(t,{gO:()=>m,jt:()=>f,pp:()=>p});var r=s(60687),i=s(72963),a=s(11516);s(43210);var l=s(68752),d=s(91821),n=s(85726),o=s(22482);let c={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},u={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function m({children:e,className:t,data:s,emptyComponent:i,error:a,errorComponent:l,isLoading:d,loadingComponent:n,onRetry:c}){return d?n||(0,r.jsx)(x,{...t&&{className:t},text:"Loading..."}):a?l||(0,r.jsx)(h,{...t&&{className:t},message:a,...c&&{onRetry:c}}):!s||Array.isArray(s)&&0===s.length?i||(0,r.jsx)("div",{className:(0,o.cn)("text-center py-8",t),children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{className:t,children:e(s)})}function p({className:e,description:t,icon:s,primaryAction:i,secondaryAction:a,title:d}){return(0,r.jsxs)("div",{className:(0,o.cn)("space-y-6 text-center py-12",e),children:[s&&(0,r.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,r.jsx)(s,{className:"h-10 w-10 text-muted-foreground"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:d}),t&&(0,r.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:t})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[i&&(0,r.jsx)(l.r,{actionType:"primary",asChild:!!i.href,icon:i.icon,onClick:i.onClick,children:i.href?(0,r.jsx)("a",{href:i.href,children:i.label}):i.label}),a&&(0,r.jsx)(l.r,{actionType:"tertiary",asChild:!!a.href,icon:a.icon,onClick:a.onClick,children:a.href?(0,r.jsx)("a",{href:a.href,children:a.label}):a.label})]})]})}function h({className:e,message:t,onRetry:s}){return(0,r.jsxs)(d.Fc,{className:(0,o.cn)("my-4",e),variant:"destructive",children:[(0,r.jsx)(i.A,{className:"size-4"}),(0,r.jsx)(d.XL,{children:"Error"}),(0,r.jsx)(d.TN,{children:(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:t}),s&&(0,r.jsx)(l.r,{actionType:"tertiary",icon:(0,r.jsx)(a.A,{className:"size-4"}),onClick:s,size:"sm",children:"Try Again"})]})})]})}function x({className:e,fullPage:t=!1,size:s="md",text:i}){return(0,r.jsx)("div",{className:(0,o.cn)("flex items-center justify-center",t&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",e),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(a.A,{className:(0,o.cn)("animate-spin text-primary",c[s])}),i&&(0,r.jsx)("span",{className:(0,o.cn)("mt-2 text-muted-foreground",u[s]),children:i})]})})}function f({className:e,count:t=1,testId:s="loading-skeleton",variant:i="default"}){return"card"===i?(0,r.jsx)("div",{className:(0,o.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",e),"data-testid":s,children:Array(t).fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,r.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsx)(n.E,{className:"mb-1 h-7 w-3/4"}),(0,r.jsx)(n.E,{className:"mb-3 h-4 w-1/2"}),(0,r.jsx)(n.E,{className:"my-3 h-px w-full"}),(0,r.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.E,{className:"mr-2.5 size-5 rounded-full"}),(0,r.jsx)(n.E,{className:"h-5 w-2/3"})]},t))})]})]},t))}):"table"===i?(0,r.jsxs)("div",{className:(0,o.cn)("space-y-3",e),"data-testid":s,children:[(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,t)=>(0,r.jsx)(n.E,{className:"h-8 flex-1"},t))}),Array(t).fill(0).map((e,t)=>(0,r.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,t)=>(0,r.jsx)(n.E,{className:"h-6 flex-1"},t))},t))]}):"list"===i?(0,r.jsx)("div",{className:(0,o.cn)("space-y-3",e),"data-testid":s,children:Array(t).fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(n.E,{className:"size-12 rounded-full"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)(n.E,{className:"h-4 w-1/3"}),(0,r.jsx)(n.E,{className:"h-4 w-full"})]})]},t))}):"stats"===i?(0,r.jsx)("div",{className:(0,o.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",e),"data-testid":s,children:Array(t).fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(n.E,{className:"h-5 w-1/3"}),(0,r.jsx)(n.E,{className:"size-5 rounded-full"})]}),(0,r.jsx)(n.E,{className:"mt-3 h-8 w-1/2"}),(0,r.jsx)(n.E,{className:"mt-2 h-4 w-2/3"})]},t))}):(0,r.jsx)("div",{className:(0,o.cn)("space-y-2",e),"data-testid":s,children:Array(t).fill(0).map((e,t)=>(0,r.jsx)(n.E,{className:"h-5 w-full"},t))})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55862:(e,t,s)=>{Promise.resolve().then(s.bind(s,94663))},57207:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68752:(e,t,s)=>{"use strict";s.d(t,{r:()=>o});var r=s(60687),i=s(11516),a=s(43210),l=s.n(a),d=s(29523),n=s(22482);let o=l().forwardRef(({actionType:e="primary",asChild:t=!1,children:s,className:a,disabled:l,icon:o,isLoading:c=!1,loadingText:u,...m},p)=>{let{className:h,variant:x}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,r.jsx)(d.$,{asChild:t,className:(0,n.cn)(h,a),disabled:c||l,ref:p,variant:x,...m,children:c?(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,r.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}),u||s]}):(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",o&&(0,r.jsx)("span",{className:"mr-2",children:o}),s]})})});o.displayName="ActionButton"},69795:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},73227:(e,t,s)=>{"use strict";s.d(t,{ZY:()=>b,AK:()=>N,b7:()=>g,xo:()=>v,si:()=>y,K:()=>j});var r=s(93425),i=s(8693),a=s(54050),l=s(43210),d=s(46349),n=s(49603),o=s(83144),c=s(57930);let u={all:["tasks"],detail:e=>["tasks",e]},m=e=>({enabled:!!e,queryFn:()=>n.taskApiService.getById(e),queryKey:u.detail(e),staleTime:3e5}),p=()=>({queryFn:()=>n.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),h=()=>({queryFn:()=>n.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),x=e=>[m(e),p(),h()];var f=s(38118);let y=e=>(0,d.GK)([...u.all],async()=>(await n.taskApiService.getAll()).data,"task",{staleTime:0,...e}),g=e=>(0,d.GK)([...u.detail(e)],async()=>await n.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),v=e=>{let[t,s,i]=(0,r.E)({queries:x(e)}),a=(0,l.useMemo)(()=>{if(t?.data&&s?.data&&i?.data)try{let e=c.J.fromApi(t.data),r=Array.isArray(s.data)?s.data:[],a=Array.isArray(i.data)?i.data:[];return(0,o.R)(e,r,a)}catch(e){throw console.error("Error enriching task data:",e),e}},[t?.data,s?.data,i?.data]),d=(0,l.useCallback)(()=>{t?.refetch(),s?.refetch(),i?.refetch()},[t?.refetch,s?.refetch,i?.refetch]);return{data:a,error:t?.error||s?.error||i?.error,isError:t?.isError||s?.isError||i?.isError,isLoading:t?.isLoading||s?.isLoading||i?.isLoading,isPending:t?.isPending||s?.isPending||i?.isPending,refetch:d}},b=()=>{let e=(0,i.jE)();return(0,a.n)({mutationFn:async e=>{let t=c.J.toCreateRequest(e);return await n.taskApiService.create(t)},onError:(t,s,r)=>{r?.previousTasks&&e.setQueryData(u.all,r.previousTasks),console.error("Failed to create task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:u.all});let s=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>{let s="optimistic-"+Date.now().toString(),r=new Date().toISOString();return[...e,{createdAt:r,dateTime:t.dateTime??null,deadline:t.deadline??null,description:t.description,driverEmployee:null,driverEmployeeId:t.driverEmployeeId??null,estimatedDuration:t.estimatedDuration??null,id:s,location:t.location??null,notes:t.notes??null,priority:t.priority,requiredSkills:t.requiredSkills??null,staffEmployee:null,staffEmployeeId:t.staffEmployeeId??null,status:t.status||"Pending",subtasks:t.subtasks?.map(e=>({completed:e.completed||!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:s,title:e.title}))||[],updatedAt:r,vehicle:null,vehicleId:t.vehicleId??null}]}),{previousTasks:s}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})},j=()=>{let e=(0,i.jE)();return(0,a.n)({mutationFn:async({data:e,id:t})=>{let s=c.J.toUpdateRequest(e);return await n.taskApiService.update(t,s)},onError:(t,s,r)=>{r?.previousTask&&e.setQueryData(u.detail(s.id),r.previousTask),r?.previousTasksList&&e.setQueryData(u.all,r.previousTasksList),console.error("Failed to update task:",t)},onMutate:async({data:t,id:s})=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(s)});let r=e.getQueryData(u.detail(s)),i=e.getQueryData(u.all);return e.setQueryData(u.detail(s),e=>{if(!e)return e;let r=new Date().toISOString();return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,f.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,f.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,f.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:s,title:e.title}))||e.subtasks||[],updatedAt:r,vehicleId:(0,f.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}),e.setQueryData(u.all,(e=[])=>e.map(e=>{if(e.id===s){let r=new Date().toISOString(),i=t.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:s,title:e.title}))||e.subtasks||[];return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,f.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:t.description??e.description,driverEmployeeId:(0,f.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,f.d$)(void 0!==t.notes?t.notes:e.notes),priority:t.priority??e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:t.status??e.status,subtasks:i,updatedAt:r,vehicleId:(0,f.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}return e})),{previousTask:r,previousTasksList:i}},onSettled:(t,s,r)=>{e.invalidateQueries({queryKey:u.detail(r.id)}),e.invalidateQueries({queryKey:u.all})}})},N=()=>{let e=(0,i.jE)();return(0,a.n)({mutationFn:async e=>(await n.taskApiService.delete(e),e),onError:(t,s,r)=>{r?.previousTasksList&&e.setQueryData(u.all,r.previousTasksList),console.error("Failed to delete task:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(t)});let s=e.getQueryData(u.all);return e.setQueryData(u.all,(e=[])=>e.filter(e=>e.id!==t)),e.removeQueries({queryKey:u.detail(t)}),{previousTasksList:s}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83144:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});class r{static enrich(e,t,s){let{employeeMap:r,vehicleMap:i}=this.createLookupMaps(t,s),a=this.enrichStaffEmployee(e,r);return a=this.enrichDriverEmployee(a,r),a=this.enrichVehicle(a,i)}static createLookupMaps(e,t){let s=Array.isArray(e)?e:[],r=Array.isArray(t)?t:[];return{employeeMap:new Map(s.map(e=>[e.id,e])),vehicleMap:new Map(r.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,t){if(!e.driverEmployeeId)return e;let s=e.driverEmployee??t.get(e.driverEmployeeId)??null;return{...e,driverEmployee:s}}static enrichStaffEmployee(e,t){if(!e.staffEmployeeId)return e;let s=e.staffEmployee??t.get(e.staffEmployeeId)??null;return{...e,staffEmployee:s}}static enrichVehicle(e,t){if(!e.vehicleId)return e;let s=e.vehicle??t.get(e.vehicleId)??null;return{...e,vehicle:s}}}let i=(e,t,s)=>r.enrich(e,t,s)},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},92876:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},93425:(e,t,s)=>{"use strict";s.d(t,{E:()=>x});var r=s(43210),i=s(33465),a=s(5563),l=s(35536),d=s(31212);function n(e,t){let s=new Set(t);return e.filter(e=>!s.has(e))}var o=class extends l.Q{#e;#t;#s;#r;#i;#a;#l;#d;#n=[];constructor(e,t,s){super(),this.#e=e,this.#r=s,this.#s=[],this.#i=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#i.forEach(e=>{e.subscribe(t=>{this.#o(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#i.forEach(e=>{e.destroy()})}setQueries(e,t){this.#s=e,this.#r=t,i.jG.batch(()=>{let e=this.#i,t=this.#c(this.#s);this.#n=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let s=t.map(e=>e.observer),r=s.map(e=>e.getCurrentResult()),i=s.some((t,s)=>t!==e[s]);(e.length!==s.length||i)&&(this.#i=s,this.#t=r,this.hasListeners()&&(n(e,s).forEach(e=>{e.destroy()}),n(s,e).forEach(e=>{e.subscribe(t=>{this.#o(e,t)})}),this.#u()))})}getCurrentResult(){return this.#t}getQueries(){return this.#i.map(e=>e.getCurrentQuery())}getObservers(){return this.#i}getOptimisticResult(e,t){let s=this.#c(e),r=s.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[r,e=>this.#m(e??r,t),()=>this.#p(r,s)]}#p(e,t){return t.map((s,r)=>{let i=e[r];return s.defaultedQueryOptions.notifyOnChangeProps?i:s.observer.trackResult(i,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#m(e,t){return t?(this.#a&&this.#t===this.#d&&t===this.#l||(this.#l=t,this.#d=this.#t,this.#a=(0,d.BH)(this.#a,t(e))),this.#a):e}#c(e){let t=new Map(this.#i.map(e=>[e.options.queryHash,e])),s=[];return e.forEach(e=>{let r=this.#e.defaultQueryOptions(e),i=t.get(r.queryHash);i?s.push({defaultedQueryOptions:r,observer:i}):s.push({defaultedQueryOptions:r,observer:new a.$(this.#e,r)})}),s}#o(e,t){let s=this.#i.indexOf(e);-1!==s&&(this.#t=function(e,t,s){let r=e.slice(0);return r[t]=s,r}(this.#t,s,t),this.#u())}#u(){if(this.hasListeners()){let e=this.#a,t=this.#p(this.#t,this.#n);e!==this.#m(t,this.#r?.combine)&&i.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},c=s(8693),u=s(24903),m=s(18228),p=s(16142),h=s(76935);function x({queries:e,...t},s){let l=(0,c.jE)(s),n=(0,u.w)(),x=(0,m.h)(),f=r.useMemo(()=>e.map(e=>{let t=l.defaultQueryOptions(e);return t._optimisticResults=n?"isRestoring":"optimistic",t}),[e,l,n]);f.forEach(e=>{(0,h.jv)(e),(0,p.LJ)(e,x)}),(0,p.wZ)(x);let[y]=r.useState(()=>new o(l,f,t)),[g,v,b]=y.getOptimisticResult(f,t.combine),j=!n&&!1!==t.subscribed;r.useSyncExternalStore(r.useCallback(e=>j?y.subscribe(i.jG.batchCalls(e)):d.lQ,[y,j]),()=>y.getCurrentResult(),()=>y.getCurrentResult()),r.useEffect(()=>{y.setQueries(f,t)},[f,t,y]);let N=g.some((e,t)=>(0,h.EU)(f[t],e))?g.flatMap((e,t)=>{let s=f[t];if(s){let t=new a.$(l,s);if((0,h.EU)(s,e))return(0,h.iL)(s,t,x);(0,h.nE)(e,n)&&(0,h.iL)(s,t,x)}return[]}):[];if(N.length>0)throw Promise.all(N);let k=g.find((e,t)=>{let s=f[t];return s&&(0,p.$1)({result:e,errorResetBoundary:x,throwOnError:s.throwOnError,query:l.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(k?.error)throw k.error;return v(b())}},94663:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\tasks\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\[id]\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,211,1658,8390,2670,101,5825,9599,3224],()=>s(18164));module.exports=r})();