/**
 * @file Factory for creating and managing API service instances.
 * @module api/services/apiServiceFactory
 */

import { ApiClient } from '../core/apiClient';
import { DelegationApiService } from './domain/delegationApi';
import { EmployeeApiService } from './domain/employeeApi';
import { ReliabilityApiService } from './domain/reliabilityApi';
import { TaskApiService } from './domain/taskApi';
import { VehicleApiService } from './domain/vehicleApi';
import { getEnvironmentConfig } from '../../config/environment';
// Import secure auth token provider
import { getSecureAuthTokenProvider } from '../index';

/**
 * Get the current auth token from the secure provider
 * Uses the single source of truth for authentication tokens
 */
function getSecureAuthToken(): string | null {
  const provider = getSecureAuthTokenProvider();
  if (!provider) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');
    }
    return null;
  }

  try {
    return provider();
  } catch (error) {
    console.error(
      '❌ Factory: Error getting auth token from secure provider:',
      error
    );
    return null;
  }
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use setSecureAuthTokenProvider from main API module instead
 */
export function setFactoryAuthTokenProvider(
  provider: () => string | null
): void {
  console.warn(
    '⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.'
  );
  // This function is now a no-op since we use the secure provider
  // The warning guides developers to use the correct function
}

/**
 * Configuration for the API service factory.
 */
export interface ApiServiceFactoryConfig {
  authToken?: string;
  baseURL: string;
  headers?: Record<string, string>;
  retryAttempts?: number;
  timeout?: number;
}

/**
 * Factory class for creating and managing API service instances.
 * Provides a centralized way to configure and access all API services.
 */
export class ApiServiceFactory {
  private readonly apiClient: ApiClient;
  private delegationService?: DelegationApiService;
  private employeeService?: EmployeeApiService;
  private reliabilityService?: ReliabilityApiService;
  private taskService?: TaskApiService;
  private vehicleService?: VehicleApiService;

  /**
   * Creates an instance of ApiServiceFactory.
   * @param config - Configuration for the API services.
   */
  constructor(config: ApiServiceFactoryConfig) {
    this.apiClient = new ApiClient({
      ...config,
      getAuthToken: getSecureAuthToken, // Use consistent secure naming
    });
  }

  /**
   * Gets the underlying ApiClient instance.
   * @returns The ApiClient instance.
   */
  public getApiClient(): ApiClient {
    return this.apiClient;
  }

  /**
   * Gets the Delegation API service instance.
   * @returns The DelegationApiService instance.
   */
  public getDelegationService(): DelegationApiService {
    if (!this.delegationService) {
      this.delegationService = new DelegationApiService(this.apiClient);
    }
    return this.delegationService;
  }

  /**
   * Gets the Employee API service instance.
   * @returns The EmployeeApiService instance.
   */
  public getEmployeeService(): EmployeeApiService {
    if (!this.employeeService) {
      this.employeeService = new EmployeeApiService(this.apiClient);
    }
    return this.employeeService;
  }

  /**
   * Gets the Reliability API service instance.
   * @returns The ReliabilityApiService instance.
   */
  public getReliabilityService(): ReliabilityApiService {
    if (!this.reliabilityService) {
      this.reliabilityService = new ReliabilityApiService(this.apiClient);
    }
    return this.reliabilityService;
  }

  /**
   * Gets the Task API service instance.
   * @returns The TaskApiService instance.
   */
  public getTaskService(): TaskApiService {
    if (!this.taskService) {
      this.taskService = new TaskApiService(this.apiClient);
    }
    return this.taskService;
  }

  /**
   * Gets the Vehicle API service instance.
   * @returns The VehicleApiService instance.
   */
  public getVehicleService(): VehicleApiService {
    if (!this.vehicleService) {
      this.vehicleService = new VehicleApiService(this.apiClient);
    }
    return this.vehicleService;
  }
}

// Create a default factory instance for the application with environment-aware configuration
const envConfig = getEnvironmentConfig();
const defaultConfig: ApiServiceFactoryConfig = {
  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration
  headers: {
    'Content-Type': 'application/json',
  },
  retryAttempts: 3,
  timeout: 10_000,
};

export const apiServiceFactory = new ApiServiceFactory(defaultConfig);

// Export individual service instances for convenience
export const vehicleApiService = apiServiceFactory.getVehicleService();
export const delegationApiService = apiServiceFactory.getDelegationService();
export const taskApiService = apiServiceFactory.getTaskService();
export const employeeApiService = apiServiceFactory.getEmployeeService();
export const reliabilityApiService = apiServiceFactory.getReliabilityService();
