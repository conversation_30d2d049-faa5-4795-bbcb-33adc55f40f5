// Debug script to check authentication state
// Run this in browser console on the User Management page

console.log('=== AUTHENTICATION DEBUG ===');

// Check if we're on the right page
console.log('Current URL:', window.location.href);

// Check if auth context is available
const authContext = window.React?.useContext || null;
console.log('React context available:', !!authContext);

// Check global auth token provider
const globalProvider = window.getGlobalAuthTokenProvider?.();
console.log('Global auth token provider:', !!globalProvider);

if (globalProvider) {
  const token = globalProvider();
  console.log('Current token available:', !!token);
  if (token) {
    console.log('Token length:', token.length);
    console.log('Token starts with:', token.substring(0, 20) + '...');
  }
}

// Check localStorage for Supabase session
const supabaseKeys = Object.keys(localStorage).filter(key => 
  key.includes('supabase') || key.includes('sb-')
);
console.log('Supabase localStorage keys:', supabaseKeys);

supabaseKeys.forEach(key => {
  try {
    const value = localStorage.getItem(key);
    if (value) {
      const parsed = JSON.parse(value);
      console.log(`${key}:`, {
        hasAccessToken: !!parsed.access_token,
        hasRefreshToken: !!parsed.refresh_token,
        expiresAt: parsed.expires_at,
        user: parsed.user?.email || 'no user'
      });
    }
  } catch (e) {
    console.log(`${key}: (not JSON)`, localStorage.getItem(key)?.substring(0, 50));
  }
});

// Check cookies
const cookies = document.cookie.split(';').map(c => c.trim());
const authCookies = cookies.filter(c => 
  c.includes('sb-') || c.includes('auth') || c.includes('token')
);
console.log('Auth-related cookies:', authCookies);

// Try to make a test API call
console.log('Testing API call...');
fetch('/api/admin/users', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('API Response status:', response.status);
  console.log('API Response headers:', Object.fromEntries(response.headers.entries()));
  return response.text();
})
.then(text => {
  console.log('API Response body:', text);
})
.catch(error => {
  console.log('API Error:', error);
});

console.log('=== END DEBUG ===');
