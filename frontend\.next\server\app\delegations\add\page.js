(()=>{var e={};e.id=5534,e.ids=[5534],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,t,r)=>{"use strict";r.d(t,{O_:()=>i,t6:()=>n});var s=r(43210),o=r(49278);function n(){let e=(0,s.useCallback)((e,t)=>o.JP.success(e,t),[]),t=(0,s.useCallback)((e,t)=>o.JP.error(e,t),[]),r=(0,s.useCallback)((e,t)=>o.JP.info(e,t),[]),n=(0,s.useCallback)(t=>e(t?.successTitle||"Success",t?.successDescription||"Operation completed successfully"),[e]),i=(0,s.useCallback)((e,r)=>{let s=e instanceof Error?e.message:e;return t(r?.errorTitle||"Error",r?.errorDescription||s||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:n,showFormError:i}}function i(e){let t;switch(e){case"employee":t=r(49278).Ok;break;case"vehicle":t=r(49278).G7;break;case"task":t=r(49278).z0;break;case"delegation":t=r(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,t){let{showFormSuccess:r,showFormError:i}=n(),a=t||(e?(0,o.iw)(e):null),d=(0,s.useCallback)(e=>a?a.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[a,r]),l=(0,s.useCallback)(e=>a?a.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[a,r]),u=(0,s.useCallback)(e=>a?a.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[a,r]),c=(0,s.useCallback)(e=>{if(a){let t=e instanceof Error?e.message:e;return a.entityCreationError(t)}return i(e,{errorTitle:"Creation Failed"})},[a,i]);return{showEntityCreated:d,showEntityUpdated:l,showEntityDeleted:u,showEntityCreationError:c,showEntityUpdateError:(0,s.useCallback)(e=>{if(a){let t=e instanceof Error?e.message:e;return a.entityUpdateError(t)}return i(e,{errorTitle:"Update Failed"})},[a,i]),showEntityDeletionError:(0,s.useCallback)(e=>{if(a){let t=e instanceof Error?e.message:e;return a.entityDeletionError(t)}return i(e,{errorTitle:"Deletion Failed"})},[a,i]),showFormSuccess:r,showFormError:i}}(void 0,t)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27040:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\delegations\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\delegations\\add\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41290:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),o=r(33886),n=r(16189),i=r(51356),a=r(48041),d=r(3940),l=r(63502);function u(){let e=(0,n.useRouter)(),{showEntityCreated:t,showEntityCreationError:r}=(0,d.O_)("delegation"),{error:u,isPending:c,mutateAsync:p}=(0,l.er)(),m=async s=>{let o={...s,delegates:s.delegates.map(e=>({name:e.name,notes:e.notes??"",title:e.title})),drivers:s.driverEmployeeIds.map(e=>({employeeId:e})),durationFrom:new Date(s.durationFrom).toISOString(),durationTo:new Date(s.durationTo).toISOString(),escorts:s.escortEmployeeIds.map(e=>({employeeId:e})),notes:s.notes??"",status:s.status.replace(" ","_"),vehicles:s.vehicleIds.map(e=>({assignedDate:new Date(s.durationFrom).toISOString(),returnDate:new Date(s.durationTo).toISOString(),vehicleId:e}))};try{await p(o);let r={event:s.eventName,location:s.location};t(r),e.push("/delegations")}catch(t){console.error("Error adding delegation:",t);let e="Failed to add delegation. Please try again.";t instanceof Error?e=t.message:u instanceof Error&&(e=u.message),r(e)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(a.z,{description:"Enter the details for the new delegation or event.",icon:o.A,title:"Add New Delegation"}),u&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error: ",u.message]}),(0,s.jsx)(i.GK,{isEditing:!1,onSubmit:m})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66412:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["delegations",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27040)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\delegations\\add\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Projects\\WorkHub\\frontend\\src\\app\\delegations\\add\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/delegations/add/page",pathname:"/delegations/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},71973:(e,t,r)=>{Promise.resolve().then(r.bind(r,27040))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85125:(e,t,r)=>{Promise.resolve().then(r.bind(r,41290))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,211,1658,8390,2670,9275,6013,6362,7113,417,101,5825,9599,3502,6413,3089,9004],()=>r(66412));module.exports=s})();