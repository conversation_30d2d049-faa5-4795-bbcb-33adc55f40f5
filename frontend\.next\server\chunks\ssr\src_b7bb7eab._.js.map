{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/breadcrumb.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx <module evaluation>\",\n    \"Breadcrumb\",\n);\nexport const BreadcrumbEllipsis = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbEllipsis() from the server but BreadcrumbEllipsis is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx <module evaluation>\",\n    \"BreadcrumbEllipsis\",\n);\nexport const BreadcrumbItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>() from the server but BreadcrumbItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx <module evaluation>\",\n    \"BreadcrumbItem\",\n);\nexport const BreadcrumbLink = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbLink() from the server but BreadcrumbLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx <module evaluation>\",\n    \"BreadcrumbLink\",\n);\nexport const BreadcrumbList = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbList() from the server but BreadcrumbList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx <module evaluation>\",\n    \"BreadcrumbList\",\n);\nexport const BreadcrumbPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbPage() from the server but BreadcrumbPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx <module evaluation>\",\n    \"BreadcrumbPage\",\n);\nexport const BreadcrumbSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbSeparator() from the server but BreadcrumbSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx <module evaluation>\",\n    \"BreadcrumbSeparator\",\n);\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,kEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,kEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kEACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,kEACA", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/breadcrumb.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Breadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx\",\n    \"Breadcrumb\",\n);\nexport const BreadcrumbEllipsis = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbEllipsis() from the server but BreadcrumbEllipsis is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx\",\n    \"BreadcrumbEllipsis\",\n);\nexport const BreadcrumbItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call B<PERSON><PERSON>rumb<PERSON>tem() from the server but <PERSON><PERSON>crumbItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx\",\n    \"BreadcrumbItem\",\n);\nexport const BreadcrumbLink = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbLink() from the server but BreadcrumbLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx\",\n    \"BreadcrumbLink\",\n);\nexport const BreadcrumbList = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbList() from the server but BreadcrumbList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx\",\n    \"BreadcrumbList\",\n);\nexport const BreadcrumbPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbPage() from the server but BreadcrumbPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx\",\n    \"BreadcrumbPage\",\n);\nexport const BreadcrumbSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call BreadcrumbSeparator() from the server but BreadcrumbSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/breadcrumb.tsx\",\n    \"BreadcrumbSeparator\",\n);\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8CACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8CACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8CACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/app/admin/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next';\r\n\r\nimport { Settings } from 'lucide-react';\r\n\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\n\r\nexport const metadata: Metadata = {\r\n  description: 'Administrative dashboard for WorkHub system',\r\n  title: 'Admin Dashboard - WorkHub',\r\n};\r\n\r\nexport default function AdminLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center\">\r\n        <Breadcrumb className=\"mb-4\">\r\n          <BreadcrumbList>\r\n            <BreadcrumbItem>\r\n              <BreadcrumbLink href=\"/\">Home</BreadcrumbLink>\r\n            </BreadcrumbItem>\r\n            <BreadcrumbSeparator />\r\n            <BreadcrumbItem>\r\n              <BreadcrumbPage>Admin</BreadcrumbPage>\r\n            </BreadcrumbItem>\r\n          </BreadcrumbList>\r\n        </Breadcrumb>\r\n      </div>\r\n      <div className=\"flex-1\">{children}</div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAIA;;;AASO,MAAM,WAAqB;IAChC,aAAa;IACb,OAAO;AACT;AAEe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC,sIAAA,CAAA,iBAAc;;0CACb,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCAAC,MAAK;8CAAI;;;;;;;;;;;0CAE3B,8OAAC,sIAAA,CAAA,sBAAmB;;;;;0CACpB,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;;AAG/B", "debugId": null}}]}