(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9977],{20029:(e,t,r)=>{Promise.resolve().then(r.bind(r,36615))},36615:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(95155),i=r(31949),a=r(12543),l=r(57082),n=r(35695),o=r(12115),c=r(88240),d=r(67428),u=r(6560),m=r(77023),h=r(95647),p=r(17841),g=r(83761),f=r(80937),x=r(21876);let v=e=>{var t,r;return e?{airport:e.airport||"",dateTime:(0,x.rm)(e.dateTime,"datetime-local"),flightNumber:e.flightNumber||"",notes:null!=(t=e.notes)?t:null,terminal:null!=(r=e.terminal)?r:null}:null};function y(){let e=(0,n.useRouter)(),t=(0,n.useParams)(),r=null==t?void 0:t.id;(0,g.sZ)(),(0,g.sZ)("driver"),(0,f.T$)();let{data:y,error:N,isLoading:j,refetch:E}=(0,p.gd)(r),{mutateAsync:D}=(0,p.jM)(),A=async t=>{if(console.log("\uD83D\uDCDD Edit page handleSubmit called",{delegationId:r,formData:t}),!r)throw Error("Delegation ID is missing.");try{var s,i,a,l,n,o,c,d,u;let m={delegates:null!=(s=t.delegates.filter(e=>e.name&&e.title).map(e=>({name:e.name,title:e.title,...void 0!==e.notes&&{notes:e.notes}})))?s:[],driverEmployeeIds:null!=(i=t.driverEmployeeIds)?i:[],durationFrom:null!=(a=(0,x.B7)(t.durationFrom))?a:"",durationTo:null!=(l=(0,x.B7)(t.durationTo))?l:"",escortEmployeeIds:null!=(n=t.escortEmployeeIds)?n:[],eventName:t.eventName,flightArrivalDetails:t.flightArrivalDetails?{airport:t.flightArrivalDetails.airport,dateTime:(0,x.g3)(t.flightArrivalDetails.dateTime),flightNumber:t.flightArrivalDetails.flightNumber,notes:null!=(o=t.flightArrivalDetails.notes)?o:null,terminal:null!=(c=t.flightArrivalDetails.terminal)?c:null}:void 0,flightDepartureDetails:t.flightDepartureDetails?{airport:t.flightDepartureDetails.airport,dateTime:(0,x.g3)(t.flightDepartureDetails.dateTime),flightNumber:t.flightDepartureDetails.flightNumber,notes:null!=(d=t.flightDepartureDetails.notes)?d:null,terminal:null!=(u=t.flightDepartureDetails.terminal)?u:null}:void 0,imageUrl:t.imageUrl,invitationFrom:t.invitationFrom,invitationTo:t.invitationTo,location:t.location,notes:t.notes,status:t.status.replace(" ","_"),vehicleIds:t.vehicleIds},h=Object.fromEntries(Object.entries(m).filter(e=>{let[,t]=e;return void 0!==t}));console.log("\uD83D\uDD04 Calling updateDelegationMutation",{cleanedUpdatePayload:h,delegationId:r}),await D({data:h,id:r}),console.log("✅ Delegation updated successfully"),e.push("/delegations/".concat(r))}catch(e){throw console.error("❌ Error in handleSubmit:",e),e}},w=(0,o.useMemo)(()=>{if(y){var e,t,r,s,i,a,l,n;return{delegates:null!=(i=null==(e=y.delegates)?void 0:e.map(e=>{var t,r;return{id:e.id,name:e.name||"Unknown Delegate",notes:null!=(t=e.notes)?t:"",title:null!=(r=e.title)?r:"No Title Specified"}}))?i:[],driverEmployeeIds:null!=(a=null==(t=y.drivers)?void 0:t.map(e=>Number(e.employeeId)).filter(e=>!isNaN(e)&&e>0))?a:[],durationFrom:(0,x.rm)(y.durationFrom,"date"),durationTo:(0,x.rm)(y.durationTo,"date"),escortEmployeeIds:null!=(l=null==(r=y.escorts)?void 0:r.map(e=>Number(e.employeeId)).filter(e=>!isNaN(e)&&e>0))?l:[],eventName:y.eventName||"",flightArrivalDetails:v(y.arrivalFlight),flightDepartureDetails:v(y.departureFlight),imageUrl:y.imageUrl||"",invitationFrom:y.invitationFrom||"",invitationTo:y.invitationTo||"",location:y.location||"",notes:y.notes||"",status:y.status.replace("_"," "),statusChangeReason:"",vehicleIds:null!=(n=null==(s=y.vehicles)?void 0:s.map(e=>e.id).filter(e=>"number"==typeof e&&e>0))?n:[]}}},[y]);return(0,s.jsx)(c.A,{children:(0,s.jsx)(m.gO,{data:w,emptyComponent:(0,s.jsxs)("div",{className:"space-y-6 text-center",children:[(0,s.jsx)(h.z,{icon:i.A,title:"Delegation Not Found"}),(0,s.jsx)("p",{children:"The requested delegation could not be found."}),(0,s.jsx)(u.r,{actionType:"primary",icon:(0,s.jsx)(a.A,{className:"size-4"}),onClick:()=>e.push("/delegations"),children:"Back to Delegations"})]}),error:N?N.message:null,isLoading:j||!1,loadingComponent:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(h.z,{icon:l.A,title:"Loading Delegation..."}),(0,s.jsx)(m.jt,{count:1,variant:"card"})]}),onRetry:E,children:e=>(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(h.z,{description:"Modify the details for this delegation or event.",icon:l.A,title:"Edit Delegation: ".concat(e.eventName)}),(0,s.jsx)(d.GK,{initialData:e,isEditing:!0,onSubmit:A})]})})})}},40879:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m,oR:()=>u});var s=r(12115);let i=0,a=new Map,l=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),d({toastId:e,type:"REMOVE_TOAST"})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:r}=t;if(r)l(r);else for(let t of e.toasts)l(t.id);return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},o=[],c={toasts:[]};function d(e){for(let t of(c=n(c,e),o))t(c)}function u(e){let{...t}=e,r=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({toastId:r,type:"DISMISS_TOAST"});return d({toast:{...t,id:r,onOpenChange:e=>{e||s()},open:!0},type:"ADD_TOAST"}),{dismiss:s,id:r,update:e=>d({toast:{...e,id:r},type:"UPDATE_TOAST"})}}function m(){let[e,t]=s.useState(c);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);-1!==e&&o.splice(e,1)}),[e]),{...e,dismiss:e=>d({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:u}}},67554:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},68856:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(95155),i=r(54036);function a(e){let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",t),...r})}},77023:(e,t,r)=>{"use strict";r.d(t,{gO:()=>m,jt:()=>f,pp:()=>h});var s=r(95155),i=r(11133),a=r(50172);r(12115);var l=r(6560),n=r(55365),o=r(68856),c=r(54036);let d={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},u={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function m(e){let{children:t,className:r,data:i,emptyComponent:a,error:l,errorComponent:n,isLoading:o,loadingComponent:d,onRetry:u}=e;return o?d||(0,s.jsx)(g,{...r&&{className:r},text:"Loading..."}):l?n||(0,s.jsx)(p,{...r&&{className:r},message:l,...u&&{onRetry:u}}):!i||Array.isArray(i)&&0===i.length?a||(0,s.jsx)("div",{className:(0,c.cn)("text-center py-8",r),children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,s.jsx)("div",{className:r,children:t(i)})}function h(e){let{className:t,description:r,icon:i,primaryAction:a,secondaryAction:n,title:o}=e;return(0,s.jsxs)("div",{className:(0,c.cn)("space-y-6 text-center py-12",t),children:[i&&(0,s.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,s.jsx)(i,{className:"h-10 w-10 text-muted-foreground"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:o}),r&&(0,s.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:r})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[a&&(0,s.jsx)(l.r,{actionType:"primary",asChild:!!a.href,icon:a.icon,onClick:a.onClick,children:a.href?(0,s.jsx)("a",{href:a.href,children:a.label}):a.label}),n&&(0,s.jsx)(l.r,{actionType:"tertiary",asChild:!!n.href,icon:n.icon,onClick:n.onClick,children:n.href?(0,s.jsx)("a",{href:n.href,children:n.label}):n.label})]})]})}function p(e){let{className:t,message:r,onRetry:o}=e;return(0,s.jsxs)(n.Fc,{className:(0,c.cn)("my-4",t),variant:"destructive",children:[(0,s.jsx)(i.A,{className:"size-4"}),(0,s.jsx)(n.XL,{children:"Error"}),(0,s.jsx)(n.TN,{children:(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:r}),o&&(0,s.jsx)(l.r,{actionType:"tertiary",icon:(0,s.jsx)(a.A,{className:"size-4"}),onClick:o,size:"sm",children:"Try Again"})]})})]})}function g(e){let{className:t,fullPage:r=!1,size:i="md",text:l}=e;return(0,s.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",r&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",t),children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(a.A,{className:(0,c.cn)("animate-spin text-primary",d[i])}),l&&(0,s.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",u[i]),children:l})]})})}function f(e){let{className:t,count:r=1,testId:i="loading-skeleton",variant:a="default"}=e;return"card"===a?(0,s.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",t),"data-testid":i,children:Array(r).fill(0).map((e,t)=>(0,s.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,s.jsx)(o.E,{className:"aspect-[16/10] w-full"}),(0,s.jsxs)("div",{className:"p-5",children:[(0,s.jsx)(o.E,{className:"mb-1 h-7 w-3/4"}),(0,s.jsx)(o.E,{className:"mb-3 h-4 w-1/2"}),(0,s.jsx)(o.E,{className:"my-3 h-px w-full"}),(0,s.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.E,{className:"mr-2.5 size-5 rounded-full"}),(0,s.jsx)(o.E,{className:"h-5 w-2/3"})]},t))})]})]},t))}):"table"===a?(0,s.jsxs)("div",{className:(0,c.cn)("space-y-3",t),"data-testid":i,children:[(0,s.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,t)=>(0,s.jsx)(o.E,{className:"h-8 flex-1"},t))}),Array(r).fill(0).map((e,t)=>(0,s.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,t)=>(0,s.jsx)(o.E,{className:"h-6 flex-1"},t))},t))]}):"list"===a?(0,s.jsx)("div",{className:(0,c.cn)("space-y-3",t),"data-testid":i,children:Array(r).fill(0).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(o.E,{className:"size-12 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(o.E,{className:"h-4 w-1/3"}),(0,s.jsx)(o.E,{className:"h-4 w-full"})]})]},t))}):"stats"===a?(0,s.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",t),"data-testid":i,children:Array(r).fill(0).map((e,t)=>(0,s.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(o.E,{className:"h-5 w-1/3"}),(0,s.jsx)(o.E,{className:"size-5 rounded-full"})]}),(0,s.jsx)(o.E,{className:"mt-3 h-8 w-1/2"}),(0,s.jsx)(o.E,{className:"mt-2 h-4 w-2/3"})]},t))}):(0,s.jsx)("div",{className:(0,c.cn)("space-y-2",t),"data-testid":i,children:Array(r).fill(0).map((e,t)=>(0,s.jsx)(o.E,{className:"h-5 w-full"},t))})}},83940:(e,t,r)=>{"use strict";r.d(t,{G7:()=>m,Gb:()=>o,JP:()=>c,Ok:()=>d,Qu:()=>u,iw:()=>n,oz:()=>p,z0:()=>h});var s=r(40879);class i{show(e){return(0,s.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class a extends i{entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}constructor(e){super(),this.config=e}}class l extends i{serviceRecordCreated(e,t){return this.success("Service Record Added","".concat(t,' service for "').concat(e,'" has been successfully logged.'))}serviceRecordUpdated(e,t){return this.success("Service Record Updated","".concat(t,' service for "').concat(e,'" has been updated.'))}serviceRecordDeleted(e,t){return this.success("Service Record Deleted","".concat(t,' service record for "').concat(e,'" has been permanently removed.'))}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new a(e)}function o(e,t){return new a({entityName:e,getDisplayName:t,messages:{created:{title:"".concat(e," Created"),description:t=>"The ".concat(e.toLowerCase(),' "').concat(t,'" has been successfully created.')},updated:{title:"".concat(e," Updated Successfully"),description:e=>"".concat(e," has been updated.")},deleted:{title:"".concat(e," Deleted Successfully"),description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create ".concat(e),description:t=>t||"An unexpected error occurred while creating the ".concat(e.toLowerCase(),".")},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the ".concat(e.toLowerCase(),".")},deletionError:{title:"Failed to Delete ".concat(e),description:t=>t||"An unexpected error occurred while deleting the ".concat(e.toLowerCase(),".")}}})}let c=new i,d=new a({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>'The employee "'.concat(e,'" has been successfully created.')},updated:{title:"Employee Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Employee Deleted Successfully",description:e=>"".concat(e," has been permanently removed from the system.")},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new a({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>'The delegation "'.concat(e,'" has been successfully created.')},updated:{title:"Delegation Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Delegation Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),m=new a({entityName:"Vehicle",getDisplayName:e=>"".concat(e.make," ").concat(e.model),messages:{created:{title:"Vehicle Added",description:e=>'The vehicle "'.concat(e,'" has been successfully created.')},updated:{title:"Vehicle Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Vehicle Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new a({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>'The task "'.concat(e,'" has been successfully created.')},updated:{title:"Task Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Task Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),p=new l},88240:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(95155),i=r(31949),a=r(67554),l=r(12115),n=r(55365),o=r(30285);class c extends l.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,t){this.setState({errorInfo:t}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",t.componentStack),this.props.onError&&this.props.onError(e,t)}render(){let{description:e="An unexpected error occurred.",resetLabel:t="Try Again",title:r="Something went wrong"}=this.props;if(this.state.hasError){var l;return this.props.fallback?this.props.fallback:(0,s.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,s.jsx)(i.A,{className:"mr-2 size-4"}),(0,s.jsx)(n.XL,{className:"text-lg font-semibold",children:r}),(0,s.jsxs)(n.TN,{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-2",children:(null==(l=this.state.error)?void 0:l.message)||e}),!1,(0,s.jsxs)(o.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,s.jsx)(a.A,{className:"mr-2 size-4"}),t]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let d=c}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,7047,3860,9664,1263,5495,1859,5669,4629,7454,8982,6548,4036,4767,8950,7515,7841,542,8441,1684,7358],()=>t(20029)),_N_E=e.O()}]);