(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2003],{19018:(e,r,s)=>{"use strict";s.d(r,{Breadcrumb:()=>n,BreadcrumbItem:()=>d,BreadcrumbLink:()=>m,BreadcrumbList:()=>o,BreadcrumbPage:()=>h,BreadcrumbSeparator:()=>x});var a=s(95155),l=s(99708),i=s(73158),c=(s(3561),s(12115)),t=s(54036);let n=c.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("nav",{"aria-label":"breadcrumb",className:(0,t.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",s),ref:r,...l})});n.displayName="Breadcrumb";let o=c.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("ol",{className:(0,t.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",s),ref:r,...l})});o.displayName="BreadcrumbList";let d=c.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("li",{className:(0,t.cn)("inline-flex items-center gap-1.5",s),ref:r,...l})});d.displayName="BreadcrumbItem";let m=c.forwardRef((e,r)=>{let{asChild:s,className:i,...c}=e,n=s?l.DX:"a";return(0,a.jsx)(n,{className:(0,t.cn)("transition-colors hover:text-foreground",i),ref:r,...c})});m.displayName="BreadcrumbLink";let h=c.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,t.cn)("font-normal text-foreground",s),ref:r,role:"link",...l})});h.displayName="BreadcrumbPage";let x=e=>{let{children:r,className:s,...l}=e;return(0,a.jsx)("span",{"aria-hidden":"true",className:(0,t.cn)("[&>svg]:size-3.5",s),role:"presentation",...l,children:null!=r?r:(0,a.jsx)(i.A,{className:"size-4"})})};x.displayName="BreadcrumbSeparator"},33271:(e,r,s)=>{"use strict";s.d(r,{k:()=>f});var a=s(95155),l=s(18018),i=s(50172),c=s(68718),t=s(15300),n=s(60679),o=s(12115),d=s(6560),m=s(44838),h=s(53712),x=s(54036),u=s(16146);function f(e){let{className:r,csvData:s,enableCsv:f=!1,entityId:p,fileName:j,reportContentId:v,reportType:b,tableId:g}=e,[N,y]=(0,o.useState)(!1),[w,S]=(0,o.useState)(!1),{showFormSuccess:A,showFormError:C}=(0,h.t6)(),k=async()=>{y(!0);try{let e="/api/reports/".concat(b).concat(p?"/".concat(p):""),r=document.createElement("a");r.href=e,r.download="".concat(j,".pdf"),r.target="_blank",document.body.append(r),r.click(),r.remove(),A({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),C("PDF download failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{y(!1)}},B=async()=>{if(f){S(!0);try{if((null==s?void 0:s.data)&&s.headers)(0,u.og)(s.data,s.headers,"".concat(j,".csv"));else if(g){let e=(0,u.tL)(g);(0,u.og)(e.data,e.headers,"".concat(j,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");A({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),C("CSV generation failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{S(!1)}}},z=N||w;return(0,a.jsxs)("div",{className:(0,x.cn)("flex items-center gap-2 no-print",r),children:[(0,a.jsx)(d.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,a.jsx)(l.A,{className:"size-4"})}),(0,a.jsxs)(m.rI,{children:[(0,a.jsx)(m.ty,{asChild:!0,children:(0,a.jsx)(d.r,{actionType:"secondary","aria-label":"Download report",disabled:z,size:"icon",title:"Download Report",children:z?(0,a.jsx)(i.A,{className:"size-4 animate-spin"}):(0,a.jsx)(c.A,{className:"size-4"})})}),(0,a.jsxs)(m.SQ,{align:"end",children:[(0,a.jsxs)(m._2,{disabled:N,onClick:k,children:[N?(0,a.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,a.jsx)(t.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:"Download PDF"})]}),f&&(0,a.jsxs)(m._2,{disabled:w,onClick:B,children:[w?(0,a.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,a.jsx)(n.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:"Download CSV"})]})]})]})]})}},35369:(e,r,s)=>{Promise.resolve().then(s.bind(s,93974))},85057:(e,r,s)=>{"use strict";s.d(r,{J:()=>o});var a=s(95155),l=s(12115),i=s(40968),c=s(74466),t=s(54036);let n=(0,c.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,a.jsx)(i.b,{ref:r,className:(0,t.cn)(n(),s),...l})});o.displayName=i.b.displayName},89440:(e,r,s)=>{"use strict";s.d(r,{AppBreadcrumb:()=>d});var a=s(95155),l=s(6874),i=s.n(l),c=s(35695),t=s(12115),n=s(19018),o=s(54036);function d(e){let{className:r,homeHref:s="/",homeLabel:l="Dashboard",showContainer:d=!0}=e,m=(0,c.usePathname)(),h=m?m.split("/").filter(Boolean):[],x=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},u=h.map((e,r)=>{let s="/"+h.slice(0,r+1).join("/"),l=r===h.length-1,c=x(e);return(0,a.jsxs)(t.Fragment,{children:[(0,a.jsx)(n.BreadcrumbItem,{children:l?(0,a.jsx)(n.BreadcrumbPage,{className:"font-medium text-foreground",children:c}):(0,a.jsx)(n.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:c})})}),!l&&(0,a.jsx)(n.BreadcrumbSeparator,{})]},s)}),f=(0,a.jsx)(n.Breadcrumb,{className:(0,o.cn)("text-sm",r),children:(0,a.jsxs)(n.BreadcrumbList,{className:"flex-wrap",children:[(0,a.jsx)(n.BreadcrumbItem,{children:(0,a.jsx)(n.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:l})})}),h.length>0&&(0,a.jsx)(n.BreadcrumbSeparator,{}),u]})});return d?(0,a.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"flex items-center",children:f})}):f}},93974:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>L});var a=s(95155),l=s(11518),i=s.n(l),c=s(3638),t=s(34301),n=s(25318),o=s(75074),d=s(28328),m=s(3235),h=s(6874),x=s.n(h),u=s(12115),f=s(88240);let p=e=>{let{children:r,fallback:s}=e;return(0,a.jsx)(f.A,{description:"An unexpected error occurred while loading service records.",fallback:s,onError:(e,r)=>{console.error("ServiceRecords component error:",e),console.error("Component stack:",r.componentStack)},resetLabel:"Try Again",title:"Error Loading Service Records",children:r})};var j=s(33271),v=s(58824),b=s(6560),g=s(89440),N=s(26126),y=s(30285),w=s(66695),S=s(62523),A=s(85057),C=s(77023),k=s(95647),B=s(59409),z=s(68856),R=s(40283),E=s(98691),F=s(80937);let D=["Oil Change","Tire Rotation","Brake Service","Battery Replacement","Air Filter Replacement","Cabin Air Filter Replacement","Wiper Blade Replacement","Fluid Check/Top-up","Spark Plug Replacement","Coolant Flush","Transmission Service","Wheel Alignment","State Inspection","Other"];function L(){var e,r;let{loading:s,session:l,user:h}=(0,R.useAuthContext)(),L=!!h&&!!(null==l?void 0:l.access_token),{data:T,error:P,isLoading:V,refetch:I}=(0,F.T$)({enabled:L&&!s}),_=(0,u.useMemo)(()=>T||[],[T]),{data:M,error:O,isLoading:H,refetch:W}=(0,E.fs)({enabled:L&&!s}),$=(0,u.useMemo)(()=>M||[],[M]),[J,Z]=(0,u.useState)("all"),[q,Y]=(0,u.useState)("all"),[G,Q]=(0,u.useState)(""),[U,X]=(0,u.useState)("");(0,u.useEffect)(()=>{let e=setTimeout(()=>{X(G)},300);return()=>clearTimeout(e)},[G]);let K=(0,u.useMemo)(()=>{let e=new Set;return $.forEach(r=>{r.servicePerformed.forEach(r=>e.add(r))}),D.forEach(r=>e.add(r)),[...e].sort()},[$]),ee=(0,u.useMemo)(()=>{let e=[...$];if("all"!==J&&(e=e.filter(e=>String(e.vehicleId)===J)),"all"!==q&&(e=e.filter(e=>e.servicePerformed.includes(q))),U){let r=U.toLowerCase();e=e.filter(e=>{var s,a;return"".concat(e.vehicleMake," ").concat(e.vehicleModel).toLowerCase().includes(r)||e.servicePerformed.join(" ").toLowerCase().includes(r)||(null==(s=e.notes)?void 0:s.toLowerCase().includes(r))||(null==(a=e.licensePlate)?void 0:a.toLowerCase().includes(r))||e.odometer.toString().includes(r)})}return e},[$,J,q,U]),er=(0,u.useCallback)(()=>{"function"==typeof I&&I(),"function"==typeof W&&W()},[I,W]),es=s||V||H,ea=(0,u.useMemo)(()=>{let e=[];return P&&e.push("Vehicles: ".concat(P.message)),O&&e.push("Service Records: ".concat(O.message)),e.length>0?e.join("; "):null},[P,O]);if(es)return(0,a.jsx)(f.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(k.z,{icon:c.A,title:"Service History",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(z.E,{className:"h-10 w-32"}),(0,a.jsx)(z.E,{className:"h-10 w-32"})]})}),(0,a.jsx)(w.Zp,{children:(0,a.jsx)(w.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,a.jsx)(z.E,{className:"h-10 w-full"}),(0,a.jsx)(z.E,{className:"h-10 w-full"}),(0,a.jsx)(z.E,{className:"h-10 w-full"})]})})}),(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 border-b p-4",children:[(0,a.jsx)(z.E,{className:"h-6 w-1/6"}),(0,a.jsx)(z.E,{className:"h-6 w-1/6"}),(0,a.jsx)(z.E,{className:"h-6 w-2/6"}),(0,a.jsx)(z.E,{className:"h-6 w-1/6"}),(0,a.jsx)(z.E,{className:"h-6 w-1/6"})]},r))})]})});if(!s&&!L)return(0,a.jsx)(f.A,{children:(0,a.jsxs)("div",{className:"space-y-6 text-center",children:[(0,a.jsx)(k.z,{icon:c.A,title:"Access Denied"}),(0,a.jsx)("p",{children:"Please sign in to view service history."})]})});if(!es&&L&&0===ee.length&&!ea){let e="all"!==J||"all"!==q||G;return(0,a.jsx)(f.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(g.AppBreadcrumb,{}),(0,a.jsx)(k.z,{description:"View and manage all service records for your vehicles.",icon:c.A,title:"Service History Report",children:(0,a.jsx)("div",{className:"no-print flex gap-2",children:(0,a.jsx)(b.r,{actionType:"tertiary",asChild:!0,icon:(0,a.jsx)(t.A,{className:"size-4"}),children:(0,a.jsx)(x(),{href:"/vehicles",children:"Log New Service"})})})}),(0,a.jsx)(C.pp,{description:e?"There are no service records matching your current filters. You can log a new service record or adjust your filters.":"No service records have been logged yet. Get started by logging your first service record.",icon:c.A,primaryAction:{href:"/vehicles",icon:(0,a.jsx)(t.A,{className:"size-4"}),label:"Log New Service"},title:"No Service Records Found",...e&&{secondaryAction:{icon:(0,a.jsx)(n.A,{className:"size-4"}),label:"Reset Filters",onClick:()=>{Z("all"),Y("all"),Q("")}}}})]})})}return(0,a.jsx)(f.A,{children:(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9 print-container space-y-6",children:[(0,a.jsx)(g.AppBreadcrumb,{}),(0,a.jsx)(k.z,{description:"View and manage all service records for your vehicles.",icon:c.A,title:"Service History Report",children:(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9 no-print flex gap-2",children:[(0,a.jsx)(b.r,{actionType:"tertiary",asChild:!0,icon:(0,a.jsx)(t.A,{className:"size-4"}),children:(0,a.jsx)(x(),{href:"/vehicles",children:"Log New Service"})}),(0,a.jsx)(j.k,{enableCsv:ee.length>0,fileName:"service-history-report-".concat(new Date().toISOString().split("T")[0]),reportContentId:"#service-history-report-content",reportType:"service-history",tableId:"#service-history-table"})]})}),(0,a.jsxs)(w.Zp,{className:"no-print shadow-sm",children:[(0,a.jsxs)(w.aR,{className:"pb-2",children:[(0,a.jsx)(w.ZB,{className:"text-lg",children:"Filter Options"}),(0,a.jsx)(w.BT,{children:"Filter service records by vehicle, service type, or search terms"})]}),(0,a.jsxs)(w.Wu,{children:[(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9 filter-grid grid grid-cols-1 items-end gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,a.jsx)(A.J,{htmlFor:"vehicle-filter",children:"Filter by Vehicle"}),(0,a.jsxs)(B.l6,{"aria-label":"Filter by vehicle",onValueChange:Z,value:J,children:[(0,a.jsx)(B.bq,{className:"mt-1.5 w-full",id:"vehicle-filter",children:(0,a.jsx)(B.yv,{placeholder:"All Vehicles"})}),(0,a.jsxs)(B.gC,{children:[(0,a.jsx)(B.eb,{value:"all",children:"All Vehicles"}),_.map(e=>(0,a.jsxs)(B.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,")"]},e.id))]})]})]}),(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,a.jsx)(A.J,{htmlFor:"service-filter",children:"Filter by Service Type"}),(0,a.jsxs)(B.l6,{"aria-label":"Filter by service type",onValueChange:Y,value:q,children:[(0,a.jsx)(B.bq,{className:"mt-1.5 w-full",id:"service-filter",children:(0,a.jsx)(B.yv,{placeholder:"All Services"})}),(0,a.jsxs)(B.gC,{children:[(0,a.jsx)(B.eb,{value:"all",children:"All Services"}),K.map(e=>(0,a.jsx)(B.eb,{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,a.jsx)(A.J,{htmlFor:"search-records",children:"Search Records"}),(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9 relative mt-1.5",children:[(0,a.jsx)(S.p,{"aria-label":"Search service records",className:"px-10",id:"search-records",onChange:e=>Q(e.target.value),placeholder:"Search by keyword, notes, plate...",type:"text",value:G}),(0,a.jsx)(o.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),G&&(0,a.jsxs)(y.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>Q(""),size:"icon",variant:"ghost",children:[(0,a.jsx)(n.A,{className:"size-4"}),(0,a.jsx)("span",{className:"jsx-fb311328cf4436d9 sr-only",children:"Clear search"})]})]})]})]}),("all"!==J||"all"!==q||G)&&(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9 mt-6 flex flex-wrap items-center justify-between rounded-md border border-border p-3",children:[(0,a.jsxs)("div",{className:"jsx-fb311328cf4436d9 flex flex-wrap items-center gap-2",children:[(0,a.jsx)("span",{className:"jsx-fb311328cf4436d9 text-sm font-medium",children:"Active Filters:"}),"all"!==J&&(0,a.jsxs)(N.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,a.jsx)(d.A,{className:"size-3"}),null==(e=_.find(e=>String(e.id)===J))?void 0:e.make," ",null==(r=_.find(e=>String(e.id)===J))?void 0:r.model]}),"all"!==q&&(0,a.jsxs)(N.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,a.jsx)(m.A,{className:"size-3"}),q]}),G&&(0,a.jsxs)(N.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,a.jsx)(o.A,{className:"size-3"}),'"',G,'"']})]}),(0,a.jsxs)(y.$,{"aria-label":"Reset all filters",className:"mt-2 sm:mt-0",onClick:()=>{Z("all"),Y("all"),Q("")},size:"sm",variant:"outline",children:[(0,a.jsx)(n.A,{className:"mr-1 size-3"}),"Reset Filters"]})]})]})]}),(0,a.jsxs)("div",{id:"service-history-report-content",className:"jsx-fb311328cf4436d9 report-content",children:[(0,a.jsx)(p,{children:(0,a.jsx)(v.R,{error:ea,isLoading:H&&!M,onRetry:er,records:ee,showVehicleInfo:!0,vehicleSpecific:!1})}),(0,a.jsxs)("footer",{className:"jsx-fb311328cf4436d9 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,a.jsxs)("p",{className:"jsx-fb311328cf4436d9",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,a.jsx)("p",{className:"jsx-fb311328cf4436d9",children:"WorkHub - Vehicle Service Management"})]})]}),(0,a.jsx)(i(),{id:"fb311328cf4436d9",children:""})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6476,7047,3860,9664,1263,5495,1859,6874,5247,6463,4866,4036,4767,8950,3712,3615,5320,6554,6563,8441,1684,7358],()=>r(35369)),_N_E=e.O()}]);