exports.id=101,exports.ids=[101],exports.modules={321:(e,t,s)=>{"use strict";s.d(t,{OV:()=>r.ProtectedRoute}),s(63213),s(55387);var r=s(34570)},1427:(e,t,s)=>{Promise.resolve().then(s.bind(s,76928)),Promise.resolve().then(s.bind(s,41674))},2143:(e,t,s)=>{"use strict";s.d(t,{k:()=>r});class r{static{this.CSRF_HEADER_NAME="X-CSRF-Token"}static{this.CSRF_COOKIE_NAME="csrf-token"}static{this.CSRF_STORAGE_KEY="workhub_csrf_token"}static{this.TOKEN_LIFETIME_MINUTES=60}static generateToken(){let e=new Uint8Array(32);for(let t=0;t<e.length;t++)e[t]=Math.floor(256*Math.random());return btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}static validateToken(e){if(!e||"string"!=typeof e||!/^[A-Za-z0-9_-]+$/.test(e))return{isValid:!1,error:"Invalid token format"};if(e.length<32)return{isValid:!1,error:"Token too short"};let t=this.getStoredToken();return t?!t.isValid||t.expiresAt<new Date?{isValid:!1,error:"Stored token expired"}:e!==t.token?{isValid:!1,error:"Token mismatch"}:{isValid:!0,token:e}:{isValid:!1,error:"No stored token found"}}static attachToRequest(e){if(!["POST","PUT","PATCH","DELETE"].includes((e.method||"GET").toUpperCase()))return e;let t=this.getCurrentToken();if(!t)return console.warn("No CSRF token available for request"),e;let s={...e.headers,[this.CSRF_HEADER_NAME]:t.token};return{...e,headers:s}}static getCurrentToken(){let e=this.getStoredToken();return!e||!e.isValid||e.expiresAt<new Date?this.refreshToken():e}static refreshToken(){let e={token:this.generateToken(),expiresAt:new Date(Date.now()+60*this.TOKEN_LIFETIME_MINUTES*1e3),isValid:!0};return this.storeToken(e),e}static clearToken(){}static initialize(){return this.refreshToken()}static isProtectionRequired(e){return["POST","PUT","PATCH","DELETE"].includes((e.method||"GET").toUpperCase())}static extractTokenFromResponse(e){return e[this.CSRF_HEADER_NAME.toLowerCase()]||e[this.CSRF_HEADER_NAME]||null}static getStoredToken(){return null}static storeToken(e){}}},3389:(e,t,s)=>{"use strict";s.d(t,{dj:()=>m,oR:()=>u});var r=s(43210);let i=0,a=new Map,n=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),d({toastId:e,type:"REMOVE_TOAST"})},1e6);a.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:s}=t;if(s)n(s);else for(let t of e.toasts)n(t.id);return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},l=[],c={toasts:[]};function d(e){for(let t of(c=o(c,e),l))t(c)}function u({...e}){let t=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({toastId:t,type:"DISMISS_TOAST"});return d({toast:{...e,id:t,onOpenChange:e=>{e||s()},open:!0},type:"ADD_TOAST"}),{dismiss:s,id:t,update:e=>d({toast:{...e,id:t},type:"UPDATE_TOAST"})}}function m(){let[e,t]=r.useState(c);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);-1!==e&&l.splice(e,1)}),[e]),{...e,dismiss:e=>d({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:u}}},8342:(e,t,s)=>{"use strict";s.d(t,{Sk:()=>o,getGlobalAuthTokenProvider:()=>l,uE:()=>d,y2:()=>n});var r=s(79772);s(77312),s(63924);var i=s(23133);s(93778),s(44194),s(10212),s(76783),s(30930),s(43845),s(16467);let a=null;function n(e){a=e}function o(){return a}function l(){return console.warn("⚠️ getGlobalAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead."),a}let c=(0,i.Qq)(),d=new r.O({baseURL:c.apiBaseUrl,getAuthToken:function(){if(!a)return null;try{return a()}catch(e){return console.error("❌ Error getting auth token from secure provider:",e),null}},headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4})},8639:(e,t,s)=>{"use strict";s.d(t,{A5:()=>m,Ln:()=>h,uq:()=>u});var r=s(60687),i=s(50616),a=s(58450);s(43210);var n=s(96834),o=s(29523),l=s(44493),c=s(64968);let d=[{className:"text-sm",description:"Compact text for more content",example:"The quick brown fox jumps over the lazy dog",label:"Small",value:"small"},{className:"text-base",description:"Standard readable text",example:"The quick brown fox jumps over the lazy dog",label:"Medium",value:"medium"},{className:"text-lg",description:"Larger text for better accessibility",example:"The quick brown fox jumps over the lazy dog",label:"Large",value:"large"}],u=()=>{let{fontSize:e,getFontSizeClass:t,setFontSize:s}=(0,c.useUiPreferences)();return(0,r.jsxs)(l.Zp,{className:"w-full max-w-2xl",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(i.A,{className:"size-5"}),"Font Size Preferences"]}),(0,r.jsx)(l.BT,{children:"Choose your preferred font size for better readability and accessibility"})]}),(0,r.jsxs)(l.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-muted/50 p-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Current Font Size"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Applied across the entire application"})]}),(0,r.jsx)(n.E,{className:"capitalize",variant:"secondary",children:e})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Available Options"}),d.map(t=>(0,r.jsx)("div",{className:`
                relative cursor-pointer rounded-lg border p-4 transition-all
                ${e===t.value?"border-primary bg-primary/5 ring-1 ring-primary/20":"border-border hover:border-primary/50 hover:bg-muted/30"}
              `,onClick:()=>s(t.value),children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,r.jsx)("h5",{className:"font-medium",children:t.label}),e===t.value&&(0,r.jsx)(a.A,{className:"size-4 text-primary"})]}),(0,r.jsx)("p",{className:"mb-3 text-sm text-muted-foreground",children:t.description}),(0,r.jsx)("div",{className:`rounded border bg-background p-3 ${t.className}`,children:t.example})]})})},t.value))]}),(0,r.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[(0,r.jsx)("div",{className:"flex gap-2",children:d.map(t=>(0,r.jsx)(o.$,{className:"capitalize",onClick:()=>s(t.value),size:"sm",variant:e===t.value?"default":"outline",children:t.label},t.value))}),(0,r.jsx)(o.$,{className:"text-muted-foreground",onClick:()=>s("medium"),size:"sm",variant:"ghost",children:"Reset to Default"})]}),(0,r.jsxs)("div",{className:"rounded-lg border bg-background p-4",children:[(0,r.jsx)("h5",{className:"mb-2 font-medium",children:"Live Preview"}),(0,r.jsxs)("div",{className:`space-y-2 ${t()}`,children:[(0,r.jsx)("p",{className:"font-semibold",children:"Heading Text"}),(0,r.jsx)("p",{children:"This is how regular paragraph text will appear with your selected font size. The setting applies to all text content throughout the WorkHub application."}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Secondary text and descriptions will also scale accordingly."})]})]})]})]})},m=()=>{let{fontSize:e,setFontSize:t}=(0,c.useUiPreferences)();return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.A,{className:"size-4 text-muted-foreground"}),(0,r.jsxs)("select",{className:"   rounded border border-input bg-background px-2 py-1 text-sm   focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2   ",onChange:e=>t(e.target.value),value:e,children:[(0,r.jsx)("option",{value:"small",children:"Small"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"large",children:"Large"})]})]})},h=()=>{let{fontSize:e,setFontSize:t}=(0,c.useUiPreferences)();return(0,r.jsxs)(o.$,{className:"flex items-center gap-2",onClick:()=>{let s=["small","medium","large"],r=(s.indexOf(e)+1)%s.length,i=s[r];i&&t(i)},size:"sm",title:`Current: ${e}. Click to cycle.`,variant:"ghost",children:[(0,r.jsx)(i.A,{className:"size-4"}),(0,r.jsx)("span",{className:"text-xs capitalize",children:e})]})}},10035:(e,t,s)=>{"use strict";s.d(t,{M:()=>i});var r=s(43210);function i(){let[e,t]=(0,r.useState)({}),[s,i]=(0,r.useState)(!1),a=(0,r.useCallback)(e=>e?/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)?void 0:"Please enter a valid email address":"Email address is required",[]),n=(0,r.useCallback)(e=>e?e.length<6?"Password must be at least 6 characters long":void 0:"Password is required",[]),o=(0,r.useCallback)(e=>{let s={},r=a(e.email),i=n(e.password);return r&&(s.email=r),i&&(s.password=i),t(s),{isValid:0===Object.keys(s).length,errors:s}},[a,n]),l=(0,r.useCallback)((e,s)=>{let r;switch(e){case"email":r=a(s);break;case"password":r=n(s);break;default:return}t(t=>({...t,[e]:r}))},[a,n]),c=(0,r.useCallback)(e=>{t(t=>{let s={...t};return delete s[e],s})},[]),d=(0,r.useCallback)(()=>{t({})},[]),u=(0,r.useCallback)(()=>{i(!0)},[]),m=(0,r.useCallback)(()=>{t({}),i(!1)},[]),h=(0,r.useCallback)((t,r)=>s&&r&&!e[t],[e,s]);return{errors:e,isFormTouched:s,validateForm:o,validateField:l,clearFieldError:c,clearAllErrors:d,markFormTouched:u,resetValidation:m,isFieldValid:h}}},10212:(e,t,s)=>{"use strict";s.d(t,{Q:()=>n});var r=s(52765),i=s(77312);let a={fromApi:e=>r.A.fromApi(e),toApi:e=>e};class n extends i.v{constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/employees",this.transformer=a}async getByRole(e){return(await this.getAll({role:e})).data}async updateAvailabilityStatus(e,t){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.patch(`${this.endpoint}/${e}/availability`,{availability:t});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.A.fromApi(s)})}}},11734:(e,t,s)=>{"use strict";s.d(t,{B:()=>r});class r{static{this.ROLE_HIERARCHY={READONLY:0,USER:1,MANAGER:2,ADMIN:3,SUPER_ADMIN:4}}static{this.ROLE_PERMISSIONS={READONLY:["read","employees:read","vehicles:read","delegations:read","tasks:read","reports:read"],USER:["read","employees:read","vehicles:read","delegations:read","tasks:read","reports:read","settings:read"],MANAGER:["read","write","employees:read","employees:write","vehicles:read","vehicles:write","delegations:read","delegations:write","tasks:read","tasks:write","reports:read","reports:write","settings:read"],ADMIN:["read","write","delete","employees:read","employees:write","employees:delete","vehicles:read","vehicles:write","vehicles:delete","delegations:read","delegations:write","delegations:delete","tasks:read","tasks:write","tasks:delete","reports:read","reports:write","settings:read","settings:write"],SUPER_ADMIN:["read","write","delete","admin","employees:read","employees:write","employees:delete","employees:admin","vehicles:read","vehicles:write","vehicles:delete","vehicles:admin","delegations:read","delegations:write","delegations:delete","delegations:admin","tasks:read","tasks:write","tasks:delete","tasks:admin","reports:read","reports:write","reports:admin","settings:read","settings:write","settings:admin","system:admin","system:audit","system:backup"]}}static hasPermission(e,t){let s=this.normalizeRole(e);return s?this.ROLE_PERMISSIONS[s].includes(t)||this.checkHierarchicalPermission(s,t)?{hasPermission:!0}:{hasPermission:!1,reason:`Access denied. Required role: ${s||"Unknown"}`,requiredRole:s||"USER"}:{hasPermission:!1,reason:"Invalid user role"}}static hasMinimumRole(e,t){let s=this.normalizeRole(e);return!!s&&this.ROLE_HIERARCHY[s]>=this.ROLE_HIERARCHY[t]}static getPermissionsForRole(e){let t=this.normalizeRole(e);return t?[...this.ROLE_PERMISSIONS[t]]:[]}static hasAllPermissions(e,t){for(let s of t){let t=this.hasPermission(e,s);if(!t.hasPermission)return{hasPermission:!1,reason:`Missing permission: ${s}`,requiredRole:t.requiredRole||"USER"}}return{hasPermission:!0}}static hasAnyPermission(e,t){for(let s of t)if(this.hasPermission(e,s).hasPermission)return{hasPermission:!0};return{hasPermission:!1,reason:`None of the required permissions found: ${t.join(", ")}`}}static normalizeRole(e){if(!e||"string"!=typeof e)return null;let t=e.toUpperCase();return Object.keys(this.ROLE_HIERARCHY).includes(t)?t:null}static checkHierarchicalPermission(e,t){let s=this.ROLE_HIERARCHY[e];for(let[e,r]of Object.entries(this.ROLE_HIERARCHY))if(r>s&&this.ROLE_PERMISSIONS[e].includes(t))break;return!1}static getMinimumRoleForPermission(e){for(let[t,s]of Object.entries(this.ROLE_PERMISSIONS))if(s.includes(e))return t}}},16398:(e,t,s)=>{"use strict";s.d(t,{vz:()=>r});var r=function(e){return e.CLOSED="CLOSED",e.HALF_OPEN="HALF_OPEN",e.OPEN="OPEN",e}(r||{});class i{constructor(e={}){this.failureCount=0,this.lastFailureTime=0,this.state="CLOSED",this.successCount=0,this.options={failureThreshold:5,isFailure:e=>e?.status===429||e?.status>=500&&e?.status<600,recoveryTimeout:6e4,requestTimeout:1e4,...e}}async execute(e){if("OPEN"===this.state)if(this.shouldAttemptReset())this.state="HALF_OPEN",this.successCount=0,console.log("\uD83D\uDD04 Circuit Breaker: Attempting to reset (HALF_OPEN)");else{let e=Math.round((this.lastFailureTime+this.options.recoveryTimeout-Date.now())/1e3);throw Error(`Circuit breaker is OPEN. Retry in ${e} seconds.`)}try{let t=await this.executeWithTimeout(e);return this.onSuccess(),t}catch(e){throw this.onFailure(e),e}}getStatus(){let e={failureCount:this.failureCount,lastFailureTime:this.lastFailureTime,state:this.state};if("OPEN"===this.state){let t=Math.max(0,this.lastFailureTime+this.options.recoveryTimeout-Date.now());return{...e,timeUntilRetry:t}}return e}reset(){this.state="CLOSED",this.failureCount=0,this.lastFailureTime=0,this.successCount=0,console.log("\uD83D\uDD04 Circuit Breaker: Manually reset (CLOSED)")}async executeWithTimeout(e){return new Promise((t,s)=>{let r=setTimeout(()=>{s(Error("Request timeout"))},this.options.requestTimeout);e().then(t).catch(s).finally(()=>clearTimeout(r))})}onFailure(e){this.options.isFailure(e)&&(this.failureCount++,this.lastFailureTime=Date.now(),"HALF_OPEN"===this.state?(this.state="OPEN",console.log("\uD83D\uDD04 Circuit Breaker: Reset failed, opening circuit (OPEN)")):this.failureCount>=this.options.failureThreshold&&(this.state="OPEN",console.log(`🔄 Circuit Breaker: Failure threshold reached (${this.failureCount}), opening circuit (OPEN)`)))}onSuccess(){this.failureCount=0,"HALF_OPEN"===this.state&&(this.successCount++,this.successCount>=3&&(this.state="CLOSED",console.log("\uD83D\uDD04 Circuit Breaker: Reset successful (CLOSED)")))}shouldAttemptReset(){return Date.now()-this.lastFailureTime>=this.options.recoveryTimeout}}new i({failureThreshold:3,recoveryTimeout:6e4,requestTimeout:8e3}),new i({failureThreshold:5,recoveryTimeout:3e4,requestTimeout:8e3}),new i({failureThreshold:3,recoveryTimeout:3e4,requestTimeout:5e3}),new i({failureThreshold:3,recoveryTimeout:6e4,requestTimeout:1e4}),new i({failureThreshold:3,recoveryTimeout:6e4,requestTimeout:1e4})},16467:(e,t,s)=>{"use strict";var r=s(77312);r.v},17839:(e,t,s)=>{"use strict";s.d(t,{DC:()=>c,qQ:()=>l});var r=s(55738),i=s(66366),a=s(36391),n=s(63924);let o=[],l=new r.E({defaultOptions:{mutations:{retry:0,retryDelay:1e3},queries:{gcTime:6e5,refetchInterval:!1,refetchOnMount:!0,refetchOnReconnect:!0,refetchOnWindowFocus:!0,retry:(e,t)=>!(t instanceof n.v3)&&!(t instanceof n.Dr)&&(!(t instanceof n.hD)||400!==t.status&&401!==t.status&&404!==t.status)&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:3e5}},mutationCache:new i.q({onError:e=>{e instanceof n.v3?console.error("Mutation Authentication Error:",e.message):e instanceof n.Dr?console.error("Mutation Network Error:",e.message):e instanceof n.hD?console.error(`Mutation API Error (${e.status}):`,e.message,e.details):console.error("An unexpected mutation error occurred:",e)}}),queryCache:new a.$({onError:e=>{e instanceof n.v3?console.error("Authentication Error:",e.message):e instanceof n.Dr?console.error("Network Error:",e.message):e instanceof n.hD?console.error(`API Error (${e.status}):`,e.message,e.details):console.error("An unexpected error occurred:",e)},onSuccess:(e,t)=>{let s=JSON.stringify(t.queryKey),r=Date.now()-(t.state.dataUpdatedAt||Date.now()),i="fetching"!==t.state.fetchStatus&&void 0!==t.state.data;o.push({cacheHit:i,duration:r,queryKey:s,timestamp:Date.now()}),o.length>1e3&&o.splice(0,o.length-1e3)}})}),c={prefetchDashboardData:async(e=!1)=>{if(!e)return void console.warn("Authentication not ready, deferring dashboard data prefetch.");let{getGlobalAuthTokenProvider:t}=await Promise.resolve().then(s.bind(s,8342)),r=t();if(!r||!r())return void console.warn("No auth token available, skipping dashboard data prefetch.");let{delegationApiService:i,employeeApiService:a,taskApiService:n,vehicleApiService:o}=await s.e(9603).then(s.bind(s,49603)),c=[l.prefetchQuery({queryFn:()=>o.getAll(),queryKey:["vehicles"],staleTime:6e5}),l.prefetchQuery({queryFn:()=>n.getAll(),queryKey:["tasks"],staleTime:3e5}),l.prefetchQuery({queryFn:()=>a.getAll(),queryKey:["employees"],staleTime:6e5}),l.prefetchQuery({queryFn:()=>i.getAll(),queryKey:["delegations"],staleTime:3e5})];await Promise.allSettled(c)},prefetchTaskManagementData:async(e=!0)=>{if(!e)return void console.warn("Authentication not ready, deferring task management data prefetch.");let{employeeApiService:t,taskApiService:r,vehicleApiService:i}=await s.e(9603).then(s.bind(s,49603)),a=[l.prefetchQuery({queryFn:()=>r.getAll(),queryKey:["tasks"],staleTime:3e5}),l.prefetchQuery({queryFn:()=>t.getAll(),queryKey:["employees"],staleTime:6e5}),l.prefetchQuery({queryFn:()=>i.getAll(),queryKey:["vehicles"],staleTime:6e5})];await Promise.allSettled(a)},prefetchVehicleDetails:async(e,t=!0)=>{if(!t)return void console.warn("Authentication not ready, deferring vehicle details prefetch.");let{vehicleApiService:r}=await s.e(9603).then(s.bind(s,49603));await l.prefetchQuery({queryFn:()=>r.getById(e),queryKey:["vehicles",e],staleTime:6e5})}}},21342:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>m,_2:()=>h,hO:()=>p,lp:()=>f,mB:()=>g,rI:()=>d,ty:()=>u});var r=s(60687),i=s(43210),a=s(26312),n=s(74158),o=s(58450),l=s(73256),c=s(22482);let d=a.bL,u=a.l9;a.YJ,a.ZL,a.Pb,a.z6,i.forwardRef(({className:e,inset:t,children:s,...i},o)=>(0,r.jsxs)(a.ZP,{ref:o,className:(0,c.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...i,children:[s,(0,r.jsx)(n.A,{className:"ml-auto"})]})).displayName=a.ZP.displayName,i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.G5,{ref:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",e),...t})).displayName=a.G5.displayName;let m=i.forwardRef(({className:e,sideOffset:t=4,...s},i)=>(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{ref:i,sideOffset:t,className:(0,c.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",e),...s})}));m.displayName=a.UC.displayName;let h=i.forwardRef(({className:e,inset:t,...s},i)=>(0,r.jsx)(a.q7,{ref:i,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...s}));h.displayName=a.q7.displayName;let p=i.forwardRef(({className:e,children:t,checked:s,...i},n)=>(0,r.jsxs)(a.H_,{ref:n,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...void 0!==s&&{checked:s},...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),t]}));p.displayName=a.H_.displayName,i.forwardRef(({className:e,children:t,...s},i)=>(0,r.jsxs)(a.hN,{ref:i,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=a.hN.displayName;let f=i.forwardRef(({className:e,inset:t,...s},i)=>(0,r.jsx)(a.JU,{ref:i,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...s}));f.displayName=a.JU.displayName;let g=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));g.displayName=a.wv.displayName},22364:(e,t,s)=>{"use strict";s.d(t,{og:()=>n,tL:()=>o});var r=s(84430),i=s.n(r);let a=(e,t)=>{let s=URL.createObjectURL(e),r=document.createElement("a");r.href=s,r.download=t,document.body.append(r),r.click(),r.remove(),URL.revokeObjectURL(s)},n=async(e,t,s)=>{s.toLowerCase().endsWith(".csv")||(s+=".csv");let r=[t,...e];a(new Blob([i().unparse(r)],{type:"text/csv;charset=utf-8;"}),s)},o=e=>{let t=document.querySelector(e);if(!t)throw Error(`Table element not found: ${e}`);let s=t.querySelector("thead tr");if(!s)throw Error("Table header row not found");let r=[];for(let e of s.querySelectorAll("th"))Object.hasOwn(e.dataset,"skipExport")||r.push(e.textContent?.trim()||"");let i=[];for(let e of t.querySelectorAll("tbody tr")){let t=[],r=0;for(let i of e.querySelectorAll("td")){let e=s.querySelectorAll("th")[r];if(r++,e&&!Object.hasOwn(e.dataset,"skipExport")){let e=i.dataset.exportValue||i.textContent?.trim()||"";t.push(e)}}i.push(t)}return{data:i,headers:r}}},22482:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});let r={backoffFactor:2,initialDelay:300,maxDelay:5e3,maxRetries:3,shouldRetry:e=>!e.response||e.response.status>=500&&e.response.status<600};s(65594),s(48184),s(22364),s(16398),s(59514);var i=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,i.$)(e))}},23133:(e,t,s)=>{"use strict";s.d(t,{Qq:()=>i});let r=function(){let{apiUrl:e,apiBaseUrl:t,wsUrl:s}={apiUrl:"http://localhost:3001",apiBaseUrl:"http://localhost:3001/api",wsUrl:"ws://localhost:3001"};return{apiUrl:e,apiBaseUrl:t,wsUrl:s,environment:"production",deploymentContext:"localhost",isProduction:!0,isDevelopment:!1,enableDebugLogging:!0}}();function i(){return r}},26461:(e,t,s)=>{"use strict";s.d(t,{SessionManager:()=>n});var r=s(23133);let i={MAX_CONCURRENT_SESSIONS:5,SESSION_TIMEOUT_MINUTES:30},a={CROSS_TAB_LOGOUT:"cross_tab_logout",SESSION_INVALID:"session_invalid",SESSION_TIMEOUT:"session_timeout",SESSION_VALIDATED:"session_validated",TOKEN_REFRESH_FAILED:"token_refresh_failed",TOKEN_REFRESH_SUCCESS:"token_refresh_success"};class n{static{this.activityListeners=[]}static{this.BROADCAST_CHANNEL_NAME="workhub_session_events"}static{this.broadcastChannel=null}static{this.sessionCheckInterval=null}static{this.STORAGE_KEYS={CONCURRENT_SESSIONS:"workhub_concurrent_sessions",LAST_ACTIVITY:"workhub_last_activity",SESSION_ID:"workhub_session_id",SESSION_STATE:"workhub_session_state"}}static addSessionEventListener(e){if(void 0===globalThis.window)return()=>{};let t=t=>{e(t.data)};return this.broadcastChannel?.addEventListener("message",t),()=>{this.broadcastChannel?.removeEventListener("message",t)}}static cleanup(){for(let e of(this.sessionCheckInterval&&(clearInterval(this.sessionCheckInterval),this.sessionCheckInterval=null),this.broadcastChannel?.close(),this.broadcastChannel=null,this.activityListeners))e();this.activityListeners=[]}static clearSessionState(){if(void 0!==globalThis.window)for(let e of Object.values(this.STORAGE_KEYS))localStorage.removeItem(e)}static detectAndResolveConflicts(){if(void 0===globalThis.window)return!0;try{let e=this.getSessionState(),t=this.getLastActivity(),s=this.getConcurrentSessions();if(e&&t&&Math.abs(e.lastActivity.getTime()-t.getTime())>3e5){console.warn("⚠️ Session timestamp conflict detected, resolving...");let s=e.lastActivity.getTime()>t.getTime()?e.lastActivity:t;localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY,s.toISOString()),this.setSessionState({...e,lastActivity:s}),console.log("✅ Session timestamp conflict resolved")}let r=this.getCurrentSessionId(),i=s.filter(e=>e.sessionId===r);if(i.length>1){console.warn("⚠️ Duplicate session entries detected, cleaning up...");let e=i.reduce((e,t)=>t.lastActivity>e.lastActivity?t:e),t=s.filter(e=>e.sessionId!==r);t.push(e),this.setConcurrentSessions(t),console.log("✅ Duplicate sessions cleaned up")}return!0}catch(e){return console.error("Failed to detect and resolve conflicts:",e),!1}}static detectTimeout(){if(void 0===globalThis.window)return!1;let e=this.getLastActivity();if(!e)return!0;let t=60*i.SESSION_TIMEOUT_MINUTES*1e3;return Date.now()-e.getTime()>t}static getCurrentSessionId(){if(void 0===globalThis.window)return"";let e=localStorage.getItem(this.STORAGE_KEYS.SESSION_ID);return e||(e=this.generateSessionId(),localStorage.setItem(this.STORAGE_KEYS.SESSION_ID,e)),e}static getSessionState(){if(void 0===globalThis.window)return null;try{let e=localStorage.getItem(this.STORAGE_KEYS.SESSION_STATE);if(!e)return null;let t=JSON.parse(e);return{...t,expiresAt:new Date(t.expiresAt),lastActivity:new Date(t.lastActivity)}}catch{return null}}static handleCrossTabLogout(){if(void 0===globalThis.window)return;let e={sessionId:this.getCurrentSessionId(),timestamp:new Date,type:a.CROSS_TAB_LOGOUT};this.broadcastSessionEvent(e),this.clearSessionState()}static handleSessionValidation(e,t){if(void 0===globalThis.window)return;let s={data:t,sessionId:this.getCurrentSessionId(),timestamp:new Date,type:e?a.SESSION_VALIDATED:a.SESSION_INVALID};this.broadcastSessionEvent(s),e?this.updateActivity():this.clearSessionState()}static handleTokenRefresh(e,t){if(void 0===globalThis.window)return;let s={data:t,sessionId:this.getCurrentSessionId(),timestamp:new Date,type:e?a.TOKEN_REFRESH_SUCCESS:a.TOKEN_REFRESH_FAILED};if(this.broadcastSessionEvent(s),e){this.updateActivity();let e=this.getSessionState();if(e){let t=new Date,s=new Date(t.getTime()+60*i.SESSION_TIMEOUT_MINUTES*1e3);this.setSessionState({...e,expiresAt:s,lastActivity:t})}}}static initialize(){void 0!==globalThis.window&&(this.initializeBroadcastChannel(),this.startSessionMonitoring(),this.setupActivityTracking(),this.initializeSessionState())}static manageConcurrentSessions(){if(void 0===globalThis.window)return;let e=this.getConcurrentSessions(),t=this.getCurrentSessionId();if(!e.find(e=>e.sessionId===t)){let s={lastActivity:new Date,sessionId:t,startTime:new Date,userAgent:navigator.userAgent};e.push(s)}let s=e.find(e=>e.sessionId===t);s&&(s.lastActivity=new Date);let r=e.filter(e=>{let t=60*i.SESSION_TIMEOUT_MINUTES*1e3;return Date.now()-e.lastActivity.getTime()<=t});r.length>i.MAX_CONCURRENT_SESSIONS&&(r.sort((e,t)=>t.lastActivity.getTime()-e.lastActivity.getTime()),r.splice(i.MAX_CONCURRENT_SESSIONS)),this.setConcurrentSessions(r)}static async performIntegrityCheck(){if(void 0===globalThis.window)return!0;try{if(!this.validateSessionConsistency())return console.warn("\uD83D\uDCCA Local session state is inconsistent"),!1;if(!await this.validateWithBackend())return console.warn("\uD83D\uDD17 Backend session validation failed"),!1;return this.cleanupStaleSessions(),console.log("✅ Session integrity check passed"),!0}catch(e){return console.error("❌ Session integrity check failed:",e),!1}}static recoverFromCorruptedState(){if(void 0===globalThis.window)return!0;try{if(console.log("\uD83D\uDD27 Attempting session state recovery..."),this.validateSessionConsistency())return console.log("✅ Session state is already consistent"),!0;let e=this.preserveNonSecurityData();return this.clearSessionState(),this.restorePreservedData(e),this.initializeSessionState(),console.log("✅ Session state recovery completed"),!0}catch(e){return console.error("❌ Session state recovery failed:",e),!1}}static setSessionState(e){if(void 0!==globalThis.window)try{localStorage.setItem(this.STORAGE_KEYS.SESSION_STATE,JSON.stringify(e))}catch(e){console.error("Failed to set session state:",e)}}static updateActivity(){if(void 0===globalThis.window)return;let e=new Date;localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY,e.toISOString()),this.manageConcurrentSessions()}static validateSessionConsistency(){if(void 0===globalThis.window)return!0;try{let e=this.getSessionState(),t=this.getLastActivity(),s=this.getCurrentSessionId();if(e&&!t)return console.warn("\uD83D\uDD0D Session state exists but no last activity found"),!1;if(t&&!s)return console.warn("\uD83D\uDD0D Last activity exists but no session ID found"),!1;if(e&&e.sessionId!==s)return console.warn("\uD83D\uDD0D Session state ID mismatch with current session ID"),!1;if(e&&e.expiresAt<new Date)return console.warn("\uD83D\uDD0D Session state has expired"),!1;return!0}catch(e){return console.error("Failed to validate session consistency:",e),!1}}static broadcastSessionEvent(e){this.broadcastChannel?.postMessage(e)}static cleanupStaleSessions(){try{let e=this.getConcurrentSessions(),t=Date.now(),s=60*i.SESSION_TIMEOUT_MINUTES*1e3,r=e.filter(e=>t-e.lastActivity.getTime()<=s);r.length!==e.length&&(console.log(`🧹 Cleaned up ${e.length-r.length} stale sessions`),this.setConcurrentSessions(r))}catch(e){console.warn("Failed to cleanup stale sessions:",e)}}static generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).slice(2,11)}`}static getConcurrentSessions(){try{let e=localStorage.getItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS);if(!e)return[];return JSON.parse(e).map(e=>({...e,lastActivity:new Date(e.lastActivity),startTime:new Date(e.startTime)}))}catch{return[]}}static getLastActivity(){let e=localStorage.getItem(this.STORAGE_KEYS.LAST_ACTIVITY);return e?new Date(e):null}static handleSessionTimeout(){let e={sessionId:this.getCurrentSessionId(),timestamp:new Date,type:a.SESSION_TIMEOUT};this.broadcastSessionEvent(e),this.clearSessionState()}static initializeBroadcastChannel(){"undefined"!=typeof BroadcastChannel&&(this.broadcastChannel=new BroadcastChannel(this.BROADCAST_CHANNEL_NAME))}static initializeSessionState(){let e=this.getCurrentSessionId(),t=new Date,s=new Date(t.getTime()+60*i.SESSION_TIMEOUT_MINUTES*1e3);this.setSessionState({expiresAt:s,isActive:!0,lastActivity:t,sessionId:e}),this.updateActivity()}static preserveNonSecurityData(){let e={};for(let t of["workhub-app-store","workhub_user_preferences"])try{e[t]=localStorage.getItem(t)}catch(e){console.warn(`Failed to preserve data for key ${t}:`,e)}return e}static restorePreservedData(e){for(let[t,s]of Object.entries(e))if(null!==s)try{localStorage.setItem(t,s)}catch(e){console.warn(`Failed to restore data for key ${t}:`,e)}}static setConcurrentSessions(e){try{localStorage.setItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS,JSON.stringify(e))}catch(e){console.error("Failed to set concurrent sessions:",e)}}static setupActivityTracking(){let e=()=>{this.updateActivity()};for(let t of["mousedown","mousemove","keypress","scroll","touchstart","click"])document.addEventListener(t,e,{passive:!0}),this.activityListeners.push(()=>{document.removeEventListener(t,e)})}static startSessionMonitoring(){this.sessionCheckInterval=setInterval(()=>{this.detectTimeout()&&this.handleSessionTimeout()},6e4)}static async validateWithBackend(){try{let e=(0,r.Qq)().apiBaseUrl;await new Promise(e=>setTimeout(e,200));let t=await fetch(`${e}/health`,{method:"GET",headers:{"Content-Type":"application/json"},cache:"no-cache"});if(t.ok)return console.log("✅ Backend connectivity validation successful"),!0;return console.log(`🔍 Backend connectivity check failed with status: ${t.status}`),!1}catch(e){return console.warn("Backend validation failed:",e),!0}}}},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>l});var r=s(60687),i=s(43210),a=s(8730),n=s(24224),o=s(22482);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef(({className:e,variant:t,size:s,asChild:i=!1,...n},c)=>{let d=i?a.DX:"button";return(0,r.jsx)(d,{className:(0,o.cn)(l({variant:t,size:s,className:e})),ref:c,...n})});c.displayName="Button"},30887:(e,t,s)=>{"use strict";s.d(t,{CSPProvider:()=>i});var r=s(12907);let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call CSPProvider() from the server but CSPProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\lib\\security\\CSPProvider.tsx","CSPProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useCSP() from the server but useCSP is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\lib\\security\\CSPProvider.tsx","useCSP"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useNonce() from the server but useNonce is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\lib\\security\\CSPProvider.tsx","useNonce"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useCSPReporting() from the server but useCSPReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\lib\\security\\CSPProvider.tsx","useCSPReporting"),(0,r.registerClientReference)(function(){throw Error("Attempted to call createSecureScriptProps() from the server but createSecureScriptProps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\lib\\security\\CSPProvider.tsx","createSecureScriptProps"),(0,r.registerClientReference)(function(){throw Error("Attempted to call initializeCSPViolationReporting() from the server but initializeCSPViolationReporting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\lib\\security\\CSPProvider.tsx","initializeCSPViolationReporting")},30930:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});var r=s(77312),i=s(48839);let a={fromApi:e=>i.M.fromApi(e),toApi:e=>e};class n extends r.v{constructor(e,t){super(e,{cacheDuration:6e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/vehicles",this.transformer=a}async getAvailableVehicles(e,t){return(await this.getAll({available:!0,endDate:t.toISOString(),startDate:e.toISOString()})).data}async getByStatus(e){return(await this.getAll({status:e})).data}}},32584:(e,t,s)=>{"use strict";s.d(t,{BK:()=>l,eu:()=>o,q5:()=>c});var r=s(60687),i=s(11096),a=s(43210),n=s(22482);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.bL,{className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),ref:s,...t}));o.displayName=i.bL.displayName;let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i._V,{className:(0,n.cn)("aspect-square h-full w-full",e),ref:s,...t}));l.displayName=i._V.displayName;let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.H4,{className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),ref:s,...t}));c.displayName=i.H4.displayName},33938:(e,t,s)=>{"use strict";s.d(t,{LG:()=>f});var r=s(60687),i=s(29848),a=s(10453),n=s(5600),o=s(88853),l=s(58450),c=s(10218);s(43210);var d=s(96834),u=s(29523),m=s(44493),h=s(41181);let p=[{description:"Clean and bright interface",icon:i.A,label:"Light",preview:"bg-white border-gray-200 text-gray-900",value:"light"},{description:"Easy on the eyes in low light",icon:a.A,label:"Dark",preview:"bg-gray-900 border-gray-700 text-white",value:"dark"},{description:"Follows your device settings",icon:n.A,label:"System",preview:"bg-gradient-to-r from-white to-gray-900 border-gray-400 text-gray-600",value:"system"}],f=()=>{let{currentTheme:e}=(0,h.useTheme)(),{setTheme:t,systemTheme:s,theme:i}=(0,c.D)(),{setTheme:a}=(0,h.useTheme)(),n=e=>{t(e),"system"===e?a(s||"light"):a(e)},f=i||"system";return(0,r.jsxs)(m.Zp,{className:"w-full max-w-2xl",children:[(0,r.jsxs)(m.aR,{children:[(0,r.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"size-5"}),"Theme Preferences"]}),(0,r.jsx)(m.BT,{children:"Choose your preferred color scheme and appearance settings"})]}),(0,r.jsxs)(m.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-muted/50 p-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Current Theme"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Applied across the entire application"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.E,{className:"capitalize",variant:"secondary",children:f}),"system"===f&&s&&(0,r.jsxs)(d.E,{className:"text-xs",variant:"outline",children:["System: ",s]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Available Themes"}),p.map(e=>{let t=e.icon,s=f===e.value;return(0,r.jsx)("div",{className:`
                  relative cursor-pointer rounded-lg border p-4 transition-all
                  ${s?"border-primary bg-primary/5 ring-1 ring-primary/20":"border-border hover:border-primary/50 hover:bg-muted/30"}
                `,onClick:()=>n(e.value),children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex flex-1 items-start gap-3",children:[(0,r.jsx)("div",{className:"flex size-10 items-center justify-center rounded-lg border bg-background",children:(0,r.jsx)(t,{className:"size-5"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"mb-1 flex items-center gap-2",children:[(0,r.jsx)("h5",{className:"font-medium",children:e.label}),s&&(0,r.jsx)(l.A,{className:"size-4 text-primary"})]}),(0,r.jsx)("p",{className:"mb-3 text-sm text-muted-foreground",children:e.description}),(0,r.jsx)("div",{className:`
                        h-8 w-full rounded border-2 ${e.preview}
                        flex items-center justify-center text-xs font-medium
                      `,children:"Preview"})]})]})})},e.value)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[(0,r.jsx)("div",{className:"flex gap-2",children:p.map(e=>{let t=e.icon;return(0,r.jsxs)(u.$,{className:"flex items-center gap-2",onClick:()=>n(e.value),size:"sm",variant:f===e.value?"default":"outline",children:[(0,r.jsx)(t,{className:"size-4"}),e.label]},e.value)})}),(0,r.jsx)(u.$,{className:"text-muted-foreground",onClick:()=>n("system"),size:"sm",variant:"ghost",children:"Reset to System"})]}),(0,r.jsxs)("div",{className:"rounded-lg border bg-background p-4",children:[(0,r.jsx)("h5",{className:"mb-2 font-medium",children:"Theme Information"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Selected Theme:"}),(0,r.jsx)("span",{className:"font-medium capitalize",children:f})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Zustand Store:"}),(0,r.jsx)("span",{className:"font-medium capitalize",children:e})]}),s&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"System Preference:"}),(0,r.jsx)("span",{className:"font-medium capitalize",children:s})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Auto-sync:"}),(0,r.jsx)("span",{className:"font-medium",children:"system"===f?"Enabled":"Disabled"})]})]})]})]})]})}},34570:(e,t,s)=>{"use strict";s.d(t,{ProtectedRoute:()=>u});var r=s(60687),i=s(11516),a=s(14975),n=s(53597);s(43210);var o=s(63213),l=s(91821),c=s(44493),d=s(55387);function u({allowedRoles:e=[],children:t,fallback:s,requireEmailVerification:u=!0}){let{error:m,loading:h,session:p,user:f,userRole:g}=(0,o.useAuthContext)();if(h)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:(0,r.jsx)(c.Zp,{className:"mx-auto w-full max-w-md",children:(0,r.jsxs)(c.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,r.jsx)(i.A,{className:"mb-4 size-8 animate-spin text-blue-600"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Verifying security credentials..."})]})})});if(m&&!f)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,r.jsxs)(c.Zp,{className:"mx-auto w-full max-w-md",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"flex items-center text-red-600",children:[(0,r.jsx)(a.A,{className:"mr-2 size-5"}),"Authentication Error"]}),(0,r.jsx)(c.BT,{children:"There was a problem with the security system"})]}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsx)(l.Fc,{variant:"destructive",children:(0,r.jsx)(l.TN,{children:m})}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(d.LoginForm,{})})]})]})});if(!f||!p)return s?(0,r.jsx)(r.Fragment,{children:s}):(0,r.jsx)(d.LoginForm,{onSuccess:()=>{globalThis.location.href="/"}});if(u&&!f.email_confirmed_at)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,r.jsxs)(c.Zp,{className:"mx-auto w-full max-w-md",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"flex items-center text-yellow-600",children:[(0,r.jsx)(n.A,{className:"mr-2 size-5"}),"Email Verification Required"]}),(0,r.jsx)(c.BT,{children:"Please verify your email address to continue"})]}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsxs)(l.Fc,{children:[(0,r.jsx)(a.A,{className:"size-4"}),(0,r.jsxs)(l.TN,{children:["We've sent a verification email to ",(0,r.jsx)("strong",{children:f.email}),". Please check your inbox and click the verification link to access the system."]})]}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Didn't receive the email? Check your spam folder or contact your administrator."})})]})]})});if(e.length>0){let t=g||"USER";if(!e.includes(t))return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,r.jsxs)(c.Zp,{className:"mx-auto w-full max-w-md",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"flex items-center text-red-600",children:[(0,r.jsx)(n.A,{className:"mr-2 size-5"}),"Access Denied"]}),(0,r.jsx)(c.BT,{children:"Insufficient permissions to access this resource"})]}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsx)(l.Fc,{variant:"destructive",children:(0,r.jsxs)(l.TN,{children:["Your account (",t,") does not have permission to access this area. Required roles: ",e.join(", ")]})}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Contact your administrator if you believe this is an error."})})]})]})})}return(0,r.jsx)(r.Fragment,{children:t})}},34595:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l,viewport:()=>c});var r=s(37413),i=s(44999),a=s(30887),n=s(78311);async function o({children:e}){let t=await (0,i.b3)(),s=await (0,i.UL)(),o=t.get("x-nonce"),l=s.get("x-nonce")?.value;return(0,r.jsx)(a.CSPProvider,{nonce:o||l||null,children:(0,r.jsx)(n.default,{children:e})})}s(61135),s(99629);let l={authors:[{name:"WorkHub Team"}],description:"Professional delegation and task management platform",keywords:["delegation","task management","workflow","productivity"],openGraph:{description:"Professional delegation and task management platform",locale:"en_US",title:"WorkHub - Delegation Management System",type:"website"},robots:"index, follow",title:"WorkHub - Delegation Management System"},c={width:"device-width",initialScale:1};function d({children:e}){return(0,r.jsx)("html",{className:"h-full",lang:"en",suppressHydrationWarning:!0,children:(0,r.jsx)("body",{className:"flex min-h-screen flex-col font-sans antialiased",children:(0,r.jsx)(o,{children:e})})})}},35950:(e,t,s)=>{"use strict";s.d(t,{w:()=>o});var r=s(60687),i=s(62369),a=s(43210),n=s(22482);let o=a.forwardRef(({className:e,decorative:t=!0,orientation:s="horizontal",...a},o)=>(0,r.jsx)(i.b,{className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),decorative:t,orientation:s,ref:o,...a}));o.displayName=i.b.displayName},38118:(e,t,s)=>{"use strict";function r(e){return void 0===e?null:e}s.d(t,{d$:()=>r})},39727:()=>{},39965:(e,t,s)=>{"use strict";s.d(t,{KW:()=>d,sf:()=>c});var r=s(60687),i=s(43210),a=s(23133),n=s(51528);let o=(()=>{let e=(0,a.Qq)();return{csrf:{enabled:!0,tokenHeader:"X-CSRF-Token",excludePaths:["/api/health","/api/status"]},tokenValidation:{enabled:!0,refreshThreshold:60*n.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES,autoRefresh:!0},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},authentication:{enabled:!0,autoLogout:!0,redirectOnFailure:!0},http:{baseURL:e.apiBaseUrl,timeout:1e4,retryAttempts:3}}})(),l=(0,i.createContext)(null);function c({children:e,initialConfig:t={},configVersion:s="1.0.0",onConfigChange:a,validateConfig:n=!0}){let c=(0,i.useMemo)(()=>({...o,...t,csrf:{...o.csrf,...t.csrf},tokenValidation:{...o.tokenValidation,...t.tokenValidation},inputSanitization:{...o.inputSanitization,...t.inputSanitization},authentication:{...o.authentication,...t.authentication},http:{...o.http,...t.http}}),[t]),d=(0,i.useMemo)(()=>{if(!n)return!0;try{if(!c.http.baseURL||c.http.timeout<=0||c.http.retryAttempts<0||c.tokenValidation.refreshThreshold<=0||c.csrf.enabled&&!c.csrf.tokenHeader||c.inputSanitization.enabled&&0===c.inputSanitization.sanitizers.length)return!1;return!0}catch(e){return console.error("SecurityConfigProvider: Configuration validation failed:",e),!1}},[c,n]),u=(0,i.useMemo)(()=>e=>{let t={...c,...e,csrf:{...c.csrf,...e.csrf},tokenValidation:{...c.tokenValidation,...e.tokenValidation},inputSanitization:{...c.inputSanitization,...e.inputSanitization},authentication:{...c.authentication,...e.authentication},http:{...c.http,...e.http}};a?.(t)},[c,a]),m=(0,i.useMemo)(()=>()=>{a?.(o)},[a]),h=(0,i.useMemo)(()=>({config:c,updateConfig:u,resetConfig:m,isConfigValid:d,configVersion:s}),[c,u,m,d,s]);return(0,r.jsx)(l.Provider,{value:h,children:e})}function d(){let e=(0,i.useContext)(l);if(!e)throw Error("useSecurityConfig must be used within a SecurityConfigProvider");return e}},41181:(e,t,s)=>{"use strict";s.r(t),s.d(t,{useTheme:()=>a});var r=s(43210),i=s(94538);let a=()=>{let e=(0,i.C)(e=>e.currentTheme),t=(0,i.C)(e=>e.setTheme),s=(0,r.useCallback)(()=>{t("light"===e?"dark":"light")},[e,t]),a="dark"===e,n="light"===e,o=(0,r.useCallback)(()=>{t("light")},[t]),l=(0,r.useCallback)(()=>{t("dark")},[t]),c=(0,r.useCallback)(()=>({background:a?"bg-gray-900":"bg-white",border:a?"border-gray-700":"border-gray-200",isDark:a,isLight:n,root:e,text:a?"text-white":"text-gray-900"}),[e,a,n]);return{currentTheme:e,getThemeClasses:c,isDark:a,isLight:n,setDarkTheme:l,setLightTheme:o,setTheme:t,toggleTheme:s}}},41674:(e,t,s)=>{"use strict";s.d(t,{CSPProvider:()=>n,s5:()=>d,oD:()=>c,dD:()=>l});var r=s(60687),i=s(43210);let a=(0,i.createContext)({nonce:null,isStrictCSP:!1,violationCount:0,isNonceValid:!1,reportViolation:()=>{},getSecureNonce:()=>null,resetViolationCount:()=>{}});function n({children:e,nonce:t}){let[s,n]=(0,i.useState)(0),o=!!t&&function(e){let t=e.length>=16,s=/^[A-Za-z0-9+/]+=*$/.test(e),r=!/(.)\1{3,}/.test(e);return t&&s&&r}(t);return(0,r.jsx)(a.Provider,{value:{nonce:t,isStrictCSP:o&&!"production".includes("development"),violationCount:s,isNonceValid:o,reportViolation:e=>{n(e=>e+1)},getSecureNonce:()=>o?t:null,resetViolationCount:()=>{n(0)}},children:e})}function o(){let e=(0,i.useContext)(a);if(!e)throw Error("useCSP must be used within a CSPProvider");return e}function l(){let{nonce:e}=o();return e}function c(){let{reportViolation:e}=o();return e}function d(e){}},41995:(e,t,s)=>{"use strict";s.d(t,{G:()=>u});var r=s(43210),i=s(85969),a=s(63213),n=s(39965),o=s(43280),l=s(67079),c=s(88790),d=s(54995);function u(e={}){let{autoInitialize:t=!0,enableLogging:s=!0,onSecurityError:m,onSecurityStatusChange:h,...p}=e,{session:f,user:g,loading:x,signOut:y}=(0,a.useAuthContext)(),{config:v,isConfigValid:b}=(0,n.KW)(),S=(0,o.b)(),N=(0,l.C)(f?.access_token),j=(0,c.j8)(),E=(0,d.o)(),k=(0,r.useRef)(null),A=(0,r.useRef)(null),w=(0,r.useMemo)(()=>!!g&&!!f?.access_token&&!x,[g,f?.access_token,x]),T=(0,r.useMemo)(()=>w&&N.isTokenValid&&!N.isTokenExpired,[w,N.isTokenValid,N.isTokenExpired]),C=(0,r.useMemo)(()=>({csrfProtection:S,tokenManagement:N,inputValidation:j,sessionSecurity:E}),[S,N,j,E]),R=(0,r.useMemo)(()=>({baseURL:v.http.baseURL,timeout:v.http.timeout,retryAttempts:v.http.retryAttempts,getAuthToken:()=>f?.access_token||null,enableCSRF:v.csrf.enabled,enableInputSanitization:v.inputSanitization.enabled,enableTokenValidation:v.tokenValidation.enabled,enableAutoLogout:v.authentication.autoLogout,securityConfig:v,validateSecurityFeatures:!0,...p}),[v,f?.access_token,p]),I=(0,r.useMemo)(()=>{try{if(!b)throw Error("Invalid security configuration");let e=(0,i.z)(R,C);return t&&C&&e.initializeSecurity(C),k.current=e,A.current=null,s&&console.log("\uD83D\uDD10 useSecureHttpClient: Client initialized successfully",{isAuthenticated:w,hasValidToken:T,securityFeatures:Object.keys(C).filter(e=>C[e])}),e}catch(t){let e=t instanceof Error?t:Error("Failed to initialize secure client");throw A.current=e,m?.(e),console.error("useSecureHttpClient: Failed to initialize client:",e),e}},[R,C,t,b,w,T,s,m]),_=(0,r.useMemo)(()=>{try{return I?.getSecurityStatus()||null}catch(e){return console.warn("useSecureHttpClient: Failed to get security status:",e),null}},[I]),O=(0,r.useCallback)(()=>{try{I&&C&&(I.refreshSecurityFeatures(),s&&console.log("\uD83D\uDD04 useSecureHttpClient: Security features refreshed"))}catch(e){console.error("useSecureHttpClient: Failed to refresh security features:",e),m?.(e instanceof Error?e:Error("Refresh failed"))}},[I,C,s,m]),P=(0,r.useCallback)(e=>{try{I&&(I.updateSecurityConfig(e),s&&console.log("\uD83D\uDD27 useSecureHttpClient: Security configuration updated",e))}catch(e){console.error("useSecureHttpClient: Failed to update security config:",e),m?.(e instanceof Error?e:Error("Config update failed"))}},[I,s,m]),D=(0,r.useCallback)(e=>j.sanitizeInput(e),[j]);return{client:I,isAuthenticated:w,hasValidToken:T,securityStatus:_,refreshToken:(0,r.useCallback)(async()=>{try{return await N.refreshToken()}catch(e){return console.error("useSecureHttpClient: Token refresh failed:",e),m?.(e instanceof Error?e:Error("Token refresh failed")),!1}},[N,m]),refreshSecurityFeatures:O,updateSecurityConfig:P,sanitizeInput:D,isInitialized:!!I&&!A.current,isLoading:x,error:A.current}}},43280:(e,t,s)=>{"use strict";s.d(t,{b:()=>a});var r=s(43210),i=s(2143);function a(){let[e,t]=(0,r.useState)({csrfToken:null,isTokenValid:!1,tokenExpiresAt:null,isInitialized:!1}),s=(0,r.useCallback)(s=>{if(!i.k.isProtectionRequired(s))return s;let r=i.k.getCurrentToken();return r?(r.token!==e.csrfToken&&t(e=>({...e,csrfToken:r.token,isTokenValid:r.isValid,tokenExpiresAt:r.expiresAt})),i.k.attachToRequest(s)):(console.warn("No valid CSRF token available for request"),s)},[e.csrfToken]),a=(0,r.useCallback)(e=>i.k.validateToken(e),[]),n=(0,r.useCallback)(()=>{let e=i.k.refreshToken();return t(t=>({...t,csrfToken:e.token,isTokenValid:e.isValid,tokenExpiresAt:e.expiresAt})),e},[]),o=(0,r.useCallback)(()=>{i.k.clearToken(),t(e=>({...e,csrfToken:null,isTokenValid:!1,tokenExpiresAt:null}))},[]),l=(0,r.useCallback)(e=>i.k.isProtectionRequired(e),[]);return{csrfToken:e.csrfToken,isTokenValid:e.isTokenValid,tokenExpiresAt:e.tokenExpiresAt,isInitialized:e.isInitialized,attachCSRF:s,validateCSRF:a,refreshCSRFToken:n,clearCSRFToken:o,isProtectionRequired:l}}},43845:(e,t,s)=>{"use strict";var r=s(77312);r.v},43939:(e,t,s)=>{"use strict";s.d(t,{B:()=>i});var r=s(51528);class i{constructor(e,t={}){this.securityFeatures=e,this.config={csrf:{enabled:!0,tokenHeader:"X-CSRF-Token",excludePaths:[]},tokenValidation:{enabled:!0,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES,autoRefresh:!0},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},authentication:{enabled:!0,autoLogout:!0,redirectOnFailure:!0},...t}}async processRequest(e,t){let s={...e};try{if(this.config.authentication?.enabled&&this.securityFeatures.sessionSecurity&&!this.securityFeatures.sessionSecurity.isSessionActive)throw Error("Authentication required for secure API calls");if(this.config.tokenValidation?.enabled&&this.securityFeatures.tokenManagement){let{isTokenValid:e,isTokenExpired:t,refreshToken:s}=this.securityFeatures.tokenManagement;if(!e||t){if(console.log("\uD83D\uDD04 SecurityComposer: Token invalid/expired, attempting refresh..."),!await s())throw Error("Token refresh failed - authentication required");console.log("✅ SecurityComposer: Token refreshed successfully")}}if(this.config.inputSanitization?.enabled&&this.securityFeatures.inputValidation&&s.body){let{sanitizeInput:e}=this.securityFeatures.inputValidation;s.body=e(s.body),console.debug("\uD83E\uDDF9 SecurityComposer: Input sanitized using moved hooks")}if(this.config.csrf?.enabled&&this.securityFeatures.csrfProtection){let e=s.method?.toUpperCase();if(["POST","PUT","PATCH","DELETE"].includes(e||"")){let{attachCSRF:e}=this.securityFeatures.csrfProtection,t={url:s.url||"",method:s.method||"GET",headers:s.headers||{},body:s.body},r=e(t);s={...s,...r},console.debug("\uD83D\uDEE1️ SecurityComposer: CSRF protection applied using moved hooks")}}return s}catch(e){throw console.error("SecurityComposer: Error processing request:",e),e}}async handleError(e,t){try{this.config.authentication?.autoLogout&&this.securityFeatures.sessionSecurity&&e instanceof Error&&(e.message.includes("401")||e.message.includes("Authentication")||e.message.includes("Unauthorized"))&&(console.warn("\uD83D\uDD10 SecurityComposer: Authentication error detected, clearing session..."),this.securityFeatures.sessionSecurity.clearSession()),this.securityFeatures.sessionSecurity&&this.securityFeatures.sessionSecurity.updateActivity()}catch(e){console.error("SecurityComposer: Error in error handling:",e)}}getSecurityStatus(){return{isAuthenticated:this.securityFeatures.sessionSecurity?.isSessionActive??!1,hasValidToken:this.securityFeatures.tokenManagement?.isTokenValid??!1,sessionActive:this.securityFeatures.sessionSecurity?.isSessionActive??!1,securityFeaturesEnabled:this.config,securityFeaturesInitialized:!!(this.securityFeatures.csrfProtection||this.securityFeatures.tokenManagement||this.securityFeatures.inputValidation||this.securityFeatures.sessionSecurity)}}updateConfig(e){this.config={...this.config,...e}}updateSecurityFeatures(e){this.securityFeatures={...this.securityFeatures,...e}}}},44194:(e,t,s)=>{"use strict";s.d(t,{y:()=>o});var r=s(68983),i=s(77312);let a={fromApi:e=>r.G.fromApi(e),toApi:e=>e},n={toCreateRequest:e=>e};class o extends i.v{constructor(e,t){super(e,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/delegations",this.transformer=a}async getByStatus(e){return(await this.getAll({status:e})).data}async manageFlightDetails(e,t){return this.executeWithInfrastructure(null,async()=>{let s=n.toCreateRequest(t),i=await this.apiClient.patch(`${this.endpoint}/${e}/flight-details`,s);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.G.fromApi(i)})}async setDelegates(e,t){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.patch(`${this.endpoint}/${e}/delegates`,{delegates:t});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.G.fromApi(s)})}async setDrivers(e,t){return this.executeWithInfrastructure(null,async()=>{let s=t.map(e=>({employeeId:e})),i=await this.apiClient.patch(`${this.endpoint}/${e}/drivers`,{drivers:s});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.G.fromApi(i)})}async setEscorts(e,t){return this.executeWithInfrastructure(null,async()=>{let s=t.map(e=>({employeeId:e})),i=await this.apiClient.patch(`${this.endpoint}/${e}/escorts`,{escorts:s});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.G.fromApi(i)})}async setVehicles(e,t){return this.executeWithInfrastructure(null,async()=>{let s=t.map(e=>({assignedDate:new Date().toISOString(),vehicleId:e})),i=await this.apiClient.patch(`${this.endpoint}/${e}/vehicles`,{vehicleAssignments:s});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.G.fromApi(i)})}async updateStatus(e,t,s){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.put(`${this.endpoint}/${e}`,{status:t,statusChangeReason:s});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.G.fromApi(i)})}}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>u});var r=s(60687),i=s(43210),a=s(22482);let n=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),ref:s,...t}));n.displayName="Card";let o=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),ref:s,...t}));o.displayName="CardHeader";let l=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),ref:s,...t}));l.displayName="CardTitle";let c=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,a.cn)("text-sm text-muted-foreground",e),ref:s,...t}));c.displayName="CardDescription";let d=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,a.cn)("p-6 pt-0",e),ref:s,...t}));d.displayName="CardContent";let u=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,a.cn)("flex items-center p-6 pt-0",e),ref:s,...t}));u.displayName="CardFooter"},46308:(e,t,s)=>{"use strict";s.d(t,{B:()=>r});class r{static{this.PATTERNS={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,PHONE:/^\+?[\d\s\-\(\)]{10,}$/,URL:/^https?:\/\/[^\s/$.?#].[^\s]*$/,ALPHANUMERIC:/^[a-zA-Z0-9]+$/,ALPHA:/^[a-zA-Z]+$/,NUMERIC:/^\d+$/,UUID:/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,SQL_INJECTION:/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|('|('')|;|--|\/\*|\*\/)/i,XSS:/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,HTML_TAGS:/<[^>]*>/g}}static{this.DANGEROUS_STRINGS=["javascript:","vbscript:","onload=","onerror=","onclick=","onmouseover=","onfocus=","onblur=","onchange=","onsubmit=","data:text/html","eval(","expression(","setTimeout(","setInterval("]}static validateValue(e,t){let s=[],r=e;return(t.sanitizer&&(r=t.sanitizer(e)),t.required&&(null==e||""===e))?(s.push("This field is required"),{isValid:!1,errors:s}):t.required||null!=e&&""!==e?("string"==typeof e&&(t.minLength&&e.length<t.minLength&&s.push(`Minimum length is ${t.minLength} characters`),t.maxLength&&e.length>t.maxLength&&s.push(`Maximum length is ${t.maxLength} characters`),t.pattern&&!t.pattern.test(e)&&s.push("Invalid format"),this.containsDangerousContent(e)&&s.push("Contains potentially dangerous content")),t.customValidator&&!t.customValidator(r)&&s.push("Custom validation failed"),{isValid:0===s.length,errors:s,sanitizedValue:r}):{isValid:!0,errors:[],sanitizedValue:r}}static validateObject(e,t){let s=[],r={};for(let[i,a]of Object.entries(t)){let t=e[i],n=this.validateValue(t,a);n.isValid?r[i]=n.sanitizedValue:s.push(...n.errors.map(e=>`${i}: ${e}`))}return{isValid:0===s.length,errors:s,sanitizedValue:r}}static sanitizeForXSS(e){return"string"!=typeof e?e:e.replace(this.PATTERNS.XSS,"").replace(/javascript:/gi,"").replace(/vbscript:/gi,"").replace(/data:text\/html/gi,"data:text/plain").replace(/on\w+\s*=/gi,"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}static escapeForDisplay(e){return"string"!=typeof e?e:e.replace(/'/g,"''").replace(/;/g,"").replace(/--/g,"").replace(/\/\*/g,"").replace(/\*\//g,"").replace(/\b(UNION|SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b/gi,"")}static validateEmail(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.EMAIL,maxLength:254,sanitizer:e=>e.toLowerCase().trim()})}static validatePhone(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.PHONE,sanitizer:e=>e.replace(/\s/g,"")})}static validateURL(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.URL,customValidator:e=>{try{return new URL(e),!0}catch{return!1}}})}static validateUUID(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.UUID})}static containsDangerousContent(e){let t=e.toLowerCase();for(let e of this.DANGEROUS_STRINGS)if(t.includes(e.toLowerCase()))return!0;return!!(this.PATTERNS.SQL_INJECTION.test(e)||this.PATTERNS.XSS.test(e))}static createEmployeeValidationSchema(){return{firstName:{required:!0,minLength:2,maxLength:50,pattern:this.PATTERNS.ALPHA,sanitizer:this.sanitizeForXSS},lastName:{required:!0,minLength:2,maxLength:50,pattern:this.PATTERNS.ALPHA,sanitizer:this.sanitizeForXSS},email:{required:!0,pattern:this.PATTERNS.EMAIL,maxLength:254,sanitizer:e=>this.sanitizeForXSS(e.toLowerCase().trim())},phone:{required:!1,pattern:this.PATTERNS.PHONE,sanitizer:e=>e?.replace(/\s/g,"")||""},position:{required:!0,minLength:2,maxLength:100,sanitizer:this.sanitizeForXSS}}}static createVehicleValidationSchema(){return{make:{required:!0,minLength:2,maxLength:50,sanitizer:this.sanitizeForXSS},model:{required:!0,minLength:1,maxLength:50,sanitizer:this.sanitizeForXSS},year:{required:!0,pattern:/^\d{4}$/,customValidator:e=>{let t=parseInt(e.toString());return t>=1900&&t<=new Date().getFullYear()+1}},licensePlate:{required:!0,minLength:2,maxLength:20,sanitizer:e=>this.sanitizeForXSS(e.toUpperCase().trim())},vin:{required:!1,minLength:17,maxLength:17,pattern:/^[A-HJ-NPR-Z0-9]{17}$/i,sanitizer:e=>e?.toUpperCase().trim()||""}}}}},47990:()=>{},48184:(e,t,s)=>{"use strict";s.d(t,{iG:()=>a,u1:()=>n});var r=s(63924);let i={DATABASE_ERROR:"A database error occurred. Please try again or contact support.",EMPLOYEE_NOT_ACTIVE:"The selected employee is not currently active. Please select an active employee.",EMPLOYEE_NOT_FOUND:"The selected employee could not be found. Please refresh and try again.",INVALID_DRIVER_ROLE:"The selected employee does not have driver role. Please select a different employee.",INVALID_FORMAT:"Please check the format of your input.",NETWORK_ERROR:"Network error. Please check your connection and try again.",REQUIRED_FIELD:"This field is required.",SERVER_ERROR:"Server error. Please try again later.",TIMEOUT_ERROR:"Request timed out. Please try again.",VALIDATION_ERROR:"Please check your input and try again.",VEHICLE_NOT_FOUND:"The selected vehicle could not be found. Please refresh and try again.",VEHICLE_WITHOUT_DRIVER:"A vehicle cannot be assigned without selecting a driver first."};function a(e,t){var s;let r=n(e)||"An unexpected error occurred. Please try again.",i="Error";return o(s=e)&&"string"==typeof s.code&&["INVALID_DRIVER_ROLE","EMPLOYEE_NOT_FOUND","EMPLOYEE_NOT_ACTIVE","VEHICLE_NOT_FOUND","VEHICLE_WITHOUT_DRIVER"].includes(s.code)?i="Assignment Error":o(e)?i="Validation Error":t&&(i=`${t} Error`),{code:e?.code,field:e?.field||e?.path?.[0],message:r,title:i}}function n(e){if(!e)return null;if(e&&"object"==typeof e){if(e.code&&i[e.code])return i[e.code]??null;if(e.error&&"string"==typeof e.error)return e.error;if(e.message&&"string"==typeof e.message)return e.message;if(e.errors&&Array.isArray(e.errors)&&e.errors.length>0)return e.errors.map(e=>e.message||e).join(", ")}if("string"==typeof e)return e;if(e instanceof Error){if(e instanceof r.hD&&e.details){if("object"==typeof e.details){let t=e.details;if(Array.isArray(t.errors)&&t.errors.length>0)return t.errors.map(e=>e.message||e).join(", ");if(t.fieldErrors&&"object"==typeof t.fieldErrors){let e=Object.values(t.fieldErrors).flat().map(e=>e.message||e);if(e.length>0)return e.join(", ")}if(t.message)return t.message;if(t.error)return t.error}if("string"==typeof e.details)return e.details}return e.message}return"An unexpected error occurred. Please try again."}function o(e){return e&&"object"==typeof e&&"code"in e&&"message"in e&&"error"in e}},48839:(e,t,s)=>{"use strict";s.d(t,{M:()=>i});let r={fromApi:e=>({cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId})},i={fromApi:e=>({color:e.color??null,createdAt:e.createdAt,id:e.id,imageUrl:e.imageUrl??null,initialOdometer:e.initialOdometer??null,licensePlate:e.licensePlate,make:e.make,model:e.model,ownerContact:e.ownerContact,ownerName:e.ownerName,serviceHistory:(()=>{let t=e.serviceHistory||e.ServiceRecord;return Array.isArray(t)?t.map(r.fromApi):[]})(),updatedAt:e.updatedAt,vin:e.vin??null,year:e.year}),toCreateRequest(e){let t=e.vin?.trim()||this.generateDefaultVin(e),s=e.ownerContact?.trim()||"<EMAIL>",r=e.ownerName?.trim()||"WorkHub Fleet Management",i={color:e.color?e.color.trim():null,imageUrl:e.imageUrl?e.imageUrl.trim():"",initialOdometer:e.initialOdometer??null,licensePlate:e.licensePlate.trim(),make:e.make.trim(),model:e.model.trim(),ownerContact:s,ownerName:r,vin:t,year:e.year};if(!i.make||!i.model||!i.year||!i.licensePlate)throw Error("Missing required fields for creating a vehicle (make, model, year, licensePlate)");if(!/^[A-HJ-NPR-Z0-9]{17}$/.test(i.vin))throw Error("VIN must be exactly 17 characters and contain only valid characters (A-H, J-N, P-R, Z, 0-9)");return i},generateDefaultVin(e){let t="ABCDEFGHJKLMNPRSTUVWXYZ0123456789",s=e.make.substring(0,3).toUpperCase().replace(/[IOQ]/g,"X").padEnd(3,"X"),r=e.model.substring(0,2).toUpperCase().replace(/[IOQ]/g,"X").padEnd(2,"X"),i=e.year.toString().substring(2),a="";for(let e=0;e<10;e++)a+=t.charAt(Math.floor(Math.random()*t.length));return`${s}${r}${i}${a}`.substring(0,17).padEnd(17,"X")},toUpdateRequest(e){let t={};return void 0!==e.make&&(t.make=e.make.trim()),void 0!==e.model&&(t.model=e.model.trim()),void 0!==e.year&&(t.year=e.year),void 0!==e.vin&&(t.vin=e.vin.trim()),void 0!==e.licensePlate&&(t.licensePlate=e.licensePlate.trim()),void 0!==e.ownerName&&(t.ownerName=e.ownerName.trim()),void 0!==e.ownerContact&&(t.ownerContact=e.ownerContact.trim()),void 0!==e.color&&(t.color=e.color?e.color.trim():null),void 0!==e.initialOdometer&&(t.initialOdometer=e.initialOdometer),void 0!==e.imageUrl&&(t.imageUrl=e.imageUrl?e.imageUrl.trim():""),t}}},50885:(e,t,s)=>{"use strict";s.r(t),s.d(t,{useModal:()=>a});var r=s(43210),i=s(51887);let a=()=>{let e=(0,i.n)(e=>e.isModalOpen),t=(0,i.n)(e=>e.modalContent),s=(0,i.n)(e=>e.openModal),a=(0,i.n)(e=>e.closeModal),n=(0,r.useCallback)(()=>{s("login")},[s]),o=(0,r.useCallback)(()=>{s("signup")},[s]),l=(0,r.useCallback)(()=>{s("settings")},[s]),c=(0,r.useCallback)(()=>{s("delegation-form")},[s]),d=(0,r.useCallback)(()=>{s("vehicle-details")},[s]),u=(0,r.useCallback)(()=>{s("task-assignment")},[s]),m=(0,r.useCallback)(()=>{s("employee-profile")},[s]),h=(0,r.useCallback)(s=>e&&t===s,[e,t]),p=(0,r.useCallback)(()=>({backdrop:"modal-backdrop",container:e?"modal-container-visible":"modal-container-hidden",content:`modal-content modal-content-${t||"default"}`,overlay:e?"modal-overlay-visible":"modal-overlay-hidden"}),[e,t]),f=(0,r.useCallback)(()=>({"aria-describedby":t?`${t}-modal-description`:void 0,"aria-hidden":!e,"aria-labelledby":t?`${t}-modal-title`:void 0,"aria-modal":e,role:"dialog"}),[e,t]),g=(0,r.useCallback)(t=>{"Escape"===t.key&&e&&a()},[e,a]),x=(0,r.useCallback)(t=>{t.target===t.currentTarget&&e&&a()},[e,a]);return{closeModal:a,getModalAriaAttributes:f,getModalClasses:p,getModalTitle:(0,r.useCallback)(()=>{switch(t){case"delegation-form":return"Create Delegation";case"employee-profile":return"Employee Profile";case"login":return"Sign In";case"settings":return"Settings";case"signup":return"Create Account";case"task-assignment":return"Assign Task";case"vehicle-details":return"Vehicle Details";default:return"Modal"}},[t]),handleBackdropClick:x,handleEscapeKey:g,isModalOfTypeOpen:h,isModalOpen:e,isWorkHubModal:(0,r.useCallback)(()=>["delegation-form","employee-profile","task-assignment","vehicle-details"].includes(t||""),[t])(),modalContent:t,openDelegationFormModal:c,openEmployeeProfileModal:m,openLoginModal:n,openModal:s,openSettingsModal:l,openSignupModal:o,openTaskAssignmentModal:u,openVehicleDetailsModal:d}}},51528:(e,t,s)=>{"use strict";s.d(t,{$f:()=>h,lo:()=>p,E9:()=>m,Cv:()=>o.SessionManager,tC:()=>d});var r=s(2143),i=s(46308),a=s(11734);class n{static{this.DEFAULT_COOKIE_OPTIONS={httpOnly:!0,secure:!0,sameSite:"lax",path:"/"}}static setClientSideCookie(e,t,s){try{return{success:!1,error:"Not available in server-side environment"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to set client-side cookie"}}}static getSecureItem(e){try{return null}catch(e){return console.error("Failed to get secure item:",e),null}}static removeSecureItem(e,t){try{return{success:!1,error:"Not available in server-side environment"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to remove secure item"}}}static isAvailable(){try{return!1}catch{return!1}}static clearAllCookies(){try{return{success:!1,error:"Not available in server-side environment"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to clear cookies"}}}static getAllCookies(){try{return{}}catch{return{}}}static hasCookie(e){return null!==this.getSecureItem(e)}static getCookieExpiration(e){return null}}var o=s(26461),l=s(53117),c=s(38118);class d{static extractTokenFromHeader(e){if(!e)return null;let t=e.split(" ");return 2!==t.length||"Bearer"!==t[0]?null:(0,c.d$)(t[1])}static extractTokenFromCookie(e,t="sb-access-token"){if(!e)return null;let s={};return e.split(";").forEach(e=>{let[t,r]=e.trim().split("=");t&&r&&(s[t]=decodeURIComponent(r))}),s[t]??null}static validateToken(e){try{if(!e||"string"!=typeof e)return{isValid:!1,isExpired:!0,payload:null,error:"Invalid token format"};let t=(0,l.s)(e),s=Math.floor(Date.now()/1e3),r=t.exp<s,i={isValid:!r,isExpired:r,payload:t};return r&&(i.error="Token expired"),i}catch(e){return{isValid:!1,isExpired:!0,payload:null,error:e instanceof Error?e.message:"Token validation failed"}}}static extractUserRole(e){let t=this.validateToken(e);if(!t.isValid||!t.payload)return null;let s=t.payload.custom_claims?.user_role;return s||(t.payload.user_role??"USER")}static extractEmployeeId(e){let t=this.validateToken(e);return t.isValid&&t.payload?t.payload.custom_claims?.employee_id??t.payload.employee_id??null:null}static isUserActive(e){let t=this.validateToken(e);return!!t.isValid&&!!t.payload&&(t.payload.custom_claims?.is_active??t.payload.is_active??!0)}static getTokenExpiration(e){let t=this.validateToken(e);return t.isValid&&t.payload?new Date(1e3*t.payload.exp):null}static willExpireSoon(e,t=5){let s=this.getTokenExpiration(e);return!s||s<=new Date(Date.now()+60*t*1e3)}static hashTokenForLogging(e){return e?e.length<16?"short-token":`${e.substring(0,8)}...${e.substring(e.length-8)}`:"no-token"}static isValidTokenFormat(e){if(!e||"string"!=typeof e)return!1;let t=e.split(".");return 3===t.length&&t.every(e=>e.length>0)}}let u={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0},m={attachCSRFToRequest:r.k.attachToRequest,clearAllCookies:n.clearAllCookies,detectTimeout:o.SessionManager.detectTimeout,escapeForDisplay:i.B.escapeForDisplay,extractEmployeeId:d.extractEmployeeId,extractUserRole:d.extractUserRole,generateCSRFToken:r.k.generateToken,getCurrentSessionId:o.SessionManager.getCurrentSessionId,getPermissionsForRole:a.B.getPermissionsForRole,getSecureItem:n.getSecureItem,hasMinimumRole:a.B.hasMinimumRole,hasPermission:a.B.hasPermission,isCSRFRequired:r.k.isProtectionRequired,isStorageAvailable:n.isAvailable,isUserActive:d.isUserActive,removeSecureItem:n.removeSecureItem,sanitizeForXSS:i.B.sanitizeForXSS,setClientSideCookie:n.setClientSideCookie,updateActivity:o.SessionManager.updateActivity,validateCSRFToken:r.k.validateToken,validateEmail:i.B.validateEmail,validateObject:i.B.validateObject,validatePhone:i.B.validatePhone,validateToken:d.validateToken,validateURL:i.B.validateURL,validateUUID:i.B.validateUUID,validateValue:i.B.validateValue,willExpireSoon:d.willExpireSoon,canPerformSecurityCheck:()=>!0,recordSecurityAttempt(){},recordSecuritySuccess(){},startSecurityOperation:e=>!0,endSecurityOperation(e){},isCircuitOpen:()=>u.isOpen,getCircuitBreakerState:()=>({attemptCount:u.attempts,lastAttempt:u.lastAttemptTime,isOpen:u.isOpen,activeOperations:Array.from(u.activeOperations),lastOperationTime:u.lastOperationTime}),forceSecurityReset(){},resetCircuitBreakerForTesting(){},initializeCircuitBreaker(){}},h={DEFAULT_COOKIE_NAME:"sb-access-token",LOGOUT_EVENT_KEY:"workhub-logout-event",MAX_CONCURRENT_SESSIONS:5,REFRESH_COOKIE_NAME:"sb-refresh-token",SESSION_TIMEOUT_MINUTES:30,TOKEN_EXPIRY_THRESHOLD_MINUTES:5,CIRCUIT_BREAKER_MAX_ATTEMPTS:3,CIRCUIT_BREAKER_RESET_TIMEOUT:3e4,SECURITY_OPERATION_COOLDOWN:5e3,VERIFICATION_LOOP_STORAGE_KEY:"workhub_verification_attempts",SECURITY_OPERATIONS_STORAGE_KEY:"workhub_active_operations"},p={CROSS_TAB_LOGOUT:"cross_tab_logout",SECURITY_VIOLATION:"security_violation",SESSION_TIMEOUT:"session_timeout",TOKEN_EXPIRED:"token_expired",TOKEN_REFRESH_FAILED:"token_refresh_failed",UNAUTHORIZED_ACCESS:"unauthorized_access"}},51887:(e,t,s)=>{"use strict";s.d(t,{n:()=>a});var r=s(26787),i=s(59350);let a=(0,r.v)()((0,i.lt)((0,i.Zr)(e=>({autoRefreshInterval:30,closeModal:()=>e({isModalOpen:!1,modalContent:null}),dashboardLayout:"cards",fontSize:"medium",isModalOpen:!1,mapViewPreference:"roadmap",modalContent:null,notificationsEnabled:!0,openModal:t=>e({isModalOpen:!0,modalContent:t}),setAutoRefreshInterval:t=>e({autoRefreshInterval:t}),setDashboardLayout:t=>e({dashboardLayout:t}),setFontSize:t=>e({fontSize:t}),setMapViewPreference:t=>e({mapViewPreference:t}),setTableDensity:t=>e({tableDensity:t}),tableDensity:"comfortable",toggleNotifications:()=>e(e=>({notificationsEnabled:!e.notificationsEnabled}))}),{name:"workhub-ui-store",partialize:e=>({autoRefreshInterval:e.autoRefreshInterval,dashboardLayout:e.dashboardLayout,fontSize:e.fontSize,mapViewPreference:e.mapViewPreference,notificationsEnabled:e.notificationsEnabled,tableDensity:e.tableDensity})}),{name:"ui-store"}))},52765:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(65594);let i={fromApi:e=>({availability:e.availability,contactEmail:e.contactEmail??null,contactInfo:e.contactInfo,contactMobile:e.contactMobile??null,contactPhone:e.contactPhone??null,createdAt:e.createdAt,currentLocation:e.currentLocation??null,department:e.department??null,employeeId:e.employeeId,fullName:e.fullName??null,generalAssignments:e.generalAssignments,hireDate:e.hireDate??null,id:e.id,name:e.name||"",notes:e.notes??null,position:e.position??null,profileImageUrl:e.profileImageUrl??null,role:e.role,shiftSchedule:e.shiftSchedule??null,skills:e.skills,status:e.status,updatedAt:e.updatedAt,workingHours:e.workingHours??null}),toCreateRequest:e=>({availability:e.availability,contactEmail:e.contactEmail?.trim()??null,contactInfo:e.contactInfo.trim(),contactMobile:e.contactMobile?.trim()??null,contactPhone:e.contactPhone?.trim()??null,currentLocation:e.currentLocation??null,department:e.department?.trim()??null,employeeId:e.employeeId,fullName:e.fullName?.trim()??null,generalAssignments:e.generalAssignments,hireDate:e.hireDate?(0,r.B7)(e.hireDate):null,name:e.name.trim(),notes:e.notes?.trim()??null,position:e.position?.trim()??null,profileImageUrl:e.profileImageUrl?.trim()??null,role:e.role,shiftSchedule:e.shiftSchedule??null,skills:e.skills,status:e.status,workingHours:e.workingHours??null}),toUpdateRequest(e){let t={};return void 0!==e.name&&(t.name=e.name?.trim()??null),void 0!==e.employeeId&&(t.employeeId=e.employeeId),void 0!==e.contactInfo&&(t.contactInfo=e.contactInfo?.trim()??null),void 0!==e.contactEmail&&(t.contactEmail=e.contactEmail?.trim()??null),void 0!==e.contactMobile&&(t.contactMobile=e.contactMobile?.trim()??null),void 0!==e.contactPhone&&(t.contactPhone=e.contactPhone?.trim()??null),void 0!==e.position&&(t.position=e.position?.trim()??null),void 0!==e.department&&(t.department=e.department?.trim()??null),void 0!==e.hireDate&&(t.hireDate=e.hireDate?(0,r.B7)(e.hireDate):null),void 0!==e.fullName&&(t.fullName=e.fullName?.trim()??null),void 0!==e.role&&(t.role=e.role),void 0!==e.status&&(t.status=e.status),void 0!==e.availability&&(t.availability=e.availability),void 0!==e.currentLocation&&(t.currentLocation=e.currentLocation),void 0!==e.workingHours&&(t.workingHours=e.workingHours),void 0!==e.generalAssignments&&(t.generalAssignments=e.generalAssignments),void 0!==e.notes&&(t.notes=e.notes?.trim()??null),void 0!==e.profileImageUrl&&(t.profileImageUrl=e.profileImageUrl?.trim()??null),void 0!==e.shiftSchedule&&(t.shiftSchedule=e.shiftSchedule),void 0!==e.skills&&(t.skills=e.skills),t}}},53194:(e,t,s)=>{"use strict";s.d(t,{n:()=>n});var r=s(43210),i=s(81609);class a extends Error{constructor({code:e,details:t,message:s,status:r}){super(s),this.name="ApiError",this.status=r||0,this.code=e||"",this.details=t}}function n(){let e=(0,i.Z8)({enableLogging:!0}),t=(0,r.useCallback)(async t=>{try{return await e.secureRequest({url:t.url,method:t.method||"GET",data:t.data,headers:t.headers||{},timeout:t.timeout||1e4})}catch(e){if(e instanceof Error)throw new a({code:"REQUEST_FAILED",message:e.message,details:e});throw e}},[e]);return{hasValidToken:e.hasValidToken,isAuthenticated:e.isAuthenticated,refreshToken:e.refreshToken,sanitizeInput:e.sanitizeInput,secureRequest:t}}},54987:(e,t,s)=>{"use strict";s.d(t,{d:()=>o});var r=s(60687),i=s(90270),a=s(43210),n=s(22482);let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:(0,r.jsx)(i.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));o.displayName=i.bL.displayName},54995:(e,t,s)=>{"use strict";s.d(t,{o:()=>n});var r=s(43210),i=s(63213),a=s(51528);function n(){let{signOut:e}=(0,i.useAuthContext)(),[t,s]=(0,r.useState)({concurrentSessions:[],isSessionActive:!0,isSessionExpired:!1,lastActivity:null,sessionId:"",sessionWarning:!1}),n=(0,r.useCallback)(()=>{a.Cv.updateActivity(),s(e=>({...e,isSessionActive:!0,lastActivity:new Date,sessionWarning:!1}))},[]),o=(0,r.useCallback)(()=>{a.Cv.handleCrossTabLogout(),s(e=>({...e,isSessionActive:!1,isSessionExpired:!0}))},[]),l=(0,r.useCallback)(()=>{a.Cv.clearSessionState(),s({concurrentSessions:[],isSessionActive:!1,isSessionExpired:!0,lastActivity:null,sessionId:"",sessionWarning:!1})},[]),c=(0,r.useCallback)(()=>{let e=a.Cv.getSessionState(),t=a.Cv.getCurrentSessionId();e&&s(s=>({...s,isSessionActive:e.isActive,isSessionExpired:!1,lastActivity:e.lastActivity,sessionId:t,sessionWarning:!1})),a.Cv.manageConcurrentSessions()},[]);return{clearSession:l,concurrentSessions:t.concurrentSessions,handleCrossTabLogout:o,isSessionActive:t.isSessionActive,isSessionExpired:t.isSessionExpired,lastActivity:t.lastActivity,refreshSession:c,sessionId:t.sessionId,sessionWarning:t.sessionWarning,updateActivity:n}}},55387:(e,t,s)=>{"use strict";s.d(t,{LoginForm:()=>k});var r=s(60687),i=s(85777),a=s(85535),n=s(67857),o=s(72963),l=s(27247),c=s(58450),d=s(11003),u=s(76311),m=s(11516),h=s(8760),p=s(53597),f=s(43210),g=s(63213),x=s(10035),y=s(91821),v=s(29523),b=s(56896),S=s(89667),N=s(80013),j=s(22482);function E({className:e,stage:t="authenticating",message:s}){let i=(()=>{switch(t){case"authenticating":return{icon:(0,r.jsx)(m.A,{className:"size-6 animate-spin text-primary"}),text:s||"Authenticating credentials...",description:"Verifying your identity securely"};case"verifying":return{icon:(0,r.jsx)(p.A,{className:"size-6 text-accent animate-pulse"}),text:s||"Verifying security...",description:"Checking account permissions"};case"redirecting":return{icon:(0,r.jsx)(m.A,{className:"size-6 animate-spin text-primary"}),text:s||"Preparing your dashboard...",description:"Setting up your workspace"};case"success":return{icon:(0,r.jsx)(c.A,{className:"size-6 text-green-600"}),text:s||"Welcome back!",description:"Login successful"};default:return{icon:(0,r.jsx)(m.A,{className:"size-6 animate-spin text-primary"}),text:s||"Loading...",description:"Please wait"}}})();return(0,r.jsxs)("div",{className:(0,j.cn)("flex flex-col items-center justify-center p-8 text-center space-y-4",e),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 rounded-full bg-primary/10 animate-ping"}),(0,r.jsx)("div",{className:"relative flex items-center justify-center w-12 h-12 rounded-full bg-background border border-border/60 shadow-lg",children:i.icon})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-lg font-medium text-foreground",children:i.text}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:i.description})]}),(0,r.jsx)("div",{className:"flex space-x-2",children:[0,1,2].map(e=>(0,r.jsx)("div",{className:(0,j.cn)("w-2 h-2 rounded-full bg-primary/30 animate-pulse","transition-all duration-300"),style:{animationDelay:`${.2*e}s`,animationDuration:"1.5s"}},e))})]})}function k({onForgotPassword:e,onSignUp:t,onSuccess:s}){let{clearError:j,error:k,loading:A,signIn:w}=(0,g.useAuthContext)(),{clearAllErrors:T,clearFieldError:C,errors:R,isFieldValid:I,markFormTouched:_,validateForm:O}=(0,x.M)(),[P,D]=(0,f.useState)({email:"",password:"",rememberMe:!1}),[z,F]=(0,f.useState)(!1),[M,L]=(0,f.useState)("authenticating"),[U,V]=(0,f.useState)(!0),[$,q]=(0,f.useState)(null),[H,B]=(0,f.useState)(!1),W=async e=>{if(e.preventDefault(),!H&&!A){if(_(),B(!0),j(),T(),!O(P).isValid||!U)return void B(!1);try{L("authenticating");let{error:e}=await w(P.email,P.password);e?B(!1):(L("success"),void 0!==globalThis.window&&"undefined"!=typeof localStorage&&(P.rememberMe?localStorage.setItem("workhub_remember_email",P.email):localStorage.removeItem("workhub_remember_email")),setTimeout(()=>{B(!1),s?.()},800))}catch(e){console.error("Login error:",e),B(!1)}}},Z=(e,t)=>{D(s=>({...s,[e]:t})),"string"==typeof t&&R[e]&&C(e),k&&j()},G=e=>{q(e)},Y=()=>{q(null)};return H?(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,r.jsx)("div",{className:"w-full max-w-md",children:(0,r.jsx)("div",{className:"rounded-2xl border border-border/60 bg-card shadow-xl backdrop-blur-sm",children:(0,r.jsx)(E,{stage:M})})})}):(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[!U&&(0,r.jsxs)("div",{className:"mb-4 flex items-center gap-2 rounded-xl border border-destructive/20 bg-destructive/10 p-3 text-destructive",children:[(0,r.jsx)(i.A,{className:"size-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"No internet connection"})]}),(0,r.jsxs)("div",{className:"mb-8 text-center",children:[(0,r.jsx)("div",{className:"group mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ring-1 ring-primary/20 transition-all duration-300 hover:ring-primary/40",children:(0,r.jsx)(a.A,{className:"size-8 text-primary-foreground transition-transform group-hover:scale-110"})}),(0,r.jsx)("h1",{className:"mb-2 text-3xl font-bold tracking-tight text-foreground",children:"Welcome back"}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-muted-foreground",children:[(0,r.jsx)("span",{children:"Sign in to your WorkHub account"}),U&&(0,r.jsx)(n.A,{className:"size-4 text-green-600"})]})]}),(0,r.jsxs)("div",{className:"rounded-2xl border border-border/60 bg-card p-8 shadow-xl backdrop-blur-sm",children:[k&&(0,r.jsxs)(y.Fc,{className:"mb-6 border-destructive/20 bg-destructive/5",variant:"destructive",children:[(0,r.jsx)(o.A,{className:"size-4"}),(0,r.jsx)(y.TN,{className:"text-destructive",children:k})]}),!U&&(0,r.jsxs)(y.Fc,{className:"mb-6 border-yellow-200 bg-yellow-50",children:[(0,r.jsx)(i.A,{className:"size-4"}),(0,r.jsx)(y.TN,{children:"You're currently offline. Please check your internet connection to sign in."})]}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:W,children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(N.J,{className:`text-sm font-medium transition-colors ${"email"===$?"text-primary":"text-foreground"}`,htmlFor:"email",children:"Email address"}),(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,r.jsx)(l.A,{className:`size-5 transition-colors ${"email"===$?"text-primary":"text-muted-foreground"}`})}),(0,r.jsx)(S.p,{autoComplete:"email",className:`h-12 pl-10 transition-all duration-200 ${R.email?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"} ${I("email",P.email)?"border-green-500/50 focus-visible:ring-green-500/20":""}`,disabled:A||!U||H,id:"email",onBlur:Y,onChange:e=>Z("email",e.target.value),onFocus:()=>G("email"),placeholder:"Enter your email",type:"email",value:P.email}),I("email",P.email)&&(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3",children:(0,r.jsx)(c.A,{className:"size-5 text-green-500 duration-200 animate-in fade-in-0 zoom-in-95"})})]}),R.email&&(0,r.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,r.jsx)(o.A,{className:"size-4"}),R.email]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(N.J,{className:`text-sm font-medium transition-colors ${"password"===$?"text-primary":"text-foreground"}`,htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,r.jsx)(a.A,{className:`size-5 transition-colors ${"password"===$?"text-primary":"text-muted-foreground"}`})}),(0,r.jsx)(S.p,{autoComplete:"current-password",className:`h-12 pl-10 pr-12 transition-all duration-200 ${R.password?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"} ${I("password",P.password)?"border-green-500/50 focus-visible:ring-green-500/20":""}`,disabled:A||!U||H,id:"password",onBlur:Y,onChange:e=>Z("password",e.target.value),onFocus:()=>G("password"),placeholder:"Enter your password",type:z?"text":"password",value:P.password}),(0,r.jsx)("button",{"aria-label":z?"Hide password":"Show password",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground transition-colors hover:text-foreground",disabled:A||H,onClick:()=>F(!z),type:"button",children:z?(0,r.jsx)(d.A,{className:"size-5"}):(0,r.jsx)(u.A,{className:"size-5"})})]}),R.password&&(0,r.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,r.jsx)(o.A,{className:"size-4"}),R.password]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(b.S,{checked:P.rememberMe,className:"data-[state=checked]:border-primary data-[state=checked]:bg-primary",disabled:A||H,id:"remember-me",onCheckedChange:e=>Z("rememberMe",e)}),(0,r.jsx)(N.J,{className:"cursor-pointer text-sm text-muted-foreground transition-colors hover:text-foreground",htmlFor:"remember-me",children:"Remember me"})]}),e&&(0,r.jsx)("button",{className:"text-sm font-medium text-primary transition-colors hover:text-primary/80",disabled:A||H,onClick:e,type:"button",children:"Forgot password?"})]}),(0,r.jsx)(v.$,{className:"group h-12 w-full rounded-xl bg-gradient-to-r from-primary to-accent font-semibold text-primary-foreground shadow-lg transition-all duration-200 hover:from-primary/90 hover:to-accent/90 hover:shadow-xl disabled:opacity-50",disabled:A||!U||H,type:"submit",children:A||H?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"mr-2 size-5 animate-spin"}),"Signing in..."]}):(0,r.jsxs)(r.Fragment,{children:["Sign in",(0,r.jsx)(h.A,{className:"ml-2 size-5 transition-transform group-hover:translate-x-1"})]})}),(0,r.jsxs)("div",{className:"rounded-xl border border-border/40 bg-muted/30 p-4",children:[(0,r.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"size-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Demo Access"})]}),(0,r.jsxs)("div",{className:"space-y-1 text-xs text-muted-foreground",children:[(0,r.jsx)("div",{className:"font-mono",children:"<EMAIL>"}),(0,r.jsx)("div",{className:"font-mono",children:"demo123"})]})]})]})]}),t&&(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,r.jsx)("button",{className:"font-medium text-primary transition-colors hover:text-primary/80",disabled:A||H,onClick:t,children:"Create one now"})]})}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)("div",{className:"inline-flex items-center gap-2 rounded-full border border-border/40 bg-card px-4 py-2 text-xs text-muted-foreground",children:[(0,r.jsx)(p.A,{className:"size-4 text-green-600"}),(0,r.jsx)("span",{children:"Protected by enterprise-grade security"})]})}),(0,r.jsxs)("footer",{className:"mt-8 text-center text-xs text-muted-foreground",children:[(0,r.jsx)("p",{children:"\xa9 2024 WorkHub. All rights reserved."}),(0,r.jsxs)("div",{className:"mt-2 space-x-4",children:[(0,r.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Terms"}),(0,r.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Privacy"}),(0,r.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Support"})]})]})]})})}},56896:(e,t,s)=>{"use strict";s.d(t,{S:()=>l});var r=s(60687),i=s(40211),a=s(58450),n=s(43210),o=s(22482);let l=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.bL,{className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),ref:s,...t,children:(0,r.jsx)(i.C1,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(a.A,{className:"size-4"})})}));l.displayName=i.bL.displayName},57930:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var r=s(52765),i=s(48839);let a={fromApi:e=>({completed:e.completed,id:e.id,taskId:e.taskId,title:e.title}),toApiRequest:e=>({completed:void 0!==e.completed&&e.completed,taskId:e.taskId,title:e.title.trim()})},n={fromApi:e=>({createdAt:e.createdAt,dateTime:e.dateTime,deadline:e.deadline??null,description:e.description,driverEmployee:e.Employee_Task_driverEmployeeIdToEmployee?r.A.fromApi(e.Employee_Task_driverEmployeeIdToEmployee):null,driverEmployeeId:e.driverEmployeeId??null,estimatedDuration:e.estimatedDuration,id:e.id,location:e.location,notes:e.notes??null,priority:e.priority,requiredSkills:e.requiredSkills,staffEmployee:e.Employee_Task_staffEmployeeIdToEmployee?r.A.fromApi(e.Employee_Task_staffEmployeeIdToEmployee):null,staffEmployeeId:e.staffEmployeeId,status:e.status,subtasks:Array.isArray(e.SubTask)?e.SubTask.map(e=>a.fromApi(e)):[],updatedAt:e.updatedAt,vehicle:e.Vehicle?i.M.fromApi(e.Vehicle):null,vehicleId:e.vehicleId??null}),toCreateRequest:e=>({dateTime:e.dateTime,deadline:e.deadline??null,description:e.description,driverEmployeeId:e.driverEmployeeId??null,estimatedDuration:e.estimatedDuration,location:e.location,notes:e.notes??null,priority:e.priority,requiredSkills:e.requiredSkills,staffEmployeeId:e.staffEmployeeId,status:e.status,subTasks:e.subtasks?.map(a.toApiRequest)??[],vehicleId:e.vehicleId??null}),toUpdateRequest(e){let t={};return void 0!==e.description&&(t.description=e.description),void 0!==e.notes&&(t.notes=e.notes),void 0!==e.location&&(t.location=e.location),void 0!==e.dateTime&&(t.dateTime=e.dateTime),void 0!==e.estimatedDuration&&(t.estimatedDuration=e.estimatedDuration),void 0!==e.priority&&(t.priority=e.priority),void 0!==e.status&&(t.status=e.status),void 0!==e.deadline&&(t.deadline=e.deadline),void 0!==e.requiredSkills&&(t.requiredSkills=e.requiredSkills),void 0!==e.vehicleId&&(t.vehicleId=e.vehicleId),void 0!==e.staffEmployeeId&&(t.staffEmployeeId=e.staffEmployeeId),void 0!==e.driverEmployeeId&&(t.driverEmployeeId=e.driverEmployeeId),void 0!==e.subtasks&&(t.subTasks=e.subtasks.map(a.toApiRequest)),t}}},59514:(e,t,s)=>{"use strict";s.d(t,{Qb:()=>i,xR:()=>a});class r{clear(){let e=this.cache.size;this.cache.clear(),console.log(`🔄 Cache: Cleared ${e} entries`)}async get(e,t,s={}){let r={...this.defaultOptions,...s},i=Date.now(),a=this.cache.get(e);if(a&&i<a.expiresAt){let t=Math.round((a.expiresAt-i)/1e3);return console.log(`🔄 Cache HIT for ${e} (expires in ${t}s)`),a.data}let n=this.lastRequestTime.get(e)||0;if(i-n<r.throttleTime&&a){let t=Math.round((r.throttleTime-(i-n))/1e3);return console.log(`🔄 Cache THROTTLED for ${e}, returning stale data (throttle ends in ${t}s)`),a.data}return a&&r.staleWhileRevalidate&&i<a.expiresAt+r.duration?(console.log(`🔄 Cache STALE for ${e}, revalidating in background`),this.revalidateInBackground(e,t,r),a.data):(console.log(`🔄 Cache MISS for ${e}, fetching fresh data`),this.fetchAndCache(e,t,r))}getStats(){let e=Date.now();return{entries:[...this.cache.entries()].map(([t,s])=>({age:e-s.timestamp,expiresIn:s.expiresAt-e,key:t})),size:this.cache.size}}invalidate(e){this.cache.delete(e),console.log(`🔄 Cache: Invalidated ${e}`)}invalidatePattern(e){let t=[];for(let s of this.cache.keys())e.test(s)&&t.push(s);for(let e of t)this.cache.delete(e);console.log(`🔄 Cache: Invalidated ${t.length} entries matching pattern`)}cleanup(e){if(this.cache.size<=e)return;let t=[...this.cache.entries()].sort(([,e],[,t])=>e.timestamp-t.timestamp).slice(0,this.cache.size-e);for(let[e]of t)this.cache.delete(e);console.log(`🔄 Cache: Cleaned up ${t.length} old entries`)}async fetchAndCache(e,t,s){try{let r=Date.now();this.lastRequestTime.set(e,r);let i=await t();return this.cache.set(e,{data:i,expiresAt:r+s.duration,timestamp:r}),this.cleanup(s.maxEntries),i}catch(s){let t=this.cache.get(e);if(t)return console.warn(`🔄 Cache: Returning stale data for ${e} due to error:`,s),t.data;throw s}}async revalidateInBackground(e,t,s){try{await this.fetchAndCache(e,t,s),console.log(`🔄 Cache: Background revalidation completed for ${e}`)}catch(t){console.warn(`🔄 Cache: Background revalidation failed for ${e}:`,t)}}constructor(){this.cache=new Map,this.defaultOptions={duration:3e4,maxEntries:100,staleWhileRevalidate:!0,throttleTime:1e3},this.lastRequestTime=new Map}}let i=new r,a={AUDIT_LOGS:6e4,ERROR_LOGS:3e4,HEALTH_STATUS:6e4,PERFORMANCE_METRICS:12e4,USER_DATA:3e5}},60436:(e,t,s)=>{"use strict";s.d(t,{F:()=>C});var r=s(60687),i=s(58595),a=s(27247),n=s(3662),o=s(97025),l=s(21724),c=s(49497),d=s(11516),u=s(27805),m=s(53597),h=s(8563),p=s(15036),f=s(26622),g=s(85814),x=s.n(g),y=s(16189),v=s(43210),b=s(32584),S=s(96834),N=s(29523),j=s(44493),E=s(21342),k=s(91821),A=s(85763),w=s(63213);let T=e=>{switch(e){case"ADMIN":return"bg-purple-500 hover:bg-purple-600 text-white";case"EMPLOYEE":return"bg-green-500 hover:bg-green-600 text-white";case"MANAGER":return"bg-blue-500 hover:bg-blue-600 text-white";case"SUPER_ADMIN":return"bg-red-500 hover:bg-red-600 text-white";default:return"bg-gray-500 hover:bg-gray-600 text-white"}};function C({showSignOut:e=!0,variant:t="dropdown"}){let{signOut:s,user:g,userRole:C}=(0,w.useAuthContext)(),R=(0,y.useRouter)(),[I,_]=(0,v.useState)(!1);if(!g)return null;let O=async()=>{_(!0);try{await s(),R.push("/login")}catch(e){console.error("Sign out error:",e)}finally{_(!1)}},P=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"Never",D=e=>e?new Date(e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Never",z=e=>{if(!e)return"Never";let t=new Date,s=new Date(e),r=Math.floor((t.getTime()-s.getTime())/1e3);return r<60?"Just now":r<3600?`${Math.floor(r/60)}m ago`:r<86400?`${Math.floor(r/3600)}h ago`:r<2592e3?`${Math.floor(r/86400)}d ago`:P(e)},F=()=>(console.log("\uD83D\uDD0D Role Debug Info:",{"user.user_metadata?.role":g.user_metadata?.role,"userRole (from context)":C,"user.user_metadata":g.user_metadata,"user.app_metadata":g.app_metadata,"user.email":g.email}),g.user_metadata?.role&&g.user_metadata.role!==C&&console.warn("\uD83D\uDEA8 Role mismatch detected! User needs to refresh their session.",{metadataRole:g.user_metadata.role,contextRole:C}),g.user_metadata?.role||C||"USER"),M=()=>g.user_metadata?.is_active!==!1?"Active":"Inactive",L=e=>{switch(e.toUpperCase()){case"SUPER_ADMIN":case"ADMIN":return"destructive";case"MANAGER":return"default";case"USER":default:return"secondary";case"READONLY":return"outline"}},U=e=>"Active"===e?"default":"destructive",V=g.email?g.email.charAt(0).toUpperCase():"U",$=g.email||"N/A",q=null!==g.email_confirmed_at,H=F(),B=H?H.replace("_"," "):"N/A",W=T(H);return"dropdown"===t?(0,r.jsxs)(E.rI,{children:[(0,r.jsx)(E.ty,{asChild:!0,children:(0,r.jsx)(N.$,{className:"relative size-8 rounded-full",variant:"ghost",children:(0,r.jsxs)(b.eu,{className:"size-8",children:[(0,r.jsx)(b.BK,{alt:V,src:g.user_metadata?.avatar_url||""}),(0,r.jsx)(b.q5,{children:V})]})})}),(0,r.jsxs)(E.SQ,{align:"end",className:"w-56",forceMount:!0,children:[(0,r.jsx)(E.lp,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:$}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:g.id})]})}),(0,r.jsx)(E.mB,{}),(0,r.jsxs)(E._2,{children:[(0,r.jsx)(i.A,{className:"mr-2 size-4"}),(0,r.jsx)(x(),{href:"/profile",children:"Profile"})]}),(0,r.jsxs)(E._2,{children:[(0,r.jsx)(a.A,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:q?"Email Verified":"Email Not Verified"}),q?(0,r.jsx)(n.A,{className:"ml-auto size-4 text-green-500"}):(0,r.jsx)(o.A,{className:"ml-auto size-4 text-red-500"})]}),(0,r.jsxs)(E._2,{children:[(0,r.jsx)(l.A,{className:"mr-2 size-4"}),(0,r.jsx)(S.E,{className:W,children:B})]}),(0,r.jsx)(E.mB,{}),(0,r.jsxs)(E._2,{onClick:O,children:[(0,r.jsx)(c.A,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Log out"})]})]})]}):"card"===t?(0,r.jsxs)(j.Zp,{className:"mx-auto w-full max-w-md",children:[(0,r.jsxs)(j.aR,{className:"flex flex-row items-center space-x-4 p-6",children:[(0,r.jsxs)(b.eu,{className:"size-16",children:[(0,r.jsx)(b.BK,{alt:V,src:g.user_metadata?.avatar_url||""}),(0,r.jsx)(b.q5,{className:"text-2xl",children:V})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)(j.ZB,{className:"text-2xl font-bold",children:$}),(0,r.jsxs)(j.BT,{className:"text-sm text-muted-foreground",children:["User ID: ",g.id]}),(0,r.jsx)(S.E,{className:`${W} px-2 py-1 text-sm`,children:B})]})]}),(0,r.jsxs)(j.Wu,{className:"space-y-4 p-6 pt-0",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(a.A,{className:"mr-2 size-5 text-muted-foreground"}),(0,r.jsxs)("span",{className:"text-base",children:["Email: ",$]}),q?(0,r.jsx)(n.A,{className:"ml-2 size-5 text-green-500"}):(0,r.jsx)(o.A,{className:"ml-2 size-5 text-red-500"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l.A,{className:"mr-2 size-5 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-base",children:"Role: "}),(0,r.jsx)(S.E,{className:`${W} ml-1`,children:B})]}),e&&(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(N.$,{onClick:O,variant:"outline",disabled:I,children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),"Signing out..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"mr-2 size-4"}),"Log out"]})})})]})]}):"detailed"===t?(0,r.jsxs)("div",{className:"w-full max-w-5xl mx-auto space-y-6",children:[(0,r.jsx)(j.Zp,{children:(0,r.jsx)(j.aR,{className:"pb-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(b.eu,{className:"size-20",children:[(0,r.jsx)(b.BK,{alt:g.email||"",src:g.user_metadata?.avatar_url}),(0,r.jsx)(b.q5,{className:"text-xl",children:V})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:g.user_metadata?.full_name||"User Profile"}),(0,r.jsx)("p",{className:"text-muted-foreground text-lg",children:g.email})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 flex-wrap",children:[(0,r.jsx)(S.E,{className:"text-sm px-3 py-1",variant:L(F()),children:F()}),(0,r.jsxs)(S.E,{className:"text-sm px-3 py-1",variant:U(M()),children:[(0,r.jsx)(u.A,{className:"mr-1 size-3"}),M()]}),g.email_confirmed_at&&(0,r.jsxs)(S.E,{className:"text-sm px-3 py-1",variant:"outline",children:[(0,r.jsx)(m.A,{className:"mr-1 size-3"}),"Verified"]})]})]})]}),e&&(0,r.jsx)(N.$,{disabled:I,onClick:O,variant:"outline",size:"sm",children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),"Signing out..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"mr-2 size-4"}),"Sign out"]})})]})})}),(0,r.jsxs)(A.tU,{defaultValue:"overview",className:"w-full",children:[(0,r.jsxs)(A.j7,{className:"grid w-full grid-cols-3",children:[(0,r.jsx)(A.Xi,{value:"overview",children:"Overview"}),(0,r.jsx)(A.Xi,{value:"security",children:"Security"}),(0,r.jsx)(A.Xi,{value:"activity",children:"Activity"})]}),(0,r.jsx)(A.av,{value:"overview",className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(j.Zp,{children:[(0,r.jsx)(j.aR,{children:(0,r.jsxs)(j.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)(i.A,{className:"size-5"}),"Account Information"]})}),(0,r.jsx)(j.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"User ID:"}),(0,r.jsxs)("span",{className:"font-mono text-xs bg-muted px-2 py-1 rounded",children:[g.id.slice(0,8),"..."]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Email:"}),(0,r.jsx)("span",{className:"text-sm",children:g.email})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Role:"}),(0,r.jsx)(S.E,{variant:L(F()),children:F()})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Status:"}),(0,r.jsx)(S.E,{variant:U(M()),children:M()})]}),g.user_metadata?.employee_id&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Employee ID:"}),(0,r.jsx)("span",{className:"text-sm",children:g.user_metadata.employee_id})]})]})})]}),(0,r.jsxs)(j.Zp,{children:[(0,r.jsx)(j.aR,{children:(0,r.jsxs)(j.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)(a.A,{className:"size-5"}),"Contact & Verification"]})}),(0,r.jsx)(j.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Email Verified:"}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:g.email_confirmed_at?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"size-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm text-green-600",children:"Verified"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"size-4 text-red-600"}),(0,r.jsx)("span",{className:"text-sm text-red-600",children:"Not Verified"})]})})]}),g.email_confirmed_at&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Verified On:"}),(0,r.jsx)("span",{className:"text-sm",children:P(g.email_confirmed_at)})]})]})})]})]})}),(0,r.jsx)(A.av,{value:"security",className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(j.Zp,{children:[(0,r.jsx)(j.aR,{children:(0,r.jsxs)(j.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)(m.A,{className:"size-5"}),"Security Status"]})}),(0,r.jsxs)(j.Wu,{className:"space-y-4",children:[(0,r.jsxs)(k.Fc,{children:[(0,r.jsx)(m.A,{className:"size-4"}),(0,r.jsx)(k.TN,{children:"Your account is protected by enterprise-grade security protocols. All activities are monitored and logged for security purposes."})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Account Type:"}),(0,r.jsx)("span",{className:"text-sm",children:"Standard User"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"SSO User:"}),(0,r.jsx)("span",{className:"text-sm",children:g.is_sso_user?"Yes":"No"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Anonymous:"}),(0,r.jsx)("span",{className:"text-sm",children:g.is_anonymous?"Yes":"No"})]})]})]})]}),(0,r.jsxs)(j.Zp,{children:[(0,r.jsx)(j.aR,{children:(0,r.jsxs)(j.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)(h.A,{className:"size-5"}),"Session Information"]})}),(0,r.jsx)(j.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Current Session:"}),(0,r.jsx)(S.E,{variant:"default",children:"Active"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Session ID:"}),(0,r.jsx)("span",{className:"font-mono text-xs bg-muted px-2 py-1 rounded",children:g.id.slice(-8)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"App Metadata:"}),(0,r.jsx)("span",{className:"text-sm",children:g.app_metadata?"Present":"None"})]})]})})]})]})}),(0,r.jsx)(A.av,{value:"activity",className:"space-y-4",children:(0,r.jsxs)(j.Zp,{children:[(0,r.jsx)(j.aR,{children:(0,r.jsxs)(j.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,r.jsx)(u.A,{className:"size-5"}),"Account Activity"]})}),(0,r.jsx)(j.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"size-5 text-muted-foreground"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Last Sign In"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:D(g.last_sign_in_at)})]})]}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:z(g.last_sign_in_at)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(f.A,{className:"size-5 text-muted-foreground"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Account Created"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:D(g.created_at)})]})]}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:z(g.created_at)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(i.A,{className:"size-5 text-muted-foreground"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Last Updated"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:D(g.updated_at)})]})]}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:z(g.updated_at)})]})]})})]})})]})]}):null}},61135:()=>{},63213:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>d,useAuthContext:()=>u});var r=s(60687),i=s(43210);s(8342);var a=s(51528),n=s(89513),o=s(65931);let l="workhub-logout-event",c=(0,i.createContext)(void 0);function d({children:e}){let[t,s]=(0,i.useState)(null),[d,u]=(0,i.useState)(null),[m,h]=(0,i.useState)(!0),[p,f]=(0,i.useState)(null),[g,x]=(0,i.useState)(!1),y=async(e,t)=>{try{h(!0),f(null);let{data:s,error:r}=await o.N.auth.signInWithPassword({email:e,password:t});if(r)return f(r.message),{error:r.message};return{}}catch(t){let e=t instanceof Error?t.message:"An unexpected error occurred";return f(e),{error:e}}finally{h(!1)}},v=async()=>{if(!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Sign out blocked by circuit breaker");let e="auth-signout";if(!a.E9.startSecurityOperation(e))return void console.debug("\uD83D\uDD04 Sign out already in progress");try{h(!0),console.log("\uD83D\uDD10 Starting sign out process..."),a.Cv.clearSessionState(),a.E9.clearAllCookies();let{error:e}=await o.N.auth.signOut();e?(console.error("❌ Supabase sign out error:",e),a.E9.recordSecurityAttempt(),f(e.message)):(console.log("✅ Sign out successful"),a.E9.recordSecuritySuccess(),s(null),u(null),f(null))}catch(e){console.error("Sign out error:",e),a.E9.recordSecurityAttempt(),f("Sign out failed")}finally{a.E9.endSecurityOperation(e),h(!1)}},b={clearError:()=>{f(null)},error:p,isInitialized:g,loading:m,session:t?{access_token:t.access_token,user:d??null}:null,signIn:y,signOut:v,user:d,userRole:d?d.user_metadata?.role||d.app_metadata?.role||"USER":null},S=(0,i.useRef)(!1);return(0,n.Q)(),(0,i.useCallback)(e=>{if(e.key===l&&"true"===e.newValue){if(console.log("\uD83D\uDD10 Cross-tab logout detected. Signing out..."),!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Cross-tab logout blocked by circuit breaker");if(!S.current){S.current=!0;let e="cross-tab-logout";a.E9.startSecurityOperation(e)?b.signOut().finally(()=>{a.E9.endSecurityOperation(e),S.current=!1,localStorage.removeItem(l)}):(console.debug("\uD83D\uDD04 Cross-tab logout already in progress"),S.current=!1)}}},[b.signOut]),(0,r.jsx)(c.Provider,{value:b,children:e})}function u(){let e=(0,i.useContext)(c);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e}},63503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>p,L3:()=>f,c7:()=>h,lG:()=>l,rr:()=>g,zM:()=>c});var r=s(60687),i=s(26134),a=s(78726),n=s(43210),o=s(22482);let l=i.bL,c=i.l9,d=i.ZL;i.bm;let u=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),ref:s,...t}));u.displayName=i.hJ.displayName;let m=n.forwardRef(({children:e,className:t,...s},n)=>(0,r.jsxs)(d,{children:[(0,r.jsx)(u,{}),(0,r.jsxs)(i.UC,{className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),ref:n,...s,children:[e,(0,r.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(a.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.UC.displayName;let h=({className:e,...t})=>(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});h.displayName="DialogHeader";let p=({className:e,...t})=>(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});p.displayName="DialogFooter";let f=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.hE,{className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),ref:s,...t}));f.displayName=i.hE.displayName;let g=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.VY,{className:(0,o.cn)("text-sm text-muted-foreground",e),ref:s,...t}));g.displayName=i.VY.displayName},63924:(e,t,s)=>{"use strict";s.d(t,{hD:()=>i,v3:()=>a,v7:()=>n,PO:()=>o,Dr:()=>l,m_:()=>c,_7:()=>d});var r=function(e){return e.AUTHENTICATION_ERROR="authentication_error",e.AUTHORIZATION_ERROR="authorization_error",e.CLIENT_ERROR="client_error",e.NETWORK_ERROR="network_error",e.NOT_FOUND="not_found",e.PARSING_ERROR="parsing_error",e.RATE_LIMIT="rate_limit",e.SERVER_ERROR="server_error",e.TIMEOUT="timeout",e.UNKNOWN="unknown",e.VALIDATION_ERROR="validation_error",e}({});class i extends Error{constructor(e,t){super(e),this.name="ApiError",this.status=t.status,t.code&&(this.code=t.code),t.endpoint&&(this.endpoint=t.endpoint),t.validationErrors&&(this.validationErrors=t.validationErrors),this.receivedData=t.receivedData,this.details=t.details,this.errorType=t.errorType||this.determineErrorType(),this.retryable=this.isRetryable(),Object.setPrototypeOf(this,i.prototype)}static create(e,t,s){return new i(e,{details:s?.details,...s?.errorType&&{errorType:s.errorType},receivedData:s?.receivedData,status:t,...s?.validationErrors&&{validationErrors:s.validationErrors}})}getFormattedMessage(){if(this.isValidationError()){let e=this.validationErrors.map(e=>`${e.path}: ${e.message}`).join("; ");return`Validation failed: ${e}`}switch(this.errorType){case"authentication_error":return"Authentication required. Please log in and try again.";case"authorization_error":return"You do not have permission to perform this action.";case"network_error":return"Network error: Unable to connect to the server. Please check your internet connection.";case"not_found":return`Resource not found: ${this.endpoint||"The requested resource"} could not be found.`;case"parsing_error":return"Could not parse the server response. Please try again or contact support.";case"rate_limit":return"Too many requests. Please try again later.";case"server_error":return`Server error (${this.status}): ${this.message}. Please try again later.`;case"timeout":return"Request timed out. The server is taking too long to respond.";default:return this.message}}getTechnicalDetails(){let e=[`Status: ${this.status}`,`Type: ${this.errorType}`,`Message: ${this.message}`];return this.details&&e.push(`Details: ${JSON.stringify(this.details)}`),this.validationErrors&&e.push(`Validation Errors: ${JSON.stringify(this.validationErrors)}`),e.join("\n")}isValidationError(){return 400===this.status&&Array.isArray(this.validationErrors)&&this.validationErrors.length>0}determineErrorType(){return this.status>=500?"server_error":429===this.status?"rate_limit":401===this.status?"authentication_error":403===this.status?"authorization_error":400===this.status&&this.isValidationError()?"validation_error":this.status>=400&&this.status<500?"client_error":"unknown"}isRetryable(){return"server_error"===this.errorType||"network_error"===this.errorType||"timeout"===this.errorType||"rate_limit"===this.errorType}}class a extends i{constructor(e="Authentication Failed",t){super(e,{details:t,errorType:r.AUTHENTICATION_ERROR,status:401}),this.name="AuthenticationError",Object.setPrototypeOf(this,a.prototype)}}class n extends i{constructor(e="Bad Request",t){super(e,{details:t,errorType:r.CLIENT_ERROR,status:400}),this.name="BadRequestError",Object.setPrototypeOf(this,n.prototype)}}class o extends i{constructor(e="Internal Server Error",t){super(e,{details:t,errorType:r.SERVER_ERROR,status:500}),this.name="InternalServerError",Object.setPrototypeOf(this,o.prototype)}}class l extends i{constructor(e="Network Error",t){super(e,{details:t,errorType:r.NETWORK_ERROR,status:0}),this.name="NetworkError",Object.setPrototypeOf(this,l.prototype)}}class c extends i{constructor(e="Resource Not Found",t){super(e,{details:t,errorType:r.NOT_FOUND,status:404}),this.name="NotFoundError",Object.setPrototypeOf(this,c.prototype)}}class d extends i{constructor(e,t,s,i){super(e,{details:i,errorType:r.CLIENT_ERROR,status:s||500}),this.code=t,this.statusCode=s,this.context=i,this.name="ServiceError",Object.setPrototypeOf(this,d.prototype)}}},64968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{useUiPreferences:()=>a});var r=s(43210),i=s(51887);let a=()=>{let e=(0,i.n)(e=>e.fontSize),t=(0,i.n)(e=>e.setFontSize),s=(0,i.n)(e=>e.notificationsEnabled),a=(0,i.n)(e=>e.toggleNotifications),n=(0,i.n)(e=>e.tableDensity),o=(0,i.n)(e=>e.setTableDensity),l=(0,i.n)(e=>e.mapViewPreference),c=(0,i.n)(e=>e.setMapViewPreference),d=(0,i.n)(e=>e.dashboardLayout),u=(0,i.n)(e=>e.setDashboardLayout),m=(0,i.n)(e=>e.autoRefreshInterval),h=(0,i.n)(e=>e.setAutoRefreshInterval),p=(0,r.useCallback)(()=>{switch(e){case"large":return"text-lg";case"small":return"text-sm";default:return"text-base"}},[e]),f=(0,r.useCallback)(()=>{switch(n){case"compact":return{cell:"py-1 px-2",row:"h-8",table:"table-compact"};case"spacious":return{cell:"py-4 px-4",row:"h-16",table:"table-spacious"};default:return{cell:"py-2 px-3",row:"h-12",table:"table-comfortable"}}},[n]),g=(0,r.useCallback)(()=>{switch(d){case"cards":default:return"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6";case"grid":return"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4";case"list":return"flex flex-col space-y-4"}},[d]),x=(0,r.useCallback)(()=>{s||a()},[s,a]),y=(0,r.useCallback)(()=>{s&&a()},[s,a]),v=(0,r.useCallback)(()=>{t("medium"),o("comfortable"),c("roadmap"),u("cards"),h(30)},[t,o,c,u,h]),b=(0,r.useCallback)(()=>({autoRefreshInterval:m,dashboardLayout:d,fontSize:e,mapViewPreference:l,notificationsEnabled:s,tableDensity:n}),[e,s,n,l,d,m]);return{autoRefreshInterval:m,dashboardLayout:d,disableNotifications:y,enableNotifications:x,fontSize:e,getAllPreferences:b,getDashboardLayoutClasses:g,getFontSizeClass:p,getTableDensityClasses:f,mapViewPreference:l,notificationsEnabled:s,resetPreferences:v,setAutoRefreshInterval:h,setDashboardLayout:u,setFontSize:t,setMapViewPreference:c,setTableDensity:o,tableDensity:n,toggleNotifications:a}}},65594:(e,t,s)=>{"use strict";s.d(t,{B7:()=>u,Qr:()=>m,g3:()=>h,rm:()=>d});var r=s(58261),i=s(47141),a=s(35355),n=s(75699),o=s(32637);let l={fallbackToBothFormats:!0,locale:"en-GB",primaryFormat:"EU"},c=(e,t=l)=>{if(!e||""===e.trim())return null;let s=e.trim();try{if(s.includes("T")||s.includes("Z")||/^\d{4}-\d{2}-\d{2}/.test(s)){let e=(0,r.H)(s);if((0,i.f)(e))return e}if(/^\d{4}-\d{2}-\d{2}$/.test(s)){let e=new Date(s+"T00:00:00");if((0,i.f)(e))return e}let e=s.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);if(e){let[,r,n]=e;for(let e of"EU"===t.primaryFormat?["dd/MM/yyyy","MM/dd/yyyy"]:["MM/dd/yyyy","dd/MM/yyyy"])try{let t=(0,a.qg)(s,e,new Date);if((0,i.f)(t)){let s=e.startsWith("MM")?Number.parseInt(r,10):Number.parseInt(n,10),i=e.startsWith("MM")?Number.parseInt(n,10):Number.parseInt(r,10);if(s>=1&&s<=12&&i>=1&&i<=31)return t}}catch(e){console.debug("Error occurred while parsing date:",e)}if(!t.fallbackToBothFormats)return null}for(let e of["dd-MM-yyyy","MM-dd-yyyy","yyyy/MM/dd","dd.MM.yyyy","MM.dd.yyyy"])try{let t=(0,a.qg)(s,e,new Date);if((0,i.f)(t))return t}catch{}let n=new Date(s);if((0,i.f)(n))return n;return null}catch{return null}},d=(e,t="datetime-local")=>{if(!e)return"";try{let s="string"==typeof e?(0,r.H)(e):e;if(!(0,i.f)(s))return"";if("date"===t)return(0,n.GP)(s,"yyyy-MM-dd");return(0,n.GP)(s,"yyyy-MM-dd'T'HH:mm")}catch{return""}},u=(e,t=l)=>{if(!e)return"";try{if((0,o.$)(e))return e.toISOString();if("string"==typeof e){let s=c(e,t);if(s&&(0,i.f)(s))return s.toISOString()}return""}catch{return""}},m=e=>{if(!e)return!1;try{let t=new Date(e);return(0,i.f)(t)}catch{return!1}},h=e=>e&&""!==e.trim()?u(e):""},65931:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(43530);let i="https://abylqjnpaegeqwktcukn.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o";if(!i||!a)throw Error("Missing Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file");let n=(0,r.createClient)(i,a)},67079:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});var r=s(43210),i=s(51528),a=s(89513);function n(e){let[t,s]=(0,r.useState)({isTokenExpired:!1,isTokenValid:!1,lastValidation:null,tokenError:null,willExpireSoon:!1}),n=(0,r.useMemo)(()=>(0,a.Q)(),[]),o=(0,r.useCallback)(()=>{if(!e)return null;if(!i.E9.canPerformSecurityCheck())return console.debug("\uD83D\uDD12 Token validation blocked by circuit breaker"),null;let t="token-validation";if(!i.E9.startSecurityOperation(t))return console.debug("\uD83D\uDD04 Token validation already in progress"),null;try{let t=i.tC.validateToken(e);return t.isValid?(i.E9.recordSecuritySuccess(),i.Cv.handleSessionValidation(!0)):(console.warn("❌ Token validation failed:",t.error),i.E9.recordSecurityAttempt(),i.Cv.handleSessionValidation(!1,{error:t.error})),s(s=>({...s,isTokenExpired:t.isExpired,isTokenValid:t.isValid,lastValidation:new Date,tokenError:t.error||null,willExpireSoon:!!t.isValid&&i.tC.willExpireSoon(e)})),t}catch(t){let e=t instanceof Error?t.message:"Token validation failed";return console.error("❌ Token validation error:",e),i.E9.recordSecurityAttempt(),s(t=>({...t,isTokenValid:!1,tokenError:e,lastValidation:new Date})),null}finally{i.E9.endSecurityOperation(t)}},[e]),l=(0,r.useCallback)(async()=>{if(!i.E9.canPerformSecurityCheck())return console.debug("\uD83D\uDD12 Token refresh blocked by circuit breaker"),!1;let e="token-refresh";if(!i.E9.startSecurityOperation(e))return console.debug("\uD83D\uDD04 Token refresh already in progress"),!1;try{s(e=>({...e,tokenError:null})),console.log("\uD83D\uDD04 Starting token refresh...");let e=await n.refreshNow();return e?(console.log("✅ Token refresh successful"),i.E9.recordSecuritySuccess(),i.Cv.handleTokenRefresh(!0),s(e=>({...e,isTokenValid:!0,isTokenExpired:!1,tokenError:null,lastValidation:new Date}))):(console.warn("❌ Token refresh failed"),i.E9.recordSecurityAttempt(),i.Cv.handleTokenRefresh(!1),s(e=>({...e,isTokenValid:!1,tokenError:"Token refresh failed"}))),e}catch(t){let e=t instanceof Error?t.message:"Token refresh failed";return console.error("❌ Token refresh error:",e),i.E9.recordSecurityAttempt(),i.Cv.handleTokenRefresh(!1,{error:e}),s(t=>({...t,isTokenValid:!1,tokenError:e})),!1}finally{i.E9.endSecurityOperation(e)}},[n]),c=(0,r.useCallback)(()=>{s({isTokenExpired:!1,isTokenValid:!1,lastValidation:null,tokenError:null,willExpireSoon:!1})},[]);return{checkTokenExpiry:(0,r.useCallback)(()=>!e||i.tC.willExpireSoon(e,i.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES),[e]),clearToken:c,getTokenExpiration:(0,r.useCallback)(()=>e?i.tC.getTokenExpiration(e):null,[e]),isTokenExpired:t.isTokenExpired,isTokenValid:t.isTokenValid,lastValidation:t.lastValidation,refreshToken:l,tokenError:t.tokenError,validateCurrentToken:o,willExpireSoon:t.willExpireSoon}}},68983:(e,t,s)=>{"use strict";s.d(t,{G:()=>n});let r={toDelegateApiStructure:e=>({name:e.name,notes:e.notes??null,title:e.title})},i={fromApi:e=>({availability:e.availability,contactEmail:e.contactEmail??null,contactInfo:e.contactInfo,contactMobile:e.contactMobile??null,contactPhone:e.contactPhone??null,createdAt:e.createdAt,currentLocation:e.currentLocation??null,department:e.department??null,employeeId:e.employeeId,fullName:e.fullName??null,generalAssignments:e.generalAssignments,hireDate:e.hireDate??null,id:e.id,name:e.name,notes:e.notes??null,position:e.position??null,profileImageUrl:e.profileImageUrl??null,role:e.role,shiftSchedule:e.shiftSchedule??null,skills:e.skills,status:e.status,updatedAt:e.updatedAt,workingHours:e.workingHours??null})},a={fromApi:e=>({color:e.color,createdAt:e.createdAt,id:e.id,imageUrl:e.imageUrl,initialOdometer:e.initialOdometer,licensePlate:e.licensePlate,make:e.make,model:e.model,ownerContact:e.ownerContact,ownerName:e.ownerName,serviceHistory:[],updatedAt:e.updatedAt,vin:e.vin,year:e.year})},n={fromApi:e=>({arrivalFlight:e.flightArrivalDetails?o.fromApi(e.flightArrivalDetails):null,createdAt:e.createdAt,delegates:e.delegates||[],departureFlight:e.flightDepartureDetails?o.fromApi(e.flightDepartureDetails):null,drivers:e.drivers?.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,employee:i.fromApi(t),employeeId:t.id,id:`driver-${e.id}-${t.id}`,updatedAt:e.updatedAt}))||[],durationFrom:e.durationFrom,durationTo:e.durationTo,escorts:e.escorts?.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,employee:i.fromApi(t),employeeId:t.id,id:`escort-${e.id}-${t.id}`,updatedAt:e.updatedAt}))||[],eventName:e.eventName,id:e.id,imageUrl:e.imageUrl??null,invitationFrom:e.invitationFrom??null,invitationTo:e.invitationTo??null,location:e.location,notes:e.notes??null,status:e.status,statusHistory:e.statusHistory||[],updatedAt:e.updatedAt,vehicles:e.vehicles?.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,id:`vehicle-${e.id}-${t.id}`,updatedAt:e.updatedAt,vehicle:a.fromApi(t),vehicleId:t.id}))||[]}),toCreateRequest:e=>({delegates:e.delegates?.map(e=>r.toDelegateApiStructure(e))??[],driverEmployeeIds:e.drivers?.map(e=>e.employeeId)??[],durationFrom:e.durationFrom,durationTo:e.durationTo,escortEmployeeIds:e.escorts?.map(e=>e.employeeId)??[],eventName:e.eventName,flightArrivalDetails:e.flightArrivalDetails?o.toApiStructureForCreate(e.flightArrivalDetails):void 0,flightDepartureDetails:e.flightDepartureDetails?o.toApiStructureForCreate(e.flightDepartureDetails):void 0,imageUrl:e.imageUrl??null,invitationFrom:e.invitationFrom??null,invitationTo:e.invitationTo??null,location:e.location,notes:e.notes??null,status:e.status,vehicleIds:e.vehicles?.map(e=>e.vehicleId)??[]}),toUpdateRequest(e){let t={};return void 0!==e.eventName&&(t.eventName=e.eventName),void 0!==e.location&&(t.location=e.location),void 0!==e.durationFrom&&(t.durationFrom=e.durationFrom),void 0!==e.durationTo&&(t.durationTo=e.durationTo),void 0!==e.status&&(t.status=e.status),void 0!==e.notes&&(t.notes=e.notes),void 0!==e.imageUrl&&(t.imageUrl=e.imageUrl),void 0!==e.invitationFrom&&(t.invitationFrom=e.invitationFrom),void 0!==e.invitationTo&&(t.invitationTo=e.invitationTo),void 0!==e.flightArrivalDetails&&(t.flightArrivalDetails=e.flightArrivalDetails??void 0),void 0!==e.flightDepartureDetails&&(t.flightDepartureDetails=e.flightDepartureDetails??void 0),void 0!==e.escorts&&(t.escortEmployeeIds=e.escorts.map(e=>e.employeeId)),void 0!==e.drivers&&(t.driverEmployeeIds=e.drivers.map(e=>e.employeeId)),void 0!==e.vehicles&&(t.vehicleIds=e.vehicles.map(e=>e.vehicleId)),t}},o={fromApi:e=>e?{airport:e.airport,dateTime:e.dateTime,flightNumber:e.flightNumber,id:e.id,notes:e.notes||null,terminal:e.terminal||null}:null,toApiStructureForCreate:e=>e}},70058:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=70058,e.exports=t},76783:(e,t,s)=>{"use strict";s.d(t,{D:()=>n});var r=s(57930),i=s(77312);let a={fromApi:e=>r.J.fromApi(e),toApi:e=>e};class n extends i.v{constructor(e,t){super(e,{cacheDuration:18e4,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/tasks",this.transformer=a}async getByStatus(e){return(await this.getAll({status:e})).data}async updateTaskStatus(e,t){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.patch(`${this.endpoint}/${e}/status`,{status:t});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),r.J.fromApi(s)})}}},76928:(e,t,s)=>{"use strict";s.d(t,{default:()=>eI});var r=s(60687),i=s(8693),a=s(16189),n=s(63213),o=s(321),l=s(24343),c=s(78726),d=s(43210),u=s.n(d),m=s(24026),h=s(27629),p=s(24920),f=s(44610),g=s(29333),x=s(48206),y=s(33886),v=s(80489),b=s(5600),S=s(58369),N=s(22423),j=s(41936),E=s(61662),k=s(74158),A=s(85814),w=s.n(A),T=s(29523),C=s(89667),R=s(22482);let I=[{title:"Overview",defaultOpen:!0,items:[{href:"/",icon:m.A,label:"Dashboard"},{href:"/reports",icon:h.A,label:"Analytics",isNew:!0}]},{title:"Operations",defaultOpen:!0,items:[{href:"/vehicles",icon:p.A,label:"Fleet"},{href:"/service-history",icon:f.A,label:"Maintenance"},{href:"/service-records",icon:g.A,label:"Service Records"}]},{title:"Workforce",defaultOpen:!1,items:[{href:"/employees",icon:x.A,label:"Team Members"},{href:"/delegations",icon:y.A,label:"Projects"},{href:"/tasks",icon:v.A,label:"Tasks"}]},{title:"System",defaultOpen:!1,items:[{href:"/reliability",icon:b.A,label:"Monitoring"},{href:"/admin",icon:S.A,label:"Administration"}]}],_=({className:e,collapsed:t=!1})=>{let s=(0,a.usePathname)(),[i,n]=(0,d.useState)(""),[o,l]=(0,d.useState)(new Set(I.filter(e=>e.defaultOpen).map(e=>e.title))),c=e=>{let t=new Set(o);t.has(e)?t.delete(e):t.add(e),l(t)},u=I.map(e=>({...e,items:e.items.filter(e=>e.label.toLowerCase().includes(i.toLowerCase()))})).filter(e=>e.items.length>0),m=e=>"/"===e?"/"===s:s?.startsWith(e);return t?(0,r.jsxs)("aside",{className:(0,R.cn)("flex w-16 flex-col border-r bg-background",e),children:[(0,r.jsx)("div",{className:"flex h-16 items-center justify-center border-b",children:(0,r.jsx)(w(),{href:"/",className:"flex items-center",children:(0,r.jsx)(N.A,{className:"size-6 text-primary"})})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,r.jsx)("div",{className:"space-y-2 p-2",children:I.flatMap(e=>e.items).map(e=>(0,r.jsx)(T.$,{asChild:!0,variant:m(e.href)?"secondary":"ghost",size:"icon",className:"size-12",children:(0,r.jsx)(w(),{href:e.href,title:e.label,children:(0,r.jsx)(e.icon,{className:"size-5"})})},e.href))})})]}):(0,r.jsxs)("aside",{className:(0,R.cn)("flex w-64 flex-col border-r bg-background",e),children:[(0,r.jsx)("div",{className:"flex h-16 items-center border-b px-6",children:(0,r.jsxs)(w(),{href:"/",className:"flex items-center space-x-2 text-lg font-semibold transition-opacity hover:opacity-80",children:[(0,r.jsx)(N.A,{className:"size-6 text-primary"}),(0,r.jsx)("span",{children:"WorkHub"})]})}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(j.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(C.p,{placeholder:"Search navigation...",value:i,onChange:e=>n(e.target.value),className:"pl-9"})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,r.jsx)("div",{className:"space-y-2 p-4",children:u.map(e=>{let t=o.has(e.title);return e.items.length>0?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(T.$,{variant:"ghost",className:"w-full justify-between px-2 py-1.5 text-xs font-medium uppercase text-muted-foreground hover:text-foreground",onClick:()=>c(e.title),children:[(0,r.jsx)("span",{children:e.title}),t?(0,r.jsx)(E.A,{className:"size-3"}):(0,r.jsx)(k.A,{className:"size-3"})]}),t&&(0,r.jsx)("div",{className:"ml-2 space-y-1",children:e.items.map(e=>(0,r.jsx)(T.$,{asChild:!0,variant:m(e.href)?"secondary":"ghost",className:"w-full justify-start px-3 py-2",children:(0,r.jsxs)(w(),{href:e.href,className:"flex items-center space-x-3",children:[(0,r.jsx)(e.icon,{className:"size-4"}),(0,r.jsx)("span",{className:"flex-1",children:e.label}),e.isNew&&(0,r.jsx)("span",{className:"rounded bg-primary px-1.5 py-0.5 text-xs text-primary-foreground",children:"New"}),e.badge&&(0,r.jsx)("span",{className:"rounded bg-muted px-1.5 py-0.5 text-xs",children:e.badge})]})},e.href))})]},e.title):null})})}),(0,r.jsx)("div",{className:"border-t p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,r.jsx)("span",{children:"v2.1.0"}),(0,r.jsx)(w(),{href:"/settings",className:"hover:text-foreground",children:"Settings"})]})})]})};var O=s(60436),P=s(29848),D=s(10453),z=s(10218),F=s(21342),M=s(41181);function L(){let{setTheme:e,systemTheme:t,theme:s}=(0,z.D)(),{currentTheme:i,setTheme:a}=(0,M.useTheme)(),[n,o]=d.useState(!1);d.useEffect(()=>{o(!0)},[]);let l=s=>{e(s),"system"===s?a(t||"light"):a(s)},c=s||i,u="dark"===("system"===c?t:c);return n?(0,r.jsxs)(F.rI,{children:[(0,r.jsx)(F.ty,{asChild:!0,children:(0,r.jsxs)(T.$,{"aria-label":`Current theme: ${c}. Click to change theme`,className:"text-foreground hover:bg-accent hover:text-accent-foreground",size:"icon",variant:"ghost",children:[(0,r.jsx)(P.A,{className:`size-[1.2rem] transition-all duration-300 ${u?"rotate-90 scale-0":"rotate-0 scale-100"}`}),(0,r.jsx)(D.A,{className:`absolute size-[1.2rem] transition-all duration-300 ${u?"rotate-0 scale-100":"rotate-90 scale-0"}`}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsxs)(F.SQ,{align:"end",className:"min-w-32",children:[(0,r.jsxs)(F._2,{className:"light"===c?"bg-accent text-accent-foreground":"",onClick:()=>l("light"),children:[(0,r.jsx)(P.A,{className:"mr-2 size-4"}),"Light"]}),(0,r.jsxs)(F._2,{className:"dark"===c?"bg-accent text-accent-foreground":"",onClick:()=>l("dark"),children:[(0,r.jsx)(D.A,{className:"mr-2 size-4"}),"Dark"]}),(0,r.jsxs)(F._2,{className:"system"===c?"bg-accent text-accent-foreground":"",onClick:()=>l("system"),children:[(0,r.jsx)(b.A,{className:"mr-2 size-4"}),"System"]})]})]}):(0,r.jsx)(T.$,{className:"text-foreground",size:"icon",variant:"ghost",children:(0,r.jsx)("div",{className:"size-[1.2rem]"})})}var U=s(64968);let V=({children:e,className:t})=>{let[s,i]=(0,d.useState)(!1),[a,n]=(0,d.useState)(!1),{fontSize:o}=(0,U.useUiPreferences)();return(0,r.jsxs)("div",{className:(0,R.cn)("flex h-screen bg-background","small"===o&&"text-sm","large"===o&&"text-lg",t),children:[a&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-black/50 lg:hidden",onClick:()=>n(!1)}),(0,r.jsx)("div",{className:(0,R.cn)("fixed inset-y-0 left-0 z-50 lg:static lg:z-auto",a?"translate-x-0":"-translate-x-full lg:translate-x-0","transition-transform duration-200 ease-in-out"),children:(0,r.jsx)(_,{collapsed:s,className:"h-full"})}),(0,r.jsxs)("div",{className:"flex flex-1 flex-col overflow-hidden",children:[(0,r.jsxs)("header",{className:"flex h-16 items-center justify-between border-b bg-background px-4 lg:px-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(T.$,{variant:"ghost",size:"icon",className:"lg:hidden",onClick:()=>{n(!a)},children:[(0,r.jsx)(l.A,{className:"size-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Open navigation menu"})]}),(0,r.jsxs)(T.$,{variant:"ghost",size:"icon",className:"hidden lg:flex",onClick:()=>{i(!s)},children:[s?(0,r.jsx)(l.A,{className:"size-5"}):(0,r.jsx)(c.A,{className:"size-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle sidebar"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(L,{}),(0,r.jsx)(O.F,{variant:"dropdown"})]})]}),(0,r.jsx)("main",{className:"flex-1 overflow-auto",children:(0,r.jsx)("div",{className:"h-full p-6",children:e})})]})]})};var $=s(88853),q=s(50616),H=s(64181),B=s(69559),W=s(1305),Z=s(96834),G=s(44493),Y=s(63503),K=s(35950),X=s(54987),J=s(50885),Q=s(8639),ee=s(33938);let et=()=>{let{closeModal:e,isModalOpen:t,modalContent:s}=(0,J.useModal)(),{dashboardLayout:i,fontSize:a,getAllPreferences:n,notificationsEnabled:o,resetPreferences:l,setDashboardLayout:c,setFontSize:d,setTableDensity:u,tableDensity:m,toggleNotifications:h}=(0,U.useUiPreferences)(),{currentTheme:p,setTheme:f}=(0,M.useTheme)(),{setTheme:g,systemTheme:x,theme:y}=(0,z.D)(),v=e=>{g(e),"system"===e?f(x||"light"):f(e)};return(0,r.jsx)(Y.lG,{onOpenChange:t=>!t&&e(),open:t&&"settings"===s,children:(0,r.jsxs)(Y.Cf,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[(0,r.jsxs)(Y.c7,{children:[(0,r.jsxs)(Y.L3,{className:"flex items-center gap-2",children:[(0,r.jsx)(S.A,{className:"size-5"}),"Application Settings"]}),(0,r.jsx)(Y.rr,{children:"Customize your WorkHub experience with these preferences"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"mb-4 flex items-center gap-2 text-lg font-semibold",children:[(0,r.jsx)($.A,{className:"size-5"}),"Theme Preferences"]}),(0,r.jsx)(ee.LG,{})]}),(0,r.jsx)(K.w,{}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"mb-4 flex items-center gap-2 text-lg font-semibold",children:[(0,r.jsx)(q.A,{className:"size-5"}),"Display Preferences"]}),(0,r.jsx)(Q.uq,{})]}),(0,r.jsx)(K.w,{}),(0,r.jsxs)(G.Zp,{children:[(0,r.jsxs)(G.aR,{children:[(0,r.jsxs)(G.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(H.A,{className:"size-5"}),"Notifications"]}),(0,r.jsx)(G.BT,{children:"Control how you receive notifications and alerts"})]}),(0,r.jsx)(G.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Enable Notifications"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive system notifications and updates"})]}),(0,r.jsx)(X.d,{checked:o,onCheckedChange:h})]})})]}),(0,r.jsxs)(G.Zp,{children:[(0,r.jsxs)(G.aR,{children:[(0,r.jsxs)(G.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(B.A,{className:"size-5"}),"Layout Preferences"]}),(0,r.jsx)(G.BT,{children:"Customize the layout and density of interface elements"})]}),(0,r.jsxs)(G.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"mb-3 font-medium",children:"Table Density"}),(0,r.jsx)("div",{className:"flex gap-2",children:["compact","comfortable","spacious"].map(e=>(0,r.jsx)(T.$,{className:"capitalize",onClick:()=>u(e),size:"sm",variant:m===e?"default":"outline",children:e},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"mb-3 font-medium",children:"Dashboard Layout"}),(0,r.jsx)("div",{className:"flex gap-2",children:["grid","list","cards"].map(e=>(0,r.jsx)(T.$,{className:"capitalize",onClick:()=>c(e),size:"sm",variant:i===e?"default":"outline",children:e},e))})]})]})]}),(0,r.jsxs)(G.Zp,{children:[(0,r.jsxs)(G.aR,{children:[(0,r.jsx)(G.ZB,{children:"Current Settings Summary"}),(0,r.jsx)(G.BT,{children:"Overview of your current preferences"})]}),(0,r.jsx)(G.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Theme"}),(0,r.jsx)(Z.E,{className:"capitalize",variant:"secondary",children:y||"system"})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Font Size"}),(0,r.jsx)(Z.E,{className:"capitalize",variant:"secondary",children:a})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Notifications"}),(0,r.jsx)(Z.E,{variant:o?"default":"secondary",children:o?"Enabled":"Disabled"})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Table Density"}),(0,r.jsx)(Z.E,{className:"capitalize",variant:"secondary",children:m})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Dashboard Layout"}),(0,r.jsx)(Z.E,{className:"capitalize",variant:"secondary",children:i})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[(0,r.jsxs)(T.$,{className:"flex items-center gap-2",onClick:()=>{l(),v("system")},variant:"outline",children:[(0,r.jsx)(W.A,{className:"size-4"}),"Reset to Defaults"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(T.$,{onClick:e,variant:"outline",children:"Cancel"}),(0,r.jsx)(T.$,{onClick:e,children:"Save Changes"})]})]})]})]})})};var es=s(94538);function er({children:e,...t}){return(0,r.jsxs)(z.N,{...t,children:[(0,r.jsx)(ei,{}),e]})}function ei(){let{setTheme:e,systemTheme:t,theme:s}=(0,z.D)(),{currentTheme:r,setTheme:i}=(0,es.C)(),[a,n]=u().useState(!1);return null}var ea=s(72963),en=s(3662),eo=s(14975),el=s(99196);let ec=e=>{switch(e){case"delegation-update":return(0,r.jsx)(y.A,{className:"size-4"});case"employee-update":return(0,r.jsx)(x.A,{className:"size-4"});case"error":return(0,r.jsx)(ea.A,{className:"size-4"});case"success":case"task-assigned":return(0,r.jsx)(en.A,{className:"size-4"});case"vehicle-maintenance":return(0,r.jsx)(p.A,{className:"size-4"});case"warning":return(0,r.jsx)(eo.A,{className:"size-4"});default:return(0,r.jsx)(el.A,{className:"size-4"})}},ed=e=>{switch(e){case"delegation-update":return"border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200";case"employee-update":return"border-teal-200 bg-teal-50 text-teal-800 dark:border-teal-800 dark:bg-teal-950 dark:text-teal-200";case"error":return"border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950 dark:text-red-200";case"success":return"border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200";case"task-assigned":return"border-indigo-200 bg-indigo-50 text-indigo-800 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-200";case"vehicle-maintenance":return"border-purple-200 bg-purple-50 text-purple-800 dark:border-purple-800 dark:bg-purple-950 dark:text-purple-200";case"warning":return"border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200";default:return"border-gray-200 bg-gray-50 text-gray-800 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-200"}},eu=e=>{switch(e){case"error":return"destructive";case"success":return"default";case"warning":return"secondary";default:return"outline"}},em=()=>{let{clearAllNotifications:e,markNotificationAsRead:t,notifications:s,removeNotification:i,unreadNotificationCount:a}=(0,es.C)();return 0===s.length?null:(0,r.jsxs)("div",{className:"fixed right-4 top-4 z-50 w-96 max-w-sm space-y-2",children:[s.length>1&&(0,r.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Notifications"}),a()>0&&(0,r.jsx)(Z.E,{className:"text-xs",variant:"destructive",children:a()})]}),(0,r.jsx)(T.$,{className:"text-xs",onClick:e,size:"sm",variant:"ghost",children:"Clear All"})]}),(0,r.jsx)("div",{className:"max-h-96 space-y-2 overflow-y-auto",children:s.slice(-5).map(e=>(0,r.jsx)(G.Zp,{className:(0,R.cn)("relative transition-all duration-300 hover:shadow-md",ed(e.type),!e.read&&"ring-2 ring-offset-2 ring-offset-background"),children:(0,r.jsx)(G.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"mt-0.5 shrink-0",children:ec(e.type)}),(0,r.jsx)("div",{className:"min-w-0 flex-1",children:(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(Z.E,{className:"mb-1 text-xs",variant:eu(e.type),children:e.type.replace("-"," ")}),(0,r.jsx)("p",{className:"text-sm font-medium leading-tight",children:e.message}),(0,r.jsx)("p",{className:"mt-1 text-xs opacity-75",children:new Date(e.timestamp).toLocaleString()}),(e.category||e.actionUrl)&&(0,r.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[e.category&&(0,r.jsx)(Z.E,{className:"text-xs",variant:"outline",children:e.category}),e.actionUrl&&(0,r.jsx)(T.$,{className:"h-auto p-0 text-xs",onClick:()=>window.open(e.actionUrl,"_blank"),size:"sm",variant:"link",children:"View Details"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsx)(T.$,{className:"size-6 p-0",onClick:()=>i(e.id),size:"sm",variant:"ghost",children:(0,r.jsx)(c.A,{className:"size-3"})}),!e.read&&(0,r.jsx)(T.$,{className:"size-6 p-0",onClick:()=>t(e.id),size:"sm",title:"Mark as read",variant:"ghost",children:(0,r.jsx)(en.A,{className:"size-3"})})]})]})})]})})},e.id))})]})};var eh=s(47313),ep=s(24224);let ef=eh.Kq,eg=d.forwardRef(({className:e,...t},s)=>(0,r.jsx)(eh.LM,{className:(0,R.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),ref:s,...t}));eg.displayName=eh.LM.displayName;let ex=(0,ep.F)("group grid items-center gap-1 rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-10 data-[state=open]:fade-in-10 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[swipe=end]:slide-out-to-right-full data-[swipe=start]:slide-out-to-left-full",{defaultVariants:{variant:"default"},variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}}}),ey=d.forwardRef(({className:e,variant:t,...s},i)=>(0,r.jsx)(eh.bL,{className:(0,R.cn)(ex({variant:t}),e),ref:i,...s}));ey.displayName=eh.bL.displayName,d.forwardRef(({className:e,...t},s)=>(0,r.jsx)(eh.rc,{className:(0,R.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),ref:s,...t})).displayName=eh.rc.displayName;let ev=d.forwardRef(({className:e,...t},s)=>(0,r.jsx)(eh.bm,{className:(0,R.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),ref:s,"toast-close":"",...t,children:(0,r.jsx)(c.A,{className:"size-4"})}));ev.displayName=eh.bm.displayName;let eb=d.forwardRef(({className:e,...t},s)=>(0,r.jsx)(eh.hE,{className:(0,R.cn)("text-sm font-semibold",e),ref:s,...t}));eb.displayName=eh.hE.displayName;let eS=d.forwardRef(({className:e,...t},s)=>(0,r.jsx)(eh.VY,{className:(0,R.cn)("text-sm opacity-90",e),ref:s,...t}));eS.displayName=eh.VY.displayName;var eN=s(3389);function ej(){let{toasts:e}=(0,eN.dj)();return(0,r.jsxs)(ef,{children:[e.map(function({action:e,description:t,id:s,title:i,...a}){return(0,r.jsxs)(ey,{...a,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[i&&(0,r.jsx)(eb,{children:i}),t&&(0,r.jsx)(eS,{children:t})]}),e&&u().isValidElement(e)?e:null,(0,r.jsx)(ev,{})]},s)}),(0,r.jsx)(eg,{})]})}var eE=s(8751),ek=s(36644),eA=s(89743);let ew=({className:e=""})=>{let[t,s]=(0,d.useState)(!1),i=(0,a.usePathname)();if(i?.startsWith("/reports"))return null;let n=[{href:"/reports",icon:h.A,label:"Dashboard",description:"Main Analytics",color:"bg-blue-600 hover:bg-blue-700"},{href:"/reports/analytics",icon:eE.A,label:"Analytics",description:"Advanced Insights",color:"bg-purple-600 hover:bg-purple-700"},{href:"/reports/data",icon:ek.A,label:"Data Tables",description:"Raw Data View",color:"bg-green-600 hover:bg-green-700"}];return(0,r.jsxs)("div",{className:(0,R.cn)("fixed bottom-6 right-6 z-50",e),children:[t&&(0,r.jsx)("div",{className:"mb-4 space-y-3",children:n.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3 animate-in slide-in-from-bottom-2 duration-200",style:{animationDelay:`${50*t}ms`},children:[(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border px-3 py-2 text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:e.label}),(0,r.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.description})]}),(0,r.jsx)(T.$,{asChild:!0,size:"lg",className:(0,R.cn)("h-12 w-12 rounded-full shadow-lg border-0 text-white",e.color),children:(0,r.jsxs)(w(),{href:e.href,children:[(0,r.jsx)(e.icon,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:e.label})]})})]},e.href))}),(0,r.jsxs)(T.$,{onClick:()=>s(!t),size:"lg",className:(0,R.cn)("h-14 w-14 rounded-full shadow-lg transition-all duration-200",t?"bg-red-600 hover:bg-red-700 rotate-45":"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"),children:[t?(0,r.jsx)(c.A,{className:"h-6 w-6 text-white"}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-white"}),(0,r.jsx)(eA.A,{className:"h-3 w-3 text-white -mt-1"})]}),(0,r.jsx)("span",{className:"sr-only",children:t?"Close quick access menu":"Open reporting quick access"})]}),!t&&(0,r.jsx)(Z.E,{variant:"destructive",className:"absolute -top-2 -right-2 text-xs px-1.5 py-0.5 animate-pulse",children:"NEW"})]})};var eT=s(17839),eC=s(93778),eR=s(41674);function eI({children:e}){return(0,eR.dD)(),(0,eR.oD)(),(0,r.jsx)(er,{attribute:"class",defaultTheme:"system",disableTransitionOnChange:!0,enableSystem:!0,children:(0,r.jsxs)(i.Ht,{client:eT.qQ,children:[(0,r.jsx)(n.AuthProvider,{children:(0,r.jsx)(eC.sf,{children:(0,r.jsx)(e_,{children:e})})}),(0,r.jsx)(em,{}),(0,r.jsx)(et,{}),(0,r.jsx)(ew,{})]})})}function e_({children:e}){let t=(0,a.usePathname)(),{isInitialized:s,loading:i,user:l}=(0,n.useAuthContext)(),{getFontSizeClass:c}=(0,U.useUiPreferences)(),u=["/auth-test","/supabase-diagnostics","/login"].some(e=>t?.startsWith(e)),m=["/login"].some(e=>t?.startsWith(e));return((0,d.useMemo)(()=>s&&!i&&!!l,[s,i,l?.id]),m)?(0,r.jsxs)(r.Fragment,{children:[e,(0,r.jsx)(ej,{})]}):u?(0,r.jsxs)("div",{className:`app-layout ${c()}`,children:[(0,r.jsx)(V,{children:e}),(0,r.jsx)(ej,{}),(0,r.jsx)("footer",{className:"no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",children:(0,r.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," WorkHub. All rights reserved."]})})]}):(0,r.jsx)("div",{className:`app-layout ${c()}`,children:(0,r.jsxs)(o.OV,{requireEmailVerification:!0,children:[(0,r.jsx)(V,{children:e}),(0,r.jsx)(ej,{}),(0,r.jsx)("footer",{className:"no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",children:(0,r.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," WorkHub. All rights reserved."]})})]})})}},77312:(e,t,s)=>{"use strict";s.d(t,{v:()=>n});var r=s(63924);class i{constructor(e,t=5,s=6e4){this.name=e,this.threshold=t,this.timeout=s,this.failures=0,this.lastFailureTime=0,this.state="CLOSED"}async execute(e){if("OPEN"===this.state)if(Date.now()-this.lastFailureTime>this.timeout)this.state="HALF_OPEN";else throw new r._7("Circuit breaker is OPEN","CIRCUIT_BREAKER_OPEN");try{let t=await e();return this.onSuccess(),t}catch(e){throw this.onFailure(),e}}getState(){return{failures:this.failures,lastFailureTime:this.lastFailureTime,name:this.name,state:this.state}}onFailure(){this.failures++,this.lastFailureTime=Date.now(),this.failures>=this.threshold&&(this.state="OPEN")}onSuccess(){this.failures=0,this.state="CLOSED"}}class a{clear(){this.cache.clear()}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiry?(this.cache.delete(e),null):t.data:null}getStats(){return{keys:[...this.cache.keys()],size:this.cache.size}}invalidate(e){this.cache.delete(e)}invalidatePattern(e){for(let t of this.cache.keys())e.test(t)&&this.cache.delete(t)}set(e,t,s=3e5){this.cache.set(e,{data:t,expiry:Date.now()+s})}constructor(){this.cache=new Map}}class n{constructor(e,t={}){this.apiClient=e,this.config={cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t},this.circuitBreaker=new i(`${this.constructor.name}`,this.config.circuitBreakerThreshold),this.cache=new a,this.metrics={averageResponseTime:0,cacheHitRatio:0,errorCount:0,requestCount:0}}clearCache(){this.cache.clear()}async create(e){return this.executeWithInfrastructure(null,async()=>{let t=this.transformer.toApi?this.transformer.toApi(e):e,s=await this.apiClient.post(this.endpoint,t);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.transformer.fromApi?this.transformer.fromApi(s):s})}async delete(e){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete(`${this.endpoint}/${e}`),this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`)})}async getAll(e){let t=`${this.endpoint}:getAll:${JSON.stringify(e||{})}`;return this.executeWithInfrastructure(t,async()=>{let t,s=new URLSearchParams;if(e)for(let[t,r]of Object.entries(e))null!=r&&s.append(t,String(r));let r=s.toString(),i=r?`${this.endpoint}?${r}`:this.endpoint,a=await this.apiClient.get(i),n={};if(a&&"success"===a.status&&a.data){let e=a.data;Array.isArray(e)?(t=e,a.pagination&&(n={pagination:{hasNext:a.pagination.hasNext??!1,hasPrevious:a.pagination.hasPrevious??!1,limit:a.pagination.limit,page:a.pagination.page,total:a.pagination.total,totalPages:a.pagination.totalPages??Math.ceil(a.pagination.total/a.pagination.limit)}})):e&&Array.isArray(e.data)?(t=e.data,e.pagination?n={pagination:{hasNext:e.pagination.hasNext??!1,hasPrevious:e.pagination.hasPrevious??!1,limit:e.pagination.limit,page:e.pagination.page,total:e.pagination.total,totalPages:e.pagination.totalPages??Math.ceil(e.pagination.total/e.pagination.limit)}}:a.pagination&&(n={pagination:{hasNext:a.pagination.hasNext??!1,hasPrevious:a.pagination.hasPrevious??!1,limit:a.pagination.limit,page:a.pagination.page,total:a.pagination.total,totalPages:a.pagination.totalPages??Math.ceil(a.pagination.total/a.pagination.limit)}})):t=[e]}else if(Array.isArray(a))t=a;else if(a&&(a.error||"error"===a.status))throw Error(a.message||a.error||"API request failed");else if(a&&"object"==typeof a)t=[a];else throw Error(`Invalid response format from API: ${JSON.stringify(a)}`);return{data:t.map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e),...n}})}async getById(e){let t=`${this.endpoint}:getById:${e}`;return this.executeWithInfrastructure(t,async()=>{let t=await this.apiClient.get(`${this.endpoint}/${e}`);return this.transformer.fromApi?this.transformer.fromApi(t):t})}getHealthStatus(){return{cacheStats:this.cache.getStats(),circuitBreakerState:this.circuitBreaker.getState(),endpoint:this.endpoint,metrics:this.metrics,service:this.constructor.name}}resetMetrics(){this.metrics={averageResponseTime:0,cacheHitRatio:0,errorCount:0,requestCount:0}}async update(e,t){return this.executeWithInfrastructure(null,async()=>{let s=this.transformer.toApi?this.transformer.toApi(t):t,r=await this.apiClient.put(`${this.endpoint}/${e}`,s);return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),this.transformer.fromApi?this.transformer.fromApi(r):r})}async executeWithInfrastructure(e,t){let s=Date.now();try{if(this.metrics.requestCount++,e){let t=this.cache.get(e);if(t)return this.metrics.cacheHitRatio=(this.metrics.cacheHitRatio*(this.metrics.requestCount-1)+1)/this.metrics.requestCount,t}let r=await this.circuitBreaker.execute(async()=>o(t,this.config.retryAttempts));e&&r&&this.cache.set(e,r,this.config.cacheDuration);let i=Date.now()-s;return this.metrics.averageResponseTime=(this.metrics.averageResponseTime*(this.metrics.requestCount-1)+i)/this.metrics.requestCount,r}catch(e){if(this.metrics.errorCount++,console.error(`Service error in ${this.constructor.name}:`,{endpoint:this.endpoint,errorDetails:e instanceof Error?{message:e.message,name:e.name,stack:e.stack}:e,errorMessage:e instanceof Error?e.message:String(e),errorType:e?.constructor?.name||typeof e,timestamp:new Date().toISOString()}),e instanceof r._7)throw e;if(e instanceof Error){if(e.message.includes("fetch")||e.message.includes("network"))throw new r._7("Network connection failed. Please check your internet connection and try again.","NETWORK_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name});if(e.message.includes("500")||e.message.includes("Internal Server Error"))throw new r._7("Server error occurred. Please try again later.","SERVER_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name})}throw new r._7(e instanceof Error?e.message:"Unknown service error","SERVICE_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name})}}}async function o(e,t=3,s=1e3){let r;for(let i=1;i<=t;i++)try{return await e()}catch(e){if(r=e instanceof Error?e:Error("Unknown error"),i===t)throw r;await new Promise(e=>setTimeout(e,s*i))}throw r}},78311:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\layout-client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout-client.tsx","default")},79772:(e,t,s)=>{"use strict";s.d(t,{O:()=>o});var r=s(38118),i=s(63924);let a=e=>new Promise(t=>setTimeout(t,e)),n=(e,t=1e3)=>Math.min(t*Math.pow(2,e),3e4);class o{constructor(e){this.baseURL=e.baseURL,this.timeout=e.timeout||1e4,this.retryAttempts=e.retryAttempts||3,this.getAuthToken=e.getAuthToken,this.defaultHeaders={"Content-Type":"application/json",...e.headers}}async delete(e,t){return this.request("DELETE",e,void 0,t)}async get(e,t){return this.request("GET",e,void 0,t)}async patch(e,t,s){return this.request("PATCH",e,t,s)}async post(e,t,s){return this.request("POST",e,t,s)}async put(e,t,s){return this.request("PUT",e,t,s)}async request(e,t,s,o){let l=`${this.baseURL}${t}`,c={...this.defaultHeaders,...o?.headers};if(this.getAuthToken){let e=this.getAuthToken();e&&(c.Authorization=`Bearer ${e}`)}let d={body:s?JSON.stringify(s):null,credentials:"include",headers:c,method:e,signal:(0,r.d$)(o?.signal)},u=new AbortController,m=setTimeout(()=>u.abort(),o?.timeout||this.timeout);d.signal=o?.signal||u.signal;for(let e=0;e<this.retryAttempts;e++)try{let e=await fetch(l,d);clearTimeout(m);let t=await e.json().catch(()=>null);if(!e.ok){if(t&&"error"===t.status)throw new i.hD(t.message,{code:t.code,details:t.error,status:e.status});let s=t?.message||e.statusText;switch(e.status){case 400:throw new i.v7(s,t);case 401:throw new i.v3(s,t);case 404:throw new i.m_(s,t);case 500:throw new i.PO(s,t);default:throw new i.hD(s,{details:t,status:e.status})}}if(t&&"object"==typeof t&&"success"===t.status)return t.data;if(204===e.status)return;return t}catch(s){if(s instanceof i.hD)throw s;if(e===this.retryAttempts-1){let e=s instanceof Error?s.message:String(s);throw new i.Dr(`Request failed after ${this.retryAttempts} attempts: ${e}`)}let t=n(e);await a(t)}throw new i.Dr("Request failed after multiple retries.")}}},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var r=s(60687),i=s(43210),a=s(78148),n=s(24224),o=s(22482);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.b,{ref:s,className:(0,o.cn)(l(),e),...t}));c.displayName=a.b.displayName},81609:(e,t,s)=>{"use strict";s.d(t,{Z8:()=>n,a8:()=>a});var r=s(43210),i=s(41995);function a(e={}){let{client:t,isAuthenticated:s,hasValidToken:n,securityStatus:o,refreshToken:l,refreshSecurityFeatures:c,updateSecurityConfig:d,sanitizeInput:u,isInitialized:m,isLoading:h,error:p}=(0,i.G)(e);return{hasValidToken:n,isAuthenticated:s,refreshToken:l,sanitizeInput:u,secureRequest:(0,r.useCallback)(async e=>{let{data:s,headers:r={},method:i="GET",timeout:a,url:n}=e;try{let e,o={headers:r,timeout:a||1e4};switch(i.toUpperCase()){case"GET":e=await t.get(n,o);break;case"POST":e=await t.post(n,s,o);break;case"PUT":e=await t.put(n,s,o);break;case"PATCH":e=await t.patch(n,s,o);break;case"DELETE":e=await t.delete(n,o);break;default:throw Error(`Unsupported HTTP method: ${i}`)}return{data:e,headers:{},status:200,statusText:"OK"}}catch(e){if(e instanceof Error)throw e;throw Error("Request failed")}},[t]),client:t,securityStatus:o,refreshSecurityFeatures:c,updateSecurityConfig:d,isInitialized:m,isLoading:h,error:p}}function n(e={}){let t=a(e);return{hasValidToken:t.hasValidToken,isAuthenticated:t.isAuthenticated,refreshToken:t.refreshToken,sanitizeInput:t.sanitizeInput,secureRequest:t.secureRequest}}},82421:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},85763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>o});var r=s(60687),i=s(55146),a=s(43210),n=s(22482);let o=i.bL,l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.B8,{className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),ref:s,...t}));l.displayName=i.B8.displayName;let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.l9,{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),ref:s,...t}));c.displayName=i.l9.displayName;let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.UC,{className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),ref:s,...t}));d.displayName=i.UC.displayName},85969:(e,t,s)=>{"use strict";s.d(t,{z:()=>l});var r=s(51528),i=s(79772),a=s(63924),n=s(43939);class o extends i.O{constructor(e,t){super(e),this.lastSecurityCheck=new Date,this.securityInitialized=!1,this.securityConfig={enableAutoLogout:e.enableAutoLogout??!0,enableCSRF:e.enableCSRF??!0,enableInputSanitization:e.enableInputSanitization??!0,enableTokenValidation:e.enableTokenValidation??!0,securityConfig:e.securityConfig??{},validateSecurityFeatures:e.validateSecurityFeatures??!0},this.enhancedSecurityConfig={authentication:{autoLogout:!0,enabled:this.securityConfig.enableAutoLogout,redirectOnFailure:!0},csrf:{enabled:this.securityConfig.enableCSRF,excludePaths:[],tokenHeader:"X-CSRF-Token"},http:{baseURL:e.baseURL||"/api",retryAttempts:e.retryAttempts||3,timeout:e.timeout||1e4},inputSanitization:{enabled:this.securityConfig.enableInputSanitization,sanitizers:["xss","sql"]},tokenValidation:{autoRefresh:!0,enabled:this.securityConfig.enableTokenValidation,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES},...e.securityConfig},this.securityComposer=t,console.log("\uD83D\uDD10 SecureApiClient: Initialized with SecurityUtils integration",{constants:{sessionTimeout:r.$f.SESSION_TIMEOUT_MINUTES,tokenThreshold:r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES},securityFeatures:Object.keys(this.securityConfig).filter(e=>this.securityConfig[e])})}getSecurityStatus(){let e={lastSecurityCheck:this.lastSecurityCheck,securityConstants:r.$f,securityFeaturesEnabled:this.securityConfig,securityFeaturesInitialized:this.securityInitialized,sessionInfo:this.getSessionInfo(),userInfo:this.extractUserFromToken()};if(this.securityComposer){let t=this.securityComposer.getSecurityStatus();return{...e,hasValidToken:t.hasValidToken,isAuthenticated:t.isAuthenticated,threatLevel:this.assessThreatLevel(t)}}return{...e,hasValidToken:!1,isAuthenticated:!1,threatLevel:"critical"}}initializeSecurity(e){try{this.securityConfig.validateSecurityFeatures&&this.validateSecurityFeatures(e),this.securityFeatures=e,this.securityComposer?(this.securityComposer.updateSecurityFeatures(e),this.securityComposer.updateConfig(this.enhancedSecurityConfig)):this.securityComposer=new n.B(e,this.enhancedSecurityConfig),this.securityInitialized=!0,this.lastSecurityCheck=new Date,console.log("\uD83D\uDD10 SecureApiClient: Security features initialized with SecurityUtils integration",{featuresInitialized:Object.keys(e).filter(t=>e[t]),securityConfig:this.enhancedSecurityConfig,timestamp:this.lastSecurityCheck.toISOString()})}catch(e){throw console.error("SecureApiClient: Failed to initialize security features:",e),Error(`Security initialization failed: ${e instanceof Error?e.message:"Unknown error"}`)}}refreshSecurityFeatures(){this.securityFeatures&&this.securityComposer?(this.securityComposer.updateSecurityFeatures(this.securityFeatures),this.lastSecurityCheck=new Date,console.log("\uD83D\uDD04 SecureApiClient: Security features refreshed")):console.warn("SecureApiClient: Cannot refresh - security features not initialized")}async secureRequest(e,t,s,r){if(!this.securityInitialized||!this.securityComposer)throw new a.v3("Security features not initialized. Call initializeSecurity() first.","SECURITY_NOT_INITIALIZED");this.lastSecurityCheck=new Date;let i={hasValidToken:this.securityFeatures?.tokenManagement?.isTokenValid??!1,isAuthenticated:this.securityFeatures?.sessionSecurity?.isSessionActive??!1,session:this.getSessionInfo(),timestamp:this.lastSecurityCheck,user:this.extractUserFromToken()},n={...r,body:s,method:e,url:t};try{await this.performPreRequestSecurityChecks(i);let{body:s,...r}=await this.securityComposer.processRequest(n,i),a=await super[e.toLowerCase()](t,s,r);return await this.performPostRequestSecurityChecks(i),a}catch(e){throw await this.handleSecurityError(e,i),e}}updateSecurityConfig(e){this.securityComposer?(this.securityComposer.updateConfig(e),console.log("\uD83D\uDD10 SecureApiClient: Security configuration updated",e)):console.warn("SecureApiClient: Cannot update config - SecurityComposer not initialized")}assessThreatLevel(e){try{if(!this.securityInitialized||!e.securityFeaturesInitialized)return"critical";if(!e.isAuthenticated||!e.hasValidToken)return"high";let t=r.E9.getSecureItem("auth_token");if(r.E9.detectTimeout()||t&&r.E9.willExpireSoon(t))return"medium";return"low"}catch(e){return console.warn("SecureApiClient: Failed to assess threat level:",e),"medium"}}extractUserFromToken(){try{if(!this.securityFeatures?.tokenManagement?.isTokenValid)return;let e=r.E9.getSecureItem("auth_token");if(!e)return;let t=r.E9.extractEmployeeId(e),s=r.E9.extractUserRole(e);return t||s?{employeeId:t,userRole:s}:void 0}catch(e){console.warn("SecureApiClient: Failed to extract user from token:",e);return}}getSessionInfo(){try{let e=r.E9.getCurrentSessionId(),t=r.E9.detectTimeout();return e?{isTimeout:t,sessionId:e}:void 0}catch(e){console.warn("SecureApiClient: Failed to get session info:",e);return}}async handleSecurityError(e,t){if(!r.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Security error handling blocked by circuit breaker");let s="api-error-handling";if(!r.E9.startSecurityOperation(s))return void console.debug("\uD83D\uDD04 Security error handling already in progress");try{this.securityComposer&&await this.securityComposer.handleError(e,t),e instanceof Error&&((e.message.includes("401")||e.message.includes("Unauthorized")||e.message.includes("Authentication"))&&(console.warn("\uD83D\uDD10 SecureApiClient: Authentication error detected"),r.E9.recordSecurityAttempt(),await this.attemptSessionRecovery()||(r.E9.clearAllCookies(),console.log("\uD83E\uDDF9 Cleared secure storage due to auth failure"))),(e.message.includes("CSRF")||e.message.includes("403"))&&(console.warn("\uD83D\uDEE1️ SecureApiClient: CSRF error detected"),r.E9.recordSecurityAttempt()),(e.message.includes("Network")||e.message.includes("fetch"))&&console.warn("\uD83C\uDF10 SecureApiClient: Network error detected"),(e.message.includes("timeout")||e.message.includes("Timeout"))&&(console.warn("⏰ SecureApiClient: Timeout error detected"),r.E9.recordSecurityAttempt()))}catch(e){console.error("SecureApiClient: Error in security error handling:",e),r.E9.recordSecurityAttempt()}finally{r.E9.endSecurityOperation(s)}}async performPostRequestSecurityChecks(e){try{e.isAuthenticated&&this.securityFeatures?.sessionSecurity&&this.securityFeatures.sessionSecurity.updateActivity()}catch(e){console.warn("SecureApiClient: Post-request security check failed:",e)}}async performPreRequestSecurityChecks(e){if(!r.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Pre-request security checks blocked by circuit breaker");let t="pre-request-security-check";if(!r.E9.startSecurityOperation(t))return void console.debug("\uD83D\uDD04 Pre-request security check already in progress");try{if(r.E9.detectTimeout()&&(console.warn("⏰ Session timeout detected in pre-request check"),r.E9.recordSecurityAttempt(),!await this.attemptSessionRecovery()))throw new a.v3("Session has timed out","SESSION_TIMEOUT");if(e.hasValidToken&&this.securityFeatures?.tokenManagement){let{isTokenExpired:e}=this.securityFeatures.tokenManagement,t=r.E9.getSecureItem("auth_token");if(e||t&&r.E9.willExpireSoon(t)){console.warn("\uD83D\uDD04 SecureApiClient: Token will expire soon, attempting refresh");try{await this.securityFeatures.tokenManagement.refreshToken()?(console.log("✅ Token refreshed successfully"),r.E9.recordSecuritySuccess()):(console.warn("❌ Token refresh failed"),r.E9.recordSecurityAttempt())}catch(e){console.error("❌ Token refresh error:",e),r.E9.recordSecurityAttempt()}}}r.E9.recordSecuritySuccess()}catch(e){throw console.error("SecureApiClient: Pre-request security check failed:",e),r.E9.recordSecurityAttempt(),e}finally{r.E9.endSecurityOperation(t)}}async attemptSessionRecovery(){try{console.log("\uD83D\uDD27 Attempting session recovery...");let{SessionManager:e}=await Promise.resolve().then(s.bind(s,26461));if(await e.performIntegrityCheck())return console.log("✅ Session integrity check passed"),!0;if(e.recoverFromCorruptedState())return console.log("✅ Session state recovered successfully"),!0;return console.warn("❌ Session recovery failed"),!1}catch(e){return console.error("❌ Session recovery error:",e),!1}}validateSecurityFeatures(e){let t=[];if(this.securityConfig.enableTokenValidation&&e.tokenManagement){let{isTokenExpired:s,isTokenValid:r}=e.tokenManagement;("boolean"!=typeof r||"boolean"!=typeof s)&&t.push("Token management features must provide boolean status indicators")}if(this.securityConfig.enableCSRF&&e.csrfProtection&&"function"!=typeof e.csrfProtection.attachCSRF&&t.push("CSRF protection must provide attachCSRF function"),this.securityConfig.enableInputSanitization&&e.inputValidation&&"function"!=typeof e.inputValidation.sanitizeInput&&t.push("Input validation must provide sanitizeInput function"),this.securityConfig.enableAutoLogout&&e.sessionSecurity){let{clearSession:s,isSessionActive:r}=e.sessionSecurity;("boolean"!=typeof r||"function"!=typeof s)&&t.push("Session security must provide boolean status and clearSession function")}if(t.length>0)throw Error(`Security feature validation failed: ${t.join(", ")}`)}}function l(e,t){let s,i={authentication:{autoLogout:!0,enabled:!0,redirectOnFailure:!0},csrf:{enabled:!0,excludePaths:[],tokenHeader:"X-CSRF-Token"},http:{baseURL:"/api",retryAttempts:3,timeout:1e4},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},tokenValidation:{autoRefresh:!0,enabled:!0,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES}},a={enableAutoLogout:!0,enableCSRF:!0,enableInputSanitization:!0,enableTokenValidation:!0,validateSecurityFeatures:!0,...e,securityConfig:{...i,...e.securityConfig}};t&&(s=new n.B(t,a.securityConfig));let l=new o(a,s);return console.log("\uD83C\uDFED SecureApiClient Factory: Created enhanced secure API client",{config:{autoLogout:a.enableAutoLogout,csrf:a.enableCSRF,inputSanitization:a.enableInputSanitization,tokenValidation:a.enableTokenValidation},securityConstants:r.$f,securityFeatures:t?Object.keys(t):[]}),l}},88790:(e,t,s)=>{"use strict";s.d(t,{j8:()=>a});var r=s(43210),i=s(46308);function a(e){let[t,s]=(0,r.useState)({isValid:!0,errors:{},touched:{},isValidating:!1}),a=(0,r.useCallback)((e,t,r)=>{s(e=>({...e,isValidating:!0}));let a=i.B.validateValue(t,r);return s(t=>({...t,isValidating:!1,errors:{...t.errors,[e]:a.isValid?[]:a.errors},isValid:a.isValid&&Object.values({...t.errors,[e]:a.isValid?[]:a.errors}).every(e=>0===e.length)})),a},[]),n=(0,r.useCallback)((e,t)=>{s(e=>({...e,isValidating:!0}));let r=i.B.validateObject(e,t),a={};return r.errors.forEach(e=>{let[t,...s]=e.split(": "),r=s.join(": ");t&&(a[t]||(a[t]=[]),a[t].push(r))}),s(e=>({...e,isValidating:!1,errors:a,isValid:r.isValid})),r},[]),o=(0,r.useCallback)((e,t=!0)=>{s(s=>({...s,touched:{...s.touched,[e]:t}}))},[]),l=(0,r.useCallback)(e=>{s(t=>({...t,errors:{...t.errors,[e]:[]},isValid:Object.values({...t.errors,[e]:[]}).every(e=>0===e.length)}))},[]),c=(0,r.useCallback)(()=>{s(e=>({...e,errors:{},isValid:!0}))},[]),d=(0,r.useCallback)(()=>{s({isValid:!0,errors:{},touched:{},isValidating:!1})},[]),u=(0,r.useCallback)(e=>{if("string"==typeof e)return i.B.sanitizeForXSS(e);if(Array.isArray(e))return e.map(e=>u(e));if(e&&"object"==typeof e){let t={};for(let[s,r]of Object.entries(e))t[s]=u(r);return t}return e},[]),m=(0,r.useCallback)(e=>{let s=t.errors[e];return s&&s.length>0&&s[0]||null},[t.errors]),h=(0,r.useCallback)(e=>{let s=t.errors[e];return!!s&&s.length>0},[t.errors]),p=(0,r.useCallback)(e=>t.touched[e]||!1,[t.touched]);return{isValid:t.isValid,errors:t.errors,touched:t.touched,isValidating:t.isValidating,validateField:a,validateForm:n,setFieldTouched:o,clearFieldErrors:l,clearAllErrors:c,resetValidation:d,sanitizeInput:u,getFieldError:m,hasFieldError:h,isFieldTouched:p}}},89513:(e,t,s)=>{"use strict";s.d(t,{Q:()=>l});var r=s(23133),i=s(26461),a=s(65931);let n={baseRetryDelay:1e3,enableDebugLogging:!1,maxRetryAttempts:3,refreshBeforeExpiryMinutes:5};class o{static{this.instance=null}constructor(e={}){this.callbacks=new Set,this.currentSession=null,this.isRefreshing=!1,this.isTabVisible=!0,this.refreshTimeout=null,this.retryAttempts=0,this.retryTimeout=null,this.config={...n,...e},this.setupVisibilityHandling(),this.log("TokenRefreshService initialized")}static getInstance(e){return o.instance||(o.instance=new o(e)),o.instance}async getSessionInfo(){try{let{data:{session:e},error:t}=await a.N.auth.getSession();if(t)return this.log("Error getting session info",{error:t}),i.SessionManager.handleSessionValidation(!1,{error:t.message}),{error:t.message,isValid:!1};if(!e)return this.log("No active session found"),i.SessionManager.handleSessionValidation(!1,{error:"No active session"}),{error:"No active session",isValid:!1};let s=Math.floor(Date.now()/1e3),r=!!e.expires_at&&e.expires_at<s,n={isExpired:r,isValid:!r,user:{id:e.user.id}};return e.expires_at&&(n.expiresAt=e.expires_at),e.user.email&&(n.user.email=e.user.email),e.user.user_metadata?.role&&(n.user.role=e.user.user_metadata.role),n}catch(e){return this.log("Exception in getSessionInfo",{error:e}),{error:e instanceof Error?e.message:"Unknown error",isValid:!1}}}async refreshNow(){return this.isRefreshing?(this.log("Refresh already in progress, skipping"),!1):this.performRefresh()}stop(){this.clearScheduledRefresh(),this.clearRetryTimeout(),this.callbacks.clear(),this.currentSession=null,this.isRefreshing=!1,this.retryAttempts=0,this.log("TokenRefreshService stopped")}subscribe(e){return this.callbacks.add(e),()=>this.callbacks.delete(e)}updateSession(e){this.currentSession=e,e?(this.scheduleRefresh(e),this.log("Session updated, refresh scheduled",{expiresAt:new Date(1e3*e.expires_at).toISOString()})):(this.clearScheduledRefresh(),this.log("Session cleared, refresh cancelled"))}clearRetryTimeout(){this.retryTimeout&&(clearTimeout(this.retryTimeout),this.retryTimeout=null)}clearScheduledRefresh(){this.refreshTimeout&&(clearTimeout(this.refreshTimeout),this.refreshTimeout=null)}emitEvent(e,t){for(let s of this.callbacks)try{s(e,t)}catch(e){console.error("Error in token refresh callback:",e)}}handleRefreshFailure(e){if(this.retryAttempts++,this.retryAttempts>=this.config.maxRetryAttempts){this.log("Max retry attempts reached, giving up and signaling critical failure"),this.emitEvent("critical_refresh_failed",{attempts:this.retryAttempts,error:"Max retry attempts exceeded, session unrecoverable",...e});return}let t=this.config.baseRetryDelay*Math.pow(2,this.retryAttempts-1);this.log("Scheduling retry",{attempt:this.retryAttempts,delayMs:t}),this.retryTimeout=setTimeout(()=>{this.performRefresh()},t)}log(e,t){this.config.enableDebugLogging&&console.log(`🔄 TokenRefreshService: ${e}`,t||"")}async performRefresh(){if(this.isRefreshing)return!1;this.isRefreshing=!0,this.log("Starting token refresh");try{let e={"Content-Type":"application/json"};this.currentSession?.access_token&&(e.Authorization=`Bearer ${this.currentSession.access_token}`);let t={};this.currentSession?.refresh_token?(t.refresh_token=this.currentSession.refresh_token,this.log("Including refresh token in request body")):this.log("Warning: No refresh token available in current session");let s=(0,r.Qq)().apiBaseUrl,n=await fetch(`${s}/auth/refresh`,{credentials:"include",headers:e,method:"POST",body:JSON.stringify(t)});if(n.ok){let e=await n.json();this.log("Token refresh successful",{expiresIn:e.expiresIn}),this.retryAttempts=0,this.clearRetryTimeout();try{let{newTokens:t}=e;t?.session&&t?.user?(await a.N.auth.setSession({access_token:t.session.access_token,refresh_token:t.session.refresh_token}),this.currentSession=t.session,this.log("Supabase session explicitly updated with new tokens")):this.log("Warning: New tokens from backend did not contain full session/user data",{data:e})}catch(e){this.log("Error explicitly updating Supabase session after refresh",{sessionUpdateError:e})}return this.emitEvent("refresh_success",e),i.SessionManager.handleTokenRefresh(!0,e),!0}{let e=await n.json().catch(()=>({}));return this.log("Token refresh failed",{error:e,status:n.status}),this.handleRefreshFailure(e),i.SessionManager.handleTokenRefresh(!1,e),!1}}catch(e){return this.log("Token refresh error",{error:e instanceof Error?e.message:String(e)}),this.handleRefreshFailure({error:"Network error"}),i.SessionManager.handleTokenRefresh(!1,{error:"Network error"}),!1}finally{this.isRefreshing=!1}}scheduleRefresh(e){if(this.clearScheduledRefresh(),!e.expires_at)return void this.log("No expiration time in session, cannot schedule refresh");let t=1e3*e.expires_at-60*this.config.refreshBeforeExpiryMinutes*1e3,s=Math.max(0,t-Date.now());if(0===s){this.log("Token expired or about to expire, refreshing immediately"),this.performRefresh();return}this.refreshTimeout=setTimeout(()=>{this.performRefresh()},s),this.log("Refresh scheduled",{delayMinutes:Math.round(s/6e4),refreshAt:new Date(t).toISOString()}),this.emitEvent("refresh_scheduled",{delay:s,refreshAt:t})}setupVisibilityHandling(){"undefined"!=typeof document&&document.addEventListener("visibilitychange",()=>{if(this.isTabVisible=!document.hidden,this.isTabVisible&&this.currentSession){let e=Date.now();1e3*(this.currentSession.expires_at||0)-e<=60*this.config.refreshBeforeExpiryMinutes*1e3&&(this.log("Tab visible and token needs refresh"),this.performRefresh())}})}}let l=e=>o.getInstance(e)},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687),i=s(43210),a=s(22482);let n=i.forwardRef(({className:e,type:t,...s},i)=>(0,r.jsx)("input",{className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:i,type:t,...s}));n.displayName="Input"},90811:(e,t,s)=>{Promise.resolve().then(s.bind(s,78311)),Promise.resolve().then(s.bind(s,30887))},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>l,TN:()=>d,XL:()=>c});var r=s(60687),i=s(24224),a=s(43210),n=s(22482);let o=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),l=a.forwardRef(({className:e,variant:t,...s},i)=>(0,r.jsx)("div",{className:(0,n.cn)(o({variant:t}),e),ref:i,role:"alert",...s}));l.displayName="Alert";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h5",{className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),ref:s,...t}));c.displayName="AlertTitle";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),ref:s,...t}));d.displayName="AlertDescription"},93778:(e,t,s)=>{"use strict";s.d(t,{sf:()=>l.sf,Sk:()=>n,a8:()=>o.a8}),s(43280),s(88790);var r=s(43210),i=s(63213),a=s(11734);function n(){let{user:e,userRole:t,loading:s}=(0,i.useAuthContext)(),n=(0,r.useMemo)(()=>!!e&&!s,[e,s]),o=(0,r.useMemo)(()=>{if(!n||!t)return null;let e=t.toUpperCase();return["USER","ADMIN","SUPER_ADMIN"].includes(e)?e:"USER"},[n,t]),l=(0,r.useCallback)(e=>!!o&&a.B.hasPermission(o,e).hasPermission,[o]),c=(0,r.useCallback)(e=>!!o&&a.B.hasAllPermissions(o,e).hasPermission,[o]),d=(0,r.useCallback)(e=>!!o&&a.B.hasAnyPermission(o,e).hasPermission,[o]),u=(0,r.useCallback)(e=>!!o&&a.B.hasMinimumRole(o,e),[o]),m=(0,r.useCallback)(e=>o?a.B.hasPermission(o,e):{hasPermission:!1,reason:"User not authenticated"},[o]),h=(0,r.useCallback)(()=>o?a.B.getPermissionsForRole(o):[],[o]),p=(0,r.useMemo)(()=>"USER"===o,[o]),f=(0,r.useMemo)(()=>"ADMIN"===o,[o]),g=(0,r.useMemo)(()=>"SUPER_ADMIN"===o,[o]);return{userRole:o,isAuthenticated:n,hasPermission:l,hasAllPermissions:c,hasAnyPermission:d,hasMinimumRole:u,getPermissionCheck:m,getAllPermissions:h,isUser:p,isAdmin:f,isSuperAdmin:g}}s(53194);var o=s(81609);s(41995),s(51528);s(54995),s(67079),s(89513);var l=s(39965);s(43939),s(85969)},94538:(e,t,s)=>{"use strict";s.d(t,{C:()=>a});var r=s(26787),i=s(59350);let a=(0,r.v)()((0,i.lt)((0,i.Zr)((e,t)=>({addNotification:t=>e(e=>({notifications:[...e.notifications,{...t,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:t=>e(e=>({notifications:e.notifications.map(e=>e.id===t?{...e,read:!0}:e)})),notifications:[],removeNotification:t=>e(e=>({notifications:e.notifications.filter(e=>e.id!==t)})),setTheme:t=>{e({currentTheme:t})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=t();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))},95989:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687),i=s(24224);s(43210);var a=s(22482);let n=(0,i.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,a.cn)(n({variant:t}),e),...s})}}};