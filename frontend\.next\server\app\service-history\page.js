(()=>{var e={};e.id=2003,e.ids=[2003],e.modules={997:(e,t,r)=>{"use strict";r.d(t,{k:()=>f});var s=r(60687),i=r(28946),n=r(11516),a=r(20620),o=r(36644);let l=(0,r(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var c=r(43210),d=r(68752),u=r(21342),h=r(3940),p=r(22482),m=r(22364);function f({className:e,csvData:t,enableCsv:r=!1,entityId:f,fileName:x,reportContentId:v,reportType:y,tableId:j}){let[b,g]=(0,c.useState)(!1),[S,_]=(0,c.useState)(!1),{showFormSuccess:N,showFormError:w}=(0,h.t6)(),C=async()=>{g(!0);try{let e=`/api/reports/${y}${f?`/${f}`:""}`,t=document.createElement("a");t.href=e,t.download=`${x}.pdf`,t.target="_blank",document.body.append(t),t.click(),t.remove(),N({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),w(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{g(!1)}},R=async()=>{if(r){_(!0);try{if(t?.data&&t.headers)(0,m.og)(t.data,t.headers,`${x}.csv`);else if(j){let e=(0,m.tL)(j);(0,m.og)(e.data,e.headers,`${x}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");N({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),w(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{_(!1)}}},A=b||S;return(0,s.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 no-print",e),children:[(0,s.jsx)(d.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,s.jsx)(i.A,{className:"size-4"})}),(0,s.jsxs)(u.rI,{children:[(0,s.jsx)(u.ty,{asChild:!0,children:(0,s.jsx)(d.r,{actionType:"secondary","aria-label":"Download report",disabled:A,size:"icon",title:"Download Report",children:A?(0,s.jsx)(n.A,{className:"size-4 animate-spin"}):(0,s.jsx)(a.A,{className:"size-4"})})}),(0,s.jsxs)(u.SQ,{align:"end",children:[(0,s.jsxs)(u._2,{disabled:b,onClick:C,children:[b?(0,s.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(o.A,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download PDF"})]}),r&&(0,s.jsxs)(u._2,{disabled:S,onClick:R,children:[S?(0,s.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(l,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12662:(e,t,r)=>{"use strict";r.d(t,{AppBreadcrumb:()=>u});var s=r(60687),i=r(85814),n=r.n(i),a=r(16189),o=r(43210),l=r.n(o),c=r(70640),d=r(22482);function u({className:e,homeHref:t="/",homeLabel:r="Dashboard",showContainer:i=!0}){let o=(0,a.usePathname)(),u=o?o.split("/").filter(Boolean):[],h=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let t={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return t[e]?t[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=u.map((e,t)=>{let r="/"+u.slice(0,t+1).join("/"),i=t===u.length-1,a=h(e);return(0,s.jsxs)(l().Fragment,{children:[(0,s.jsx)(c.BreadcrumbItem,{children:i?(0,s.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:a}):(0,s.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:r,children:a})})}),!i&&(0,s.jsx)(c.BreadcrumbSeparator,{})]},r)}),m=(0,s.jsx)(c.Breadcrumb,{className:(0,d.cn)("text-sm",e),children:(0,s.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,s.jsx)(c.BreadcrumbItem,{children:(0,s.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,s.jsx)(n(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:r})})}),u.length>0&&(0,s.jsx)(c.BreadcrumbSeparator,{}),p]})});return i?(0,s.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,s.jsx)("div",{className:"flex items-center",children:m})}):m}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},25373:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\service-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\service-history\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28946:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42935:(e,t,r)=>{Promise.resolve().then(r.bind(r,25373))},54050:(e,t,r)=>{"use strict";r.d(t,{n:()=>d});var s=r(43210),i=r(65406),n=r(33465),a=r(35536),o=r(31212),l=class extends a.Q{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#i(),this.#n()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#i(){let e=this.#r?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},c=r(8693);function d(e,t){let r=(0,c.jE)(t),[i]=s.useState(()=>new l(r,e));s.useEffect(()=>{i.setOptions(e)},[i,e]);let a=s.useSyncExternalStore(s.useCallback(e=>i.subscribe(n.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=s.useCallback((e,t)=>{i.mutate(e,t).catch(o.lQ)},[i]);if(a.error&&(0,o.GU)(i.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:d,mutateAsync:a.mutate}}},54967:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>M});var s=r(60687),i=r(76180),n=r.n(i),a=r(44610),o=r(35265),l=r(78726),c=r(41936),d=r(24920),u=r(29333),h=r(85814),p=r.n(h),m=r(43210),f=r(95668);let x=({children:e,fallback:t})=>(0,s.jsx)(f.A,{description:"An unexpected error occurred while loading service records.",fallback:t,onError:(e,t)=>{console.error("ServiceRecords component error:",e),console.error("Component stack:",t.componentStack)},resetLabel:"Try Again",title:"Error Loading Service Records",children:e});var v=r(997),y=r(24847),j=r(68752),b=r(12662),g=r(96834),S=r(29523),_=r(44493),N=r(89667),w=r(80013),C=r(52027),R=r(48041),A=r(15079),k=r(85726),z=r(63213),F=r(2775),P=r(72273);let E=["Oil Change","Tire Rotation","Brake Service","Battery Replacement","Air Filter Replacement","Cabin Air Filter Replacement","Wiper Blade Replacement","Fluid Check/Top-up","Spark Plug Replacement","Coolant Flush","Transmission Service","Wheel Alignment","State Inspection","Other"];function M(){let{loading:e,session:t,user:r}=(0,z.useAuthContext)(),i=!!r&&!!t?.access_token,{data:h,error:M,isLoading:O,refetch:q}=(0,P.T$)({enabled:i&&!e}),B=(0,m.useMemo)(()=>h||[],[h]),{data:T,error:L,isLoading:D,refetch:I}=(0,F.fs)({enabled:i&&!e}),$=(0,m.useMemo)(()=>T||[],[T]),[V,H]=(0,m.useState)("all"),[G,W]=(0,m.useState)("all"),[K,U]=(0,m.useState)(""),[Z,J]=(0,m.useState)(""),Q=(0,m.useMemo)(()=>{let e=new Set;return $.forEach(t=>{t.servicePerformed.forEach(t=>e.add(t))}),E.forEach(t=>e.add(t)),[...e].sort()},[$]),Y=(0,m.useMemo)(()=>{let e=[...$];if("all"!==V&&(e=e.filter(e=>String(e.vehicleId)===V)),"all"!==G&&(e=e.filter(e=>e.servicePerformed.includes(G))),Z){let t=Z.toLowerCase();e=e.filter(e=>`${e.vehicleMake} ${e.vehicleModel}`.toLowerCase().includes(t)||e.servicePerformed.join(" ").toLowerCase().includes(t)||e.notes?.toLowerCase().includes(t)||e.licensePlate?.toLowerCase().includes(t)||e.odometer.toString().includes(t))}return e},[$,V,G,Z]),X=(0,m.useCallback)(()=>{"function"==typeof q&&q(),"function"==typeof I&&I()},[q,I]),ee=e||O||D,et=(0,m.useMemo)(()=>{let e=[];return M&&e.push(`Vehicles: ${M.message}`),L&&e.push(`Service Records: ${L.message}`),e.length>0?e.join("; "):null},[M,L]);if(ee)return(0,s.jsx)(f.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(R.z,{icon:a.A,title:"Service History",children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(k.E,{className:"h-10 w-32"}),(0,s.jsx)(k.E,{className:"h-10 w-32"})]})}),(0,s.jsx)(_.Zp,{children:(0,s.jsx)(_.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,s.jsx)(k.E,{className:"h-10 w-full"}),(0,s.jsx)(k.E,{className:"h-10 w-full"}),(0,s.jsx)(k.E,{className:"h-10 w-full"})]})})}),(0,s.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-4 border-b p-4",children:[(0,s.jsx)(k.E,{className:"h-6 w-1/6"}),(0,s.jsx)(k.E,{className:"h-6 w-1/6"}),(0,s.jsx)(k.E,{className:"h-6 w-2/6"}),(0,s.jsx)(k.E,{className:"h-6 w-1/6"}),(0,s.jsx)(k.E,{className:"h-6 w-1/6"})]},t))})]})});if(!e&&!i)return(0,s.jsx)(f.A,{children:(0,s.jsxs)("div",{className:"space-y-6 text-center",children:[(0,s.jsx)(R.z,{icon:a.A,title:"Access Denied"}),(0,s.jsx)("p",{children:"Please sign in to view service history."})]})});if(!ee&&i&&0===Y.length&&!et){let e="all"!==V||"all"!==G||K;return(0,s.jsx)(f.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(b.AppBreadcrumb,{}),(0,s.jsx)(R.z,{description:"View and manage all service records for your vehicles.",icon:a.A,title:"Service History Report",children:(0,s.jsx)("div",{className:"no-print flex gap-2",children:(0,s.jsx)(j.r,{actionType:"tertiary",asChild:!0,icon:(0,s.jsx)(o.A,{className:"size-4"}),children:(0,s.jsx)(p(),{href:"/vehicles",children:"Log New Service"})})})}),(0,s.jsx)(C.pp,{description:e?"There are no service records matching your current filters. You can log a new service record or adjust your filters.":"No service records have been logged yet. Get started by logging your first service record.",icon:a.A,primaryAction:{href:"/vehicles",icon:(0,s.jsx)(o.A,{className:"size-4"}),label:"Log New Service"},title:"No Service Records Found",...e&&{secondaryAction:{icon:(0,s.jsx)(l.A,{className:"size-4"}),label:"Reset Filters",onClick:()=>{H("all"),W("all"),U("")}}}})]})})}return(0,s.jsx)(f.A,{children:(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9 print-container space-y-6",children:[(0,s.jsx)(b.AppBreadcrumb,{}),(0,s.jsx)(R.z,{description:"View and manage all service records for your vehicles.",icon:a.A,title:"Service History Report",children:(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9 no-print flex gap-2",children:[(0,s.jsx)(j.r,{actionType:"tertiary",asChild:!0,icon:(0,s.jsx)(o.A,{className:"size-4"}),children:(0,s.jsx)(p(),{href:"/vehicles",children:"Log New Service"})}),(0,s.jsx)(v.k,{enableCsv:Y.length>0,fileName:`service-history-report-${new Date().toISOString().split("T")[0]}`,reportContentId:"#service-history-report-content",reportType:"service-history",tableId:"#service-history-table"})]})}),(0,s.jsxs)(_.Zp,{className:"no-print shadow-sm",children:[(0,s.jsxs)(_.aR,{className:"pb-2",children:[(0,s.jsx)(_.ZB,{className:"text-lg",children:"Filter Options"}),(0,s.jsx)(_.BT,{children:"Filter service records by vehicle, service type, or search terms"})]}),(0,s.jsxs)(_.Wu,{children:[(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9 filter-grid grid grid-cols-1 items-end gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,s.jsx)(w.J,{htmlFor:"vehicle-filter",children:"Filter by Vehicle"}),(0,s.jsxs)(A.l6,{"aria-label":"Filter by vehicle",onValueChange:H,value:V,children:[(0,s.jsx)(A.bq,{className:"mt-1.5 w-full",id:"vehicle-filter",children:(0,s.jsx)(A.yv,{placeholder:"All Vehicles"})}),(0,s.jsxs)(A.gC,{children:[(0,s.jsx)(A.eb,{value:"all",children:"All Vehicles"}),B.map(e=>(0,s.jsxs)(A.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,")"]},e.id))]})]})]}),(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,s.jsx)(w.J,{htmlFor:"service-filter",children:"Filter by Service Type"}),(0,s.jsxs)(A.l6,{"aria-label":"Filter by service type",onValueChange:W,value:G,children:[(0,s.jsx)(A.bq,{className:"mt-1.5 w-full",id:"service-filter",children:(0,s.jsx)(A.yv,{placeholder:"All Services"})}),(0,s.jsxs)(A.gC,{children:[(0,s.jsx)(A.eb,{value:"all",children:"All Services"}),Q.map(e=>(0,s.jsx)(A.eb,{value:e,children:e},e))]})]})]}),(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9",children:[(0,s.jsx)(w.J,{htmlFor:"search-records",children:"Search Records"}),(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9 relative mt-1.5",children:[(0,s.jsx)(N.p,{"aria-label":"Search service records",className:"px-10",id:"search-records",onChange:e=>U(e.target.value),placeholder:"Search by keyword, notes, plate...",type:"text",value:K}),(0,s.jsx)(c.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),K&&(0,s.jsxs)(S.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>U(""),size:"icon",variant:"ghost",children:[(0,s.jsx)(l.A,{className:"size-4"}),(0,s.jsx)("span",{className:"jsx-fb311328cf4436d9 sr-only",children:"Clear search"})]})]})]})]}),("all"!==V||"all"!==G||K)&&(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9 mt-6 flex flex-wrap items-center justify-between rounded-md border border-border p-3",children:[(0,s.jsxs)("div",{className:"jsx-fb311328cf4436d9 flex flex-wrap items-center gap-2",children:[(0,s.jsx)("span",{className:"jsx-fb311328cf4436d9 text-sm font-medium",children:"Active Filters:"}),"all"!==V&&(0,s.jsxs)(g.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,s.jsx)(d.A,{className:"size-3"}),B.find(e=>String(e.id)===V)?.make," ",B.find(e=>String(e.id)===V)?.model]}),"all"!==G&&(0,s.jsxs)(g.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,s.jsx)(u.A,{className:"size-3"}),G]}),K&&(0,s.jsxs)(g.E,{className:"flex items-center gap-1",variant:"outline",children:[(0,s.jsx)(c.A,{className:"size-3"}),'"',K,'"']})]}),(0,s.jsxs)(S.$,{"aria-label":"Reset all filters",className:"mt-2 sm:mt-0",onClick:()=>{H("all"),W("all"),U("")},size:"sm",variant:"outline",children:[(0,s.jsx)(l.A,{className:"mr-1 size-3"}),"Reset Filters"]})]})]})]}),(0,s.jsxs)("div",{id:"service-history-report-content",className:"jsx-fb311328cf4436d9 report-content",children:[(0,s.jsx)(x,{children:(0,s.jsx)(y.R,{error:et,isLoading:D&&!T,onRetry:X,records:Y,showVehicleInfo:!0,vehicleSpecific:!1})}),(0,s.jsxs)("footer",{className:"jsx-fb311328cf4436d9 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,s.jsxs)("p",{className:"jsx-fb311328cf4436d9",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,s.jsx)("p",{className:"jsx-fb311328cf4436d9",children:"WorkHub - Vehicle Service Management"})]})]}),(0,s.jsx)(n(),{id:"fb311328cf4436d9",children:""})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70640:(e,t,r)=>{"use strict";r.d(t,{Breadcrumb:()=>l,BreadcrumbItem:()=>d,BreadcrumbLink:()=>u,BreadcrumbList:()=>c,BreadcrumbPage:()=>h,BreadcrumbSeparator:()=>p});var s=r(60687),i=r(8730),n=r(74158),a=(r(69795),r(43210)),o=r(22482);let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("nav",{"aria-label":"breadcrumb",className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:r,...t}));l.displayName="Breadcrumb";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("ol",{className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:r,...t}));c.displayName="BreadcrumbList";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("li",{className:(0,o.cn)("inline-flex items-center gap-1.5",e),ref:r,...t}));d.displayName="BreadcrumbItem";let u=a.forwardRef(({asChild:e,className:t,...r},n)=>{let a=e?i.DX:"a";return(0,s.jsx)(a,{className:(0,o.cn)("transition-colors hover:text-foreground",t),ref:n,...r})});u.displayName="BreadcrumbLink";let h=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,o.cn)("font-normal text-foreground",e),ref:r,role:"link",...t}));h.displayName="BreadcrumbPage";let p=({children:e,className:t,...r})=>(0,s.jsx)("span",{"aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",t),role:"presentation",...r,children:e??(0,s.jsx)(n.A,{className:"size-4"})});p.displayName="BreadcrumbSeparator"},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,r)=>{"use strict";r(56397);var s=r(43210),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),n="undefined"!=typeof process&&process.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,i=t.optimizeForSpeed,o=void 0===i?n:i;l(a(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",l("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){return l(a(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},r.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},r.deleteRule=function(e){this._serverSheet.deleteRule(e)},r.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},r.cssRules=function(){return this._serverSheet.cssRules},r.makeStyleTag=function(e,t,r){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(s,r):i.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function u(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return d[s]||(d[s]="jsx-"+c(e+"-"+r)),d[s]}function h(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,i=t.optimizeForSpeed,n=void 0!==i&&i;this._sheet=s||new o({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),s&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,i=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var n=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=n,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var i=u(s,r);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return h(i,e)}):[h(i,t)]}}return{styleId:u(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=s.createContext(null);m.displayName="StyleSheetContext";i.default.useInsertionEffect||i.default.useLayoutEffect;var f=void 0;function x(e){var t=f||s.useContext(m);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=x},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79727:(e,t,r)=>{Promise.resolve().then(r.bind(r,54967))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88630:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),i=r(48088),n=r(88170),a=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["service-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,25373)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\service-history\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\WorkHub\\frontend\\src\\app\\service-history\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/service-history/page",pathname:"/service-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,211,1658,8390,2670,4897,101,5825,5782,5009,9637,4423],()=>r(88630));module.exports=s})();