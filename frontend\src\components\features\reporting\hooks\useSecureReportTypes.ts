/**
 * @file useSecureReportTypes - Enhanced report types hook with comprehensive security
 * @description Migrated to use new Phase 3 architecture with useSecureApiClient
 *
 * Phase 4 Migration: Updated to use new secure API architecture
 * - Uses useSecureApiClient instead of legacy useSecureApiClient
 * - Maintains all existing functionality
 * - Adds enhanced security features and monitoring
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSecureApiClient } from '@/lib/api/security';
// Migration utilities removed - no longer needed
import type { ReportType } from '../data/types/reporting';

/**
 * Enhanced hook for managing report types with comprehensive security
 *
 * Features:
 * - CSRF Protection for all mutations
 * - Input Sanitization for all data
 * - Token Validation & Refresh
 * - Automatic Logout on auth failures
 * - All the reliability of ApiClient
 */
export const useSecureReportTypes = () => {
  const queryClient = useQueryClient();
  const {
    client: secureApiClient,
    isAuthenticated,
    hasValidToken,
    securityStatus,
  } = useSecureApiClient({
    // Enable all security features
    enableCSRF: true,
    enableInputSanitization: true,
    enableTokenValidation: true,
    enableAutoLogout: true,
  });

  // Migration completed - using secure API architecture

  // Query for fetching all report types with security
  const reportTypesQuery = useQuery({
    queryKey: ['secure-report-types'],
    queryFn: async (): Promise<ReportType[]> => {
      const result = await secureApiClient.get('/reporting/report-types');
      return result.data?.data || [];
    },
    enabled: isAuthenticated && hasValidToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry authentication errors
      if (error?.status === 401) return false;
      return failureCount < 3;
    },
  });

  // Mutation for creating a new report type with security
  const createReportType = useMutation({
    mutationFn: async (
      reportTypeData: Partial<ReportType>
    ): Promise<ReportType> => {
      // SecureApiClient will automatically:
      // 1. Sanitize the input data
      // 2. Add CSRF protection
      // 3. Validate/refresh token
      // 4. Handle auth errors
      const result = await secureApiClient.post(
        '/reporting/report-types',
        reportTypeData
      );
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['secure-report-types'] });
    },
    onError: (error: any) => {
      console.error('Failed to create report type:', error);
      // SecureApiClient already handled auto-logout if needed
    },
  });

  // Mutation for updating an existing report type with security
  const updateReportType = useMutation({
    mutationFn: async ({
      id,
      ...reportTypeData
    }: Partial<ReportType> & { id: string }): Promise<ReportType> => {
      const result = await secureApiClient.put(
        `/reporting/report-types/${id}`,
        reportTypeData
      );
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['secure-report-types'] });
    },
  });

  // Mutation for deleting a report type with security
  const deleteReportType = useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await secureApiClient.delete(`/reporting/report-types/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['secure-report-types'] });
    },
  });

  // Mutation for duplicating a report type with security
  const duplicateReportType = useMutation({
    mutationFn: async (id: string): Promise<ReportType> => {
      const result = await secureApiClient.post(
        `/reporting/report-types/${id}/duplicate`
      );
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['secure-report-types'] });
    },
  });

  // Mutation for toggling report type active status with security
  const toggleReportTypeActive = useMutation({
    mutationFn: async ({
      id,
      isActive,
    }: {
      id: string;
      isActive: boolean;
    }): Promise<ReportType> => {
      const result = await secureApiClient.patch(
        `/reporting/report-types/${id}/toggle-active`,
        { isActive }
      );
      return result.data?.data || result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['secure-report-types'] });
    },
  });

  return {
    // Query data
    data: reportTypesQuery.data,
    isLoading: reportTypesQuery.isLoading,
    error: reportTypesQuery.error,

    // Mutations
    createReportType,
    updateReportType,
    deleteReportType,
    duplicateReportType,
    toggleReportTypeActive,

    // Security status
    securityStatus,
    isAuthenticated,
    hasValidToken,

    // Utility functions
    refetch: reportTypesQuery.refetch,
  };
};

/**
 * Hook for fetching a single report type by ID with security
 */
export const useSecureReportType = (id: string) => {
  const {
    client: secureApiClient,
    isAuthenticated,
    hasValidToken,
  } = useSecureApiClient();

  return useQuery({
    queryKey: ['secure-report-type', id],
    queryFn: async (): Promise<ReportType> => {
      const result = await secureApiClient.get(`/reporting/report-types/${id}`);
      return result.data?.data || result.data;
    },
    enabled: !!id && isAuthenticated && hasValidToken,
    staleTime: 5 * 60 * 1000,
    retry: (failureCount, error: any) => {
      if (error?.status === 401) return false;
      return failureCount < 3;
    },
  });
};

/**
 * Hook for fetching report types by category with security
 */
export const useSecureReportTypesByCategory = (category: string) => {
  const {
    client: secureApiClient,
    isAuthenticated,
    hasValidToken,
  } = useSecureApiClient();

  return useQuery({
    queryKey: ['secure-report-types', 'category', category],
    queryFn: async (): Promise<ReportType[]> => {
      const result = await secureApiClient.get(
        `/reporting/report-types?category=${encodeURIComponent(category)}`
      );
      return result.data?.data || [];
    },
    enabled: !!category && isAuthenticated && hasValidToken,
    staleTime: 5 * 60 * 1000,
    retry: (failureCount, error: any) => {
      if (error?.status === 401) return false;
      return failureCount < 3;
    },
  });
};
