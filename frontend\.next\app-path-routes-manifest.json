{"/api/csp-report/route": "/api/csp-report", "/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/add-vehicle/page": "/add-vehicle", "/delegations/add/page": "/delegations/add", "/auth-test/page": "/auth-test", "/delegations/[id]/page": "/delegations/[id]", "/employees/[id]/edit/page": "/employees/[id]/edit", "/delegations/page": "/delegations", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/employees/[id]/page": "/employees/[id]", "/employees/new/page": "/employees/new", "/employees/add/page": "/employees/add", "/font-size-demo/page": "/font-size-demo", "/login/page": "/login", "/page": "/", "/profile/page": "/profile", "/employees/page": "/employees", "/service-history/page": "/service-history", "/settings/page": "/settings", "/service-records/[id]/edit/page": "/service-records/[id]/edit", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/tasks/add/page": "/tasks/add", "/tasks/page": "/tasks", "/tasks/[id]/page": "/tasks/[id]", "/service-records/[id]/page": "/service-records/[id]", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/vehicles/new/page": "/vehicles/new", "/vehicles/[id]/page": "/vehicles/[id]", "/zustand-test/page": "/zustand-test", "/vehicles/page": "/vehicles", "/admin/page": "/admin", "/delegations/report/list/page": "/delegations/report/list", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/tasks/report/page": "/tasks/report", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/delegations/[id]/report/page": "/delegations/[id]/report", "/reports/analytics/page": "/reports/analytics", "/reports/data/page": "/reports/data", "/reliability/page": "/reliability", "/reports/page": "/reports"}