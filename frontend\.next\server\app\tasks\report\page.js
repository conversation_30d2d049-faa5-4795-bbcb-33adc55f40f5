(()=>{var e={};e.id=5034,e.ids=[5034],e.modules={997:(e,t,s)=>{"use strict";s.d(t,{k:()=>f});var r=s(60687),i=s(28946),a=s(11516),n=s(20620),l=s(36644);let o=(0,s(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var c=s(43210),d=s(68752),u=s(21342),h=s(3940),p=s(22482),m=s(22364);function f({className:e,csvData:t,enableCsv:s=!1,entityId:f,fileName:x,reportContentId:b,reportType:g,tableId:y}){let[v,j]=(0,c.useState)(!1),[N,k]=(0,c.useState)(!1),{showFormSuccess:w,showFormError:_}=(0,h.t6)(),S=async()=>{j(!0);try{let e=`/api/reports/${g}${f?`/${f}`:""}`,t=document.createElement("a");t.href=e,t.download=`${x}.pdf`,t.target="_blank",document.body.append(t),t.click(),t.remove(),w({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),_(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{j(!1)}},C=async()=>{if(s){k(!0);try{if(t?.data&&t.headers)(0,m.og)(t.data,t.headers,`${x}.csv`);else if(y){let e=(0,m.tL)(y);(0,m.og)(e.data,e.headers,`${x}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");w({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),_(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{k(!1)}}},R=v||N;return(0,r.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 no-print",e),children:[(0,r.jsx)(d.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,r.jsx)(i.A,{className:"size-4"})}),(0,r.jsxs)(u.rI,{children:[(0,r.jsx)(u.ty,{asChild:!0,children:(0,r.jsx)(d.r,{actionType:"secondary","aria-label":"Download report",disabled:R,size:"icon",title:"Download Report",children:R?(0,r.jsx)(a.A,{className:"size-4 animate-spin"}):(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsxs)(u.SQ,{align:"end",children:[(0,r.jsxs)(u._2,{disabled:v,onClick:S,children:[v?(0,r.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(l.A,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download PDF"})]}),s&&(0,r.jsxs)(u._2,{disabled:N,onClick:C,children:[N?(0,r.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(o,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5068:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14583:(e,t,s)=>{"use strict";s.d(t,{$o:()=>b,Eb:()=>m,Iu:()=>u,WA:()=>f,cU:()=>h,dK:()=>d,n$:()=>p});var r=s(60687),i=s(43967),a=s(74158),n=s(69795),l=s(43210),o=s(29523),c=s(22482);let d=l.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,c.cn)("flex justify-center",e),ref:s,...t}));d.displayName="Pagination";let u=l.forwardRef(({className:e,...t},s)=>(0,r.jsx)("ul",{className:(0,c.cn)("flex flex-row items-center gap-1",e),ref:s,...t}));u.displayName="PaginationContent";let h=l.forwardRef(({className:e,...t},s)=>(0,r.jsx)("li",{className:(0,c.cn)("",e),ref:s,...t}));h.displayName="PaginationItem";let p=l.forwardRef(({className:e,isActive:t,...s},i)=>(0,r.jsx)(o.$,{"aria-current":t?"page":void 0,className:(0,c.cn)("h-9 w-9",e),ref:i,size:"icon",variant:t?"outline":"ghost",...s}));p.displayName="PaginationLink";let m=l.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(o.$,{className:(0,c.cn)("h-9 w-9 gap-1",e),ref:s,size:"icon",variant:"ghost",...t,children:[(0,r.jsx)(i.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Previous page"})]}));m.displayName="PaginationPrevious";let f=l.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(o.$,{className:(0,c.cn)("h-9 w-9 gap-1",e),ref:s,size:"icon",variant:"ghost",...t,children:[(0,r.jsx)(a.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Next page"})]}));f.displayName="PaginationNext";let x=l.forwardRef(({className:e,...t},s)=>(0,r.jsxs)("span",{"aria-hidden":!0,className:(0,c.cn)("flex h-9 w-9 items-center justify-center",e),ref:s,...t,children:[(0,r.jsx)(n.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"More pages"})]}));function b({className:e,currentPage:t,onPageChange:s,totalPages:i}){let a=(()=>{let e=[];e.push(1);let s=Math.max(2,t-1),r=Math.min(i-1,t+1);s>2&&e.push("ellipsis1");for(let t=s;t<=r;t++)e.push(t);return r<i-1&&e.push("ellipsis2"),i>1&&e.push(i),e})();return i<=1?null:(0,r.jsx)(d,{className:e,children:(0,r.jsxs)(u,{children:[(0,r.jsx)(h,{children:(0,r.jsx)(m,{"aria-disabled":1===t?"true":void 0,"aria-label":"Go to previous page",disabled:1===t,onClick:()=>s(t-1)})}),a.map((e,i)=>"ellipsis1"===e||"ellipsis2"===e?(0,r.jsx)(h,{children:(0,r.jsx)(x,{})},`ellipsis-${i}`):(0,r.jsx)(h,{children:(0,r.jsx)(p,{"aria-label":`Go to page ${e}`,isActive:t===e,onClick:()=>s(e),children:e})},`page-${e}`)),(0,r.jsx)(h,{children:(0,r.jsx)(f,{"aria-disabled":t===i?"true":void 0,"aria-label":"Go to next page",disabled:t===i,onClick:()=>s(t+1)})})]})})}x.displayName="PaginationEllipsis"},15795:(e,t,s)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function i(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let t=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(t.toLowerCase())||t.includes("_")){let e=t.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return t}if(e.role){let t=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${t} (Role)`}return"Unknown Employee"}function a(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function n(e){return e.replaceAll("_"," ")}s.d(t,{DV:()=>i,fZ:()=>r,s:()=>a,vq:()=>n})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28946:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33086:(e,t,s)=>{"use strict";s.d(t,{zc:()=>d});var r=s(60687),i=s(97025),a=s(14975),n=s(99196);s(43210);var l=s(91821),o=s(3940),c=s(48184);function d({className:e="",context:t,error:s,showToast:d=!1}){var u;let{showFormError:h}=(0,o.t6)();if(!s)return null;let p=(0,c.iG)(s,t),m=(u=p.code||"UNKNOWN_ERROR",["VALIDATION_ERROR","ASSIGNMENT_ERROR","ROLE_ERROR"].some(e=>u.includes(e))?"error":["BUSINESS_RULE_WARNING","DEPRECATION_WARNING"].some(e=>u.includes(e))?"warning":"info");return(0,r.jsxs)(l.Fc,{className:`${e} ${function(e){switch(e){case"error":return"border-destructive/50 text-destructive dark:border-destructive";case"warning":return"border-yellow-500/50 text-yellow-600 dark:border-yellow-500 dark:text-yellow-400";default:return"border-blue-500/50 text-blue-600 dark:border-blue-500 dark:text-blue-400"}}(m)}`,children:[function(e){switch(e){case"error":return(0,r.jsx)(i.A,{className:"size-4"});case"warning":return(0,r.jsx)(a.A,{className:"size-4"});default:return(0,r.jsx)(n.A,{className:"size-4"})}}(m),(0,r.jsx)(l.XL,{children:p.title}),(0,r.jsxs)(l.TN,{children:[p.message,p.code&&(0,r.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Error Code: ",p.code]}),p.field&&(0,r.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Field: ",p.field]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43705:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a,metadata:()=>i});var r=s(37413);let i={title:"Task Report"};function a({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},47235:(e,t,s)=>{Promise.resolve().then(s.bind(s,83662))},52440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(60687),i=s(76180),a=s.n(i),n=s(80489),l=s(14975),o=s(77368),c=s(41936),d=s(78726),u=s(16189),h=s(43210),p=s(99196),m=s(25841),f=s(44493),x=s(22482);function b({className:e,tasks:t}){let s=t.length,i=t.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{}),a=t.reduce((e,t)=>(e[t.priority]=(e[t.priority]||0)+1,e),{}),n=t.filter(e=>e.deadline&&(0,m.R)(new Date(e.deadline))&&"Completed"!==e.status&&"Cancelled"!==e.status).length,l=t.filter(e=>!e.staffEmployeeId&&"Completed"!==e.status&&"Cancelled"!==e.status).length,o={Assigned:"bg-blue-100 text-blue-800",Cancelled:"bg-gray-100 text-gray-800",Completed:"bg-green-100 text-green-800",In_Progress:"bg-purple-100 text-purple-800",Pending:"bg-yellow-100 text-yellow-800"},c={High:"bg-red-100 text-red-800",Low:"bg-blue-100 text-blue-800",Medium:"bg-orange-100 text-orange-800"};return(0,r.jsxs)("div",{className:(0,x.cn)("mt-4 space-y-4",e),children:[(0,r.jsxs)("div",{className:"summary-grid grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-5",children:[(0,r.jsx)(g,{className:"bg-gray-50",label:"Total Tasks",textColor:"text-gray-500",value:s}),(0,r.jsx)(g,{className:(0,x.cn)("bg-red-50",n>0?"border-red-200":""),label:"Overdue Tasks",textColor:"text-red-500",value:n}),(0,r.jsx)(g,{className:(0,x.cn)("bg-amber-50",l>0?"border-amber-200":""),label:"Unassigned Tasks",textColor:"text-amber-500",value:l}),(0,r.jsx)(g,{className:"bg-purple-50",label:"In Progress",textColor:"text-purple-500",value:i.In_Progress||0}),(0,r.jsx)(g,{className:"bg-green-50",label:"Completed",textColor:"text-green-500",value:i.Completed||0})]}),(0,r.jsx)(f.Zp,{className:"overflow-hidden",children:(0,r.jsxs)(f.Wu,{className:"p-4",children:[(0,r.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"Status Distribution"}),(0,r.jsx)("div",{className:"space-y-2",children:["Pending","Assigned","In_Progress","Completed","Cancelled"].map(e=>(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{children:e}),(0,r.jsxs)("span",{children:[i[e]||0," tasks (",s?Math.round((i[e]||0)/s*100):0,"%)"]})]}),(0,r.jsx)("div",{className:"h-2 w-full rounded-full bg-gray-200",children:(0,r.jsx)("div",{"aria-label":`${e}: ${i[e]||0} tasks`,className:(0,x.cn)("h-2 rounded-full",o[e]),style:{width:`${s?(i[e]||0)/s*100:0}%`}})})]},e))})]})}),(0,r.jsx)(f.Zp,{className:"overflow-hidden",children:(0,r.jsxs)(f.Wu,{className:"p-4",children:[(0,r.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"Priority Distribution"}),(0,r.jsx)("div",{className:"mt-1 flex flex-wrap gap-2",children:["High","Medium","Low"].map(e=>(0,r.jsxs)("div",{className:(0,x.cn)("text-xs px-3 py-1 rounded-full",c[e]),children:[e,": ",a[e]||0]},e))})]})})]})}function g({className:e,colSpan:t,label:s,textColor:i="text-gray-500",value:a}){return(0,r.jsx)(f.Zp,{className:(0,x.cn)("overflow-hidden",e,t),children:(0,r.jsxs)(f.Wu,{className:"p-2 text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-semibold",children:a}),(0,r.jsx)("p",{className:(0,x.cn)("text-xs",i),children:s})]})})}var y=s(91821),v=s(29523),j=s(33086),N=s(52027),k=s(14583),w=s(15209);function _({className:e,error:t,isLoading:s,onRetry:i,tasks:a}){let[n,l]=(0,h.useState)("dateTime"),[c,d]=(0,h.useState)("desc"),[u,m]=(0,h.useState)(1),[x]=(0,h.useState)(10),g={Assigned:2,Cancelled:5,Completed:4,In_Progress:1,Pending:3},_={High:1,Low:3,Medium:2},S=(0,h.useMemo)(()=>[...a].sort((e,t)=>{let s,r;switch(n){case"dateTime":case"deadline":s=e[n]?new Date(e[n]).getTime():0,r=t[n]?new Date(t[n]).getTime():0;break;case"description":case"location":s=e[n]?.toLowerCase()||"",r=t[n]?.toLowerCase()||"";break;case"priority":s=_[e.priority]||999,r=_[t.priority]||999;break;case"staffEmployeeId":s=e.staffEmployeeId||999,r=t.staffEmployeeId||999;break;case"status":s=g[e.status]||999,r=g[t.status]||999;break;default:s=e[n],r=t[n]}let i=s??"",a=r??"";return i<a?"asc"===c?-1:1:i>a?"asc"===c?1:-1:0}),[a,n,c,g,_]);(0,h.useCallback)(e=>{n===e?d("asc"===c?"desc":"asc"):(l(e),d("asc"))},[n,c]);let C=u*x,R=C-x,A=(0,h.useMemo)(()=>S.slice(R,C),[S,R,C]),P=(0,h.useMemo)(()=>Math.ceil(S.length/x),[S.length,x]),E=(0,h.useCallback)(e=>{m(e)},[]);return s?(0,r.jsx)("div",{className:"space-y-4","data-testid":"loading-skeleton",children:(0,r.jsx)(N.jt,{count:5,variant:"table"})}):t?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(j.zc,{context:"Loading Tasks",error:t}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)(v.$,{"aria-label":"Try loading tasks again",onClick:i,size:"sm",variant:"outline",children:[(0,r.jsx)(o.A,{className:"mr-2 size-4"}),"Try Again"]})})]}):0===a.length?(0,r.jsxs)(y.Fc,{className:"mb-6",variant:"default",children:[(0,r.jsx)(p.A,{className:"size-4"}),(0,r.jsx)(y.XL,{children:"No Tasks"}),(0,r.jsx)(y.TN,{children:"No tasks match your current filters. Try adjusting your search or filter criteria."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(b,{tasks:S}),(0,r.jsx)(f.Zp,{className:"card-print shadow-md",children:(0,r.jsx)(f.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)(w.z,{tasks:A})})})}),S.length>x&&(0,r.jsx)("div",{className:"no-print mt-4 flex justify-center",children:(0,r.jsx)(k.$o,{currentPage:u,onPageChange:E,totalPages:P})}),(0,r.jsxs)("div",{className:"no-print text-center text-sm text-muted-foreground",children:["Showing ",R+1," to"," ",Math.min(C,S.length)," of ",S.length," ","tasks"]})]})}var S=s(997),C=s(89667),R=s(80013),A=s(48041),P=s(15079),E=s(19599),M=s(73227);function z(){return(0,r.jsx)(h.Suspense,{fallback:(0,r.jsx)("div",{className:"py-10 text-center",children:"Loading report..."}),children:(0,r.jsx)(O,{})})}function O(){(0,u.useSearchParams)();let{data:e,error:t,isLoading:s,refetch:i}=(0,M.si)(),{data:p,error:m,isLoading:x,refetch:b}=(0,E.nR)(),g=(0,h.useMemo)(()=>e||[],[e]),j=(0,h.useMemo)(()=>p||[],[p]),[k,w]=(0,h.useState)(""),[z,O]=(0,h.useState)(""),[F,T]=(0,h.useState)("all"),[q,I]=(0,h.useState)("all"),[L,$]=(0,h.useState)("all"),D=(0,h.useMemo)(()=>{let e=[...g],t=z.toLowerCase();return"all"!==F&&(e=e.filter(e=>e.status===F)),"all"!==q&&(e=e.filter(e=>e.priority===q)),"all"!==L&&("unassigned"===L||(e=e.filter(e=>e.staffEmployeeId?.toString()===L))),t&&(e=e.filter(e=>{let s=e.staffEmployeeId?j.find(t=>t.id===e.staffEmployeeId):e.driverEmployeeId?j.find(t=>t.id===e.driverEmployeeId):null,r=s?s.fullName||s.name||"Unknown Employee".trim().toLowerCase():"";return e.description&&e.description.toLowerCase().includes(t)||r.includes(t)||e.status.toLowerCase().includes(t)||e.priority.toLowerCase().includes(t)})),e},[g,z,F,q,L,j]),U=(0,h.useCallback)(()=>{i(),b()},[i,b]);return s||x?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(A.z,{description:"Loading task data...",icon:n.A,title:"Tasks Report"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(N.jt,{count:1,variant:"card"}),(0,r.jsx)(N.jt,{className:"mt-6",count:5,variant:"table"})]})]}):t||m?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(A.z,{description:"Error loading task data.",icon:n.A,title:"Tasks Report"}),(0,r.jsxs)(y.Fc,{className:"mb-6",variant:"destructive",children:[(0,r.jsx)(l.A,{className:"size-4"}),(0,r.jsx)(y.XL,{children:"Error Loading Data"}),(0,r.jsx)(y.TN,{children:t?.message||m?.message||"An unknown error occurred."}),(0,r.jsxs)(v.$,{className:"mt-2",onClick:U,size:"sm",variant:"outline",children:[(0,r.jsx)(o.A,{className:"mr-2 size-4"}),"Try Again"]})]})]}):(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 space-y-6",children:[(0,r.jsx)(A.z,{description:"View and manage all tasks and assignments.",icon:n.A,title:"Tasks Report",children:(0,r.jsx)("div",{className:"jsx-d0a4b0b7e2af8440 no-print flex items-center gap-2",children:(0,r.jsx)(S.k,{enableCsv:D.length>0,fileName:`tasks-report-${new Date().toISOString().split("T")[0]}`,reportContentId:"#task-report-content",reportType:"task",tableId:"#tasks-table"})})}),(0,r.jsx)(f.Zp,{className:"no-print shadow-md",children:(0,r.jsxs)(f.Wu,{className:"pt-6",children:[(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 filter-grid mb-6 grid grid-cols-1 items-end gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440",children:[(0,r.jsx)(R.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"status-filter",children:"Filter by Status"}),(0,r.jsxs)(P.l6,{"aria-label":"Filter by status",onValueChange:T,value:F,children:[(0,r.jsx)(P.bq,{className:"w-full",id:"status-filter",children:(0,r.jsx)(P.yv,{placeholder:"All Statuses"})}),(0,r.jsxs)(P.gC,{children:[(0,r.jsx)(P.eb,{value:"all",children:"All Statuses"}),(0,r.jsx)(P.eb,{value:"Pending",children:"Pending"}),(0,r.jsx)(P.eb,{value:"Assigned",children:"Assigned"}),(0,r.jsx)(P.eb,{value:"In_Progress",children:"In Progress"}),(0,r.jsx)(P.eb,{value:"Completed",children:"Completed"}),(0,r.jsx)(P.eb,{value:"Cancelled",children:"Cancelled"})]})]})]}),(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440",children:[(0,r.jsx)(R.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"priority-filter",children:"Filter by Priority"}),(0,r.jsxs)(P.l6,{"aria-label":"Filter by priority",onValueChange:I,value:q,children:[(0,r.jsx)(P.bq,{className:"w-full",id:"priority-filter",children:(0,r.jsx)(P.yv,{placeholder:"All Priorities"})}),(0,r.jsxs)(P.gC,{children:[(0,r.jsx)(P.eb,{value:"all",children:"All Priorities"}),(0,r.jsx)(P.eb,{value:"High",children:"High"}),(0,r.jsx)(P.eb,{value:"Medium",children:"Medium"}),(0,r.jsx)(P.eb,{value:"Low",children:"Low"})]})]})]}),(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440",children:[(0,r.jsx)(R.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"employee-filter",children:"Filter by Assignee"}),(0,r.jsxs)(P.l6,{"aria-label":"Filter by assignee",onValueChange:$,value:L,children:[(0,r.jsx)(P.bq,{className:"w-full",id:"employee-filter",children:(0,r.jsx)(P.yv,{placeholder:"All Assignees"})}),(0,r.jsxs)(P.gC,{children:[(0,r.jsx)(P.eb,{value:"all",children:"All Assignees"}),(0,r.jsx)(P.eb,{value:"unassigned",children:"Unassigned"}),Array.isArray(j)&&j.map(e=>(0,r.jsxs)(P.eb,{value:e.id.toString(),children:[" ",e.fullName||e.name||`Employee ${e.id}`]},e.id))]})]})]}),(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 relative",children:[(0,r.jsx)(R.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"search-tasks",children:"Search Tasks"}),(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 relative",children:[(0,r.jsx)(C.p,{"aria-label":"Search tasks",className:"px-10",id:"search-tasks",onChange:e=>w(e.target.value),placeholder:"Search by description, location, notes...",type:"text",value:k}),(0,r.jsx)(c.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground"}),k&&(0,r.jsxs)(v.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>w(""),size:"icon",variant:"ghost",children:[(0,r.jsx)(d.A,{className:"size-4"}),(0,r.jsx)("span",{className:"jsx-d0a4b0b7e2af8440 sr-only",children:"Clear search"})]})]})]})]}),("all"!==F||"all"!==q||"all"!==L||k)&&(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 mt-4 flex items-center justify-between rounded bg-gray-50 p-2",children:[(0,r.jsxs)("div",{className:"jsx-d0a4b0b7e2af8440 text-sm",children:[(0,r.jsx)("span",{className:"jsx-d0a4b0b7e2af8440 font-medium",children:"Active Filters:"}),"all"!==F&&(0,r.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:["Status: ",F.replace("_"," ")]}),"all"!==q&&(0,r.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:["Priority: ",q]}),"all"!==L&&(0,r.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:["Assignee:"," ","unassigned"===L?"Unassigned":(j.find(e=>e.id.toString()===L)?j.find(e=>e.id.toString()===L)?.fullName||j.find(e=>e.id.toString()===L)?.name||"Unknown Employee":"")||"Unknown"]}),k&&(0,r.jsxs)("span",{className:"jsx-d0a4b0b7e2af8440 ml-2",children:['Search: "',k,'"']})]}),(0,r.jsx)(v.$,{"aria-label":"Reset all filters",onClick:()=>{T("all"),I("all"),$("all"),w("")},size:"sm",variant:"ghost",children:"Reset Filters"})]})]})}),(0,r.jsxs)("div",{id:"task-report-content",className:"jsx-d0a4b0b7e2af8440 report-content",children:[(0,r.jsxs)("header",{className:"jsx-d0a4b0b7e2af8440 print-only mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,r.jsx)("h1",{className:"jsx-d0a4b0b7e2af8440 text-3xl font-bold text-gray-800",children:"Tasks Report"}),(0,r.jsxs)("p",{className:"jsx-d0a4b0b7e2af8440 text-md text-gray-600",children:["all"!==F&&`Status: ${F.replace("_"," ")}`,"all"!==q&&("all"===F?"":" | ")+`Priority: ${q}`,"all"!==L&&("all"!==F||"all"!==q?" | ":"")+`Assignee: ${"unassigned"===L?"Unassigned":(Array.isArray(j)&&j.find(e=>e.id.toString()===L)?j.find(e=>e.id.toString()===L)?.fullName||j.find(e=>e.id.toString()===L)?.name||"Unknown Employee":"")||"Employee "+L}`]})]}),(0,r.jsx)(_,{error:t,isLoading:s,onRetry:i,tasks:D}),(0,r.jsxs)("footer",{className:"jsx-d0a4b0b7e2af8440 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,r.jsxs)("p",{className:"jsx-d0a4b0b7e2af8440",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,r.jsx)("p",{className:"jsx-d0a4b0b7e2af8440",children:"WorkHub - Task Management"})]})]}),(0,r.jsx)(a(),{id:"d0a4b0b7e2af8440",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-description,.print-location,.print-service-col,.print-notes-col{max-width:200px;white-space:normal!important;word-break:break-word}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.overflow-x-auto{overflow-x:auto}.filter-grid{grid-template-columns:1fr!important}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})}},54050:(e,t,s)=>{"use strict";s.d(t,{n:()=>d});var r=s(43210),i=s(65406),a=s(33465),n=s(35536),l=s(31212),o=class extends n.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,l.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,l.EN)(t.mutationKey)!==(0,l.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#a(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#a()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#i(){let e=this.#s?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){a.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},c=s(8693);function d(e,t){let s=(0,c.jE)(t),[i]=r.useState(()=>new o(s,e));r.useEffect(()=>{i.setOptions(e)},[i,e]);let n=r.useSyncExternalStore(r.useCallback(e=>i.subscribe(a.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=r.useCallback((e,t)=>{i.mutate(e,t).catch(l.lQ)},[i]);if(n.error&&(0,l.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},54608:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},61307:(e,t,s)=>{Promise.resolve().then(s.bind(s,52440))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65456:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},72322:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},72975:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,s)=>{"use strict";s(56397);var r=s(43210),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(r),a="undefined"!=typeof process&&process.env&&!0,n=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,i=t.optimizeForSpeed,l=void 0===i?a:i;o(n(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",o("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){o("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),o(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;o(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){return o(n(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},s.replaceRule=function(e,t){this._optimizeForSpeed;var s=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){a||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}return e},s.deleteRule=function(e){this._serverSheet.deleteRule(e)},s.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},s.cssRules=function(){return this._serverSheet.cssRules},s.makeStyleTag=function(e,t,s){t&&o(n(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return s?i.insertBefore(r,s):i.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function o(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},d={};function u(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return d[r]||(d[r]="jsx-"+c(e+"-"+s)),d[r]}function h(e,t){var s=e+(t=t.replace(/\/style/gi,"\\/style"));return d[s]||(d[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[s]}var p=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,i=t.optimizeForSpeed,a=void 0!==i&&i;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:a}),this._sheet.inject(),r&&"boolean"==typeof a&&(this._sheet.setOptimizeForSpeed(a),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var s=this.getIdAndRules(e),r=s.styleId,i=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var a=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=a,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var i=u(r,s);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return h(i,e)}):[h(i,t)]}}return{styleId:u(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=r.createContext(null);m.displayName="StyleSheetContext";i.default.useInsertionEffect||i.default.useLayoutEffect;var f=void 0;function x(e){var t=f||r.useContext(m);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=x},76180:(e,t,s)=>{"use strict";e.exports=s(75913).style},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82706:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(65239),i=s(48088),a=s(88170),n=s.n(a),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["tasks",{children:["report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83662)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\report\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,43705)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\report\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\report\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/tasks/report/page",pathname:"/tasks/report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83662:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\tasks\\\\report\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\report\\page.tsx","default")},83997:e=>{"use strict";e.exports=require("tty")},90357:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},91645:e=>{"use strict";e.exports=require("net")},93425:(e,t,s)=>{"use strict";s.d(t,{E:()=>f});var r=s(43210),i=s(33465),a=s(5563),n=s(35536),l=s(31212);function o(e,t){let s=new Set(t);return e.filter(e=>!s.has(e))}var c=class extends n.Q{#e;#n;#l;#o;#c;#d;#u;#h;#p=[];constructor(e,t,s){super(),this.#e=e,this.#o=s,this.#l=[],this.#c=[],this.#n=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#c.forEach(e=>{e.subscribe(t=>{this.#m(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#c.forEach(e=>{e.destroy()})}setQueries(e,t){this.#l=e,this.#o=t,i.jG.batch(()=>{let e=this.#c,t=this.#f(this.#l);this.#p=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let s=t.map(e=>e.observer),r=s.map(e=>e.getCurrentResult()),i=s.some((t,s)=>t!==e[s]);(e.length!==s.length||i)&&(this.#c=s,this.#n=r,this.hasListeners()&&(o(e,s).forEach(e=>{e.destroy()}),o(s,e).forEach(e=>{e.subscribe(t=>{this.#m(e,t)})}),this.#a()))})}getCurrentResult(){return this.#n}getQueries(){return this.#c.map(e=>e.getCurrentQuery())}getObservers(){return this.#c}getOptimisticResult(e,t){let s=this.#f(e),r=s.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[r,e=>this.#x(e??r,t),()=>this.#b(r,s)]}#b(e,t){return t.map((s,r)=>{let i=e[r];return s.defaultedQueryOptions.notifyOnChangeProps?i:s.observer.trackResult(i,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#x(e,t){return t?(this.#d&&this.#n===this.#h&&t===this.#u||(this.#u=t,this.#h=this.#n,this.#d=(0,l.BH)(this.#d,t(e))),this.#d):e}#f(e){let t=new Map(this.#c.map(e=>[e.options.queryHash,e])),s=[];return e.forEach(e=>{let r=this.#e.defaultQueryOptions(e),i=t.get(r.queryHash);i?s.push({defaultedQueryOptions:r,observer:i}):s.push({defaultedQueryOptions:r,observer:new a.$(this.#e,r)})}),s}#m(e,t){let s=this.#c.indexOf(e);-1!==s&&(this.#n=function(e,t,s){let r=e.slice(0);return r[t]=s,r}(this.#n,s,t),this.#a())}#a(){if(this.hasListeners()){let e=this.#d,t=this.#b(this.#n,this.#p);e!==this.#x(t,this.#o?.combine)&&i.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#n)})})}}},d=s(8693),u=s(24903),h=s(18228),p=s(16142),m=s(76935);function f({queries:e,...t},s){let n=(0,d.jE)(s),o=(0,u.w)(),f=(0,h.h)(),x=r.useMemo(()=>e.map(e=>{let t=n.defaultQueryOptions(e);return t._optimisticResults=o?"isRestoring":"optimistic",t}),[e,n,o]);x.forEach(e=>{(0,m.jv)(e),(0,p.LJ)(e,f)}),(0,p.wZ)(f);let[b]=r.useState(()=>new c(n,x,t)),[g,y,v]=b.getOptimisticResult(x,t.combine),j=!o&&!1!==t.subscribed;r.useSyncExternalStore(r.useCallback(e=>j?b.subscribe(i.jG.batchCalls(e)):l.lQ,[b,j]),()=>b.getCurrentResult(),()=>b.getCurrentResult()),r.useEffect(()=>{b.setQueries(x,t)},[x,t,b]);let N=g.some((e,t)=>(0,m.EU)(x[t],e))?g.flatMap((e,t)=>{let s=x[t];if(s){let t=new a.$(n,s);if((0,m.EU)(s,e))return(0,m.iL)(s,t,f);(0,m.nE)(e,o)&&(0,m.iL)(s,t,f)}return[]}):[];if(N.length>0)throw Promise.all(N);let k=g.find((e,t)=>{let s=x[t];return s&&(0,p.$1)({result:e,errorResetBoundary:f,throwOnError:s.throwOnError,query:n.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(k?.error)throw k.error;return y(v())}},94735:e=>{"use strict";e.exports=require("events")},95688:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,211,1658,8390,2670,4897,101,5825,9599,5782,5009,9637,4867],()=>s(82706));module.exports=r})();